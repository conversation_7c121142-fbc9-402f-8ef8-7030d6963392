<template>
  <div class="dashboard-view p-4 bg-slate-800 min-h-screen">
    <!-- Header Section -->
    <header class="mb-6">
      <div class="flex justify-between items-center">
        <h1 class="text-3xl font-bold text-white">动态仪表盘</h1>
        <div class="flex gap-2">
          <button 
            @click="resetLayout" 
            class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition"
          >
            重置布局
          </button>
          <button 
            @click="saveLayout" 
            class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition"
          >
            保存布局
          </button>
        </div>
      </div>
      <p class="text-slate-300 mt-2">拖拽和调整组件大小来自定义您的仪表盘布局</p>
    </header>

    <!-- GridStack Container -->
    <div class="grid-stack-container">
      <div ref="gridStackRef" class="grid-stack"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'

import { useLayoutStore, type LayoutItem } from '@/stores/layout'

// 导入所有可能用到的卡片组件
import BasicInfoCard from '@/components/cards/BasicInfoCard.vue'
import SalesChartCard from '@/components/cards/SalesChartCard.vue'
import KeyMetricCard from '@/components/cards/KeyMetricCard.vue'

const layoutStore = useLayoutStore()
const gridStackRef = ref<HTMLElement | null>(null)
let grid: GridStack | null = null

// 组件映射表：将字符串名字映射到真实的组件对象
const componentMap = {
  BasicInfoCard,
  SalesChartCard,
  KeyMetricCard
}

const getComponent = (componentName: string) => {
  return componentMap[componentName as keyof typeof componentMap] || null
}

// 初始化GridStack
const initGridStack = () => {
  console.log('Initializing GridStack...')
  console.log('gridStackRef.value:', gridStackRef.value)

  if (!gridStackRef.value) {
    console.error('GridStack container not found!')
    return
  }

  grid = GridStack.init({
    column: 12,
    cellHeight: 60,
    margin: 8,
    resizable: true,
    draggable: true
  }, gridStackRef.value)

  console.log('GridStack initialized:', grid)

  // 监听布局变化
  grid.on('change', (event, items) => {
    if (items && items.length > 0) {
      const newLayout: LayoutItem[] = items.map(item => {
        const existingItem = layoutStore.layout.find(l => l.id === item.id)
        return {
          id: item.id!,
          x: item.x!,
          y: item.y!,
          w: item.w!,
          h: item.h!,
          component: existingItem?.component || 'BasicInfoCard',
          props: existingItem?.props || {}
        }
      })
      layoutStore.updateLayout(newLayout)
    }
  })

  // 加载初始布局
  loadLayout()
}

// 加载布局
const loadLayout = () => {
  if (!grid) return

  // 清空grid
  grid.removeAll()

  // 添加布局项
  layoutStore.layout.forEach(item => {
    const element = document.createElement('div')
    element.classList.add('grid-stack-item')
    element.setAttribute('gs-id', item.id)
    element.setAttribute('gs-x', item.x.toString())
    element.setAttribute('gs-y', item.y.toString())
    element.setAttribute('gs-w', item.w.toString())
    element.setAttribute('gs-h', item.h.toString())

    const content = document.createElement('div')
    content.classList.add('grid-stack-item-content', 'bg-slate-700', 'rounded-lg', 'p-4', 'text-white')
    content.innerHTML = `
      <h3 class="text-lg font-semibold mb-2">${item.props.title || item.component}</h3>
      <p class="text-sm text-slate-300">组件: ${item.component}</p>
      <p class="text-xs text-slate-400 mt-2">拖拽我来移动位置</p>
    `

    element.appendChild(content)
    grid!.addWidget(element)
  })
}

// 重置布局
const resetLayout = () => {
  layoutStore.resetLayout()
  loadLayout()
}

// 保存布局（手动触发）
const saveLayout = () => {
  // 布局已经通过watch自动保存，这里只是给用户反馈
  console.log('布局已保存到localStorage')
  alert('布局已保存！')
}

onMounted(() => {
  console.log('DashboardView mounted')
  console.log('Layout store data:', layoutStore.layout)
  initGridStack()
})

onUnmounted(() => {
  // 销毁GridStack实例
  if (grid) {
    grid.destroy()
  }
})
</script>

<style scoped>
.dashboard-view {
  font-family: 'Inter', sans-serif;
}

.grid-stack-container {
  min-height: 600px;
}

/* GridStack样式覆盖 */
:deep(.grid-stack-item) {
  background: transparent;
}

:deep(.grid-stack-item-content) {
  background: #374151;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  cursor: move;
}

:deep(.grid-stack-item-content:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

:deep(.grid-stack-item.ui-draggable-dragging) {
  opacity: 0.8;
  transform: rotate(2deg);
}

:deep(.grid-stack-item .ui-resizable-handle) {
  background: #3B82F6;
  opacity: 0;
  transition: opacity 0.2s;
}

:deep(.grid-stack-item:hover .ui-resizable-handle) {
  opacity: 0.7;
}
</style>
