<template>
  <div class="flex h-screen text-white bg-black tech-background">
    <!-- 数据流动效果 -->
    <div class="data-stream"></div>
    <div class="data-stream"></div>
    <div class="data-stream"></div>
    <div class="data-stream"></div>
    <div class="data-stream"></div>

    <!-- 1. 引入组件工具箱 -->
    <WidgetToolbox />

    <main class="flex-1 overflow-auto flex flex-col">
      <!-- 2. Header 部分 -->
      <header class="p-4 flex justify-between items-center bg-black bg-opacity-20 backdrop-blur-sm border-b border-blue-500 border-opacity-30">
        <h1 class="text-2xl font-bold text-white">
          <span class="text-blue-400">物流大数据</span>监控中心
        </h1>
        <div class="flex gap-2">
          <button
            @click="clearLayout"
            class="bg-red-500 bg-opacity-80 text-white px-4 py-2 rounded hover:bg-red-600 transition backdrop-blur-sm border border-red-400 border-opacity-50"
          >
            清空布局
          </button>
          <button
            @click="resetLayout"
            class="px-4 py-2 bg-blue-600 bg-opacity-80 text-white rounded hover:bg-blue-700 transition backdrop-blur-sm border border-blue-400 border-opacity-50"
          >
            重置布局
          </button>
        </div>
      </header>

      <!-- 3. GridStack 容器 -->
      <div class="p-4 flex-1">
        <div
          ref="gridStackRef"
          class="grid-stack min-h-96"
          style="background: rgba(10, 22, 52, 0.1); border-radius: 8px; border: 1px solid rgba(0, 150, 255, 0.2);"
        >
          <!-- GridStack items will be dynamically added here -->
        </div>
      </div>
    </main>

    <!-- Configuration Modal -->
    <ConfigurationModal />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, createApp } from 'vue'
import { GridStack } from 'gridstack'
import 'gridstack/dist/gridstack.min.css'
import '@/assets/images/dashboard-bg.css'

import { useLayoutStore, type LayoutItem } from '@/stores/layout'
import WidgetToolbox from '@/components/WidgetToolbox.vue'
import ConfigurationModal from '@/components/ConfigurationModal.vue'

// 导入所有可能用到的卡片组件
import BasicInfoCard from '@/components/cards/BasicInfoCard.vue'
import SalesChartCard from '@/components/cards/SalesChartCard.vue'
import KeyMetricCard from '@/components/cards/KeyMetricCard.vue'
import MapCard from '@/components/cards/MapCard.vue'

const layoutStore = useLayoutStore()
const gridStackRef = ref<HTMLElement | null>(null)
let grid: GridStack | null = null
const componentInstances = new Map<string, any>()

// 组件映射表：将字符串名字映射到真实的组件对象
const componentMap = {
  BasicInfoCard,
  SalesChartCard,
  KeyMetricCard,
  MapCard
}

const getComponent = (componentName: string) => {
  return componentMap[componentName as keyof typeof componentMap] || null
}

// 创建GridStack项目的HTML结构
const createGridItem = (item: LayoutItem): HTMLElement => {
  const div = document.createElement('div')
  div.classList.add('grid-stack-item')
  div.setAttribute('gs-id', item.id)
  div.setAttribute('gs-x', item.x.toString())
  div.setAttribute('gs-y', item.y.toString())
  div.setAttribute('gs-w', item.w.toString())
  div.setAttribute('gs-h', item.h.toString())

  const wrapper = document.createElement('div')
  wrapper.classList.add('grid-stack-item-content-wrapper', 'relative', 'h-full')

  // 按钮容器
  const buttonContainer = document.createElement('div')
  buttonContainer.className = 'absolute top-1 right-1 z-10 flex space-x-1'

  // 设置按钮
  const settingsBtn = document.createElement('button')
  settingsBtn.innerHTML = '⚙️'
  settingsBtn.className = 'bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-70 hover:opacity-100 transition-opacity'
  settingsBtn.onclick = (e) => {
    e.stopPropagation()
    layoutStore.startEditing(item.id)
  }

  // 删除按钮
  const deleteBtn = document.createElement('button')
  deleteBtn.innerHTML = '×'
  deleteBtn.className = 'bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-70 hover:opacity-100 transition-opacity'
  deleteBtn.onclick = (e) => {
    e.stopPropagation()
    removeWidget(item.id)
  }

  buttonContainer.appendChild(settingsBtn)
  buttonContainer.appendChild(deleteBtn)

  const content = document.createElement('div')
  content.classList.add('grid-stack-item-content', 'h-full', 'rounded-lg', 'overflow-hidden')
  content.style.cssText = `
    background-color: rgba(10, 22, 52, 0.6);
    border: 1px solid rgba(0, 150, 255, 0.4);
    box-shadow: 0 0 15px rgba(0, 150, 255, 0.2);
    backdrop-filter: blur(5px);
  `

  wrapper.appendChild(buttonContainer)
  wrapper.appendChild(content)
  div.appendChild(wrapper)
  return div
}

// 渲染Vue组件到GridStack项目中
const renderVueComponent = (item: LayoutItem, element: HTMLElement) => {
  const component = getComponent(item.component)
  if (!component) {
    console.warn(`Component ${item.component} not found`)
    return
  }

  const content = element.querySelector('.grid-stack-item-content')
  if (!content) return

  // 创建Vue应用实例
  const app = createApp(component, item.props)
  const instance = app.mount(content)

  // 保存实例以便后续清理
  componentInstances.set(item.id, { app, instance })
}

// 移除组件
const removeWidget = (widgetId: string) => {
  // 清理Vue组件实例
  const instance = componentInstances.get(widgetId)
  if (instance) {
    instance.app.unmount()
    componentInstances.delete(widgetId)
  }

  // 从GridStack中移除
  if (grid) {
    const element = grid.getGridItems().find(el => el.getAttribute('gs-id') === widgetId)
    if (element) {
      grid.removeWidget(element)
    }
  }

  // 从store中移除
  layoutStore.removeWidget(widgetId)
}

// 初始化GridStack
const initGridStack = () => {
  console.log('Initializing GridStack...')
  console.log('gridStackRef.value:', gridStackRef.value)

  if (!gridStackRef.value) {
    console.error('GridStack container not found!')
    return
  }

  grid = GridStack.init({
    column: 12,
    cellHeight: 60,
    margin: 8,
    resizable: true,
    draggable: true,
    acceptWidgets: '.new-widget', // 接受来自工具箱的组件
    removable: false // 禁用默认的移除功能，我们用自定义删除按钮
  }, gridStackRef.value)

  console.log('GridStack initialized:', grid)

  // 监听从工具箱拖入新组件
  grid.on('added', (event, items) => {
    console.log('Widget added:', items)
    if (items && items.length > 0) {
      const newItem = items[0]
      const node = newItem.el as HTMLElement

      // 从 gs- attributes 恢复组件元数据
      const component = node.getAttribute('gs-component') || 'BasicInfoCard'
      const propsStr = node.getAttribute('gs-props') || '{}'
      let props = {}
      try {
        props = JSON.parse(propsStr)
      } catch (e) {
        console.warn('Failed to parse props:', propsStr)
      }

      // 添加到store
      layoutStore.addWidget({
        x: newItem.x || 0,
        y: newItem.y || 0,
        w: newItem.w || 4,
        h: newItem.h || 5,
        component,
        props
      })

      // 移除临时的占位符元素
      grid!.removeWidget(node, false)
    }
  })

  // 监听布局变化
  grid.on('change', (event, items) => {
    if (items && items.length > 0) {
      const newLayout: LayoutItem[] = items.map(item => {
        const existingItem = layoutStore.layout.find(l => l.id === item.id)
        return {
          id: item.id!,
          x: item.x!,
          y: item.y!,
          w: item.w!,
          h: item.h!,
          component: existingItem?.component || 'BasicInfoCard',
          props: existingItem?.props || {}
        }
      })
      layoutStore.updateLayout(newLayout)
    }
  })

  // 加载初始布局
  loadLayout()
}

// 加载布局
const loadLayout = () => {
  if (!grid) return

  // 清理现有组件
  componentInstances.forEach(({ app }) => {
    app.unmount()
  })
  componentInstances.clear()

  // 清空grid
  grid.removeAll()

  // 添加布局项
  layoutStore.layout.forEach(item => {
    const element = createGridItem(item)
    grid!.addWidget(element)
    renderVueComponent(item, element)
  })
}

// 清空布局
const clearLayout = () => {
  layoutStore.clearLayout()
  loadLayout()
}

// 重置布局
const resetLayout = () => {
  layoutStore.resetLayout()
  loadLayout()
}

onMounted(() => {
  console.log('DashboardView mounted')
  console.log('Layout store data:', layoutStore.layout)
  initGridStack()
})

onUnmounted(() => {
  // 清理Vue组件实例
  componentInstances.forEach(({ app }) => {
    app.unmount()
  })
  componentInstances.clear()

  // 销毁GridStack实例
  if (grid) {
    grid.destroy()
  }
})
</script>

<style scoped>
.dashboard-view {
  font-family: 'Inter', sans-serif;
}

/* GridStack样式覆盖 */
:deep(.grid-stack) {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 8px;
  min-height: 400px;
  padding: 16px;
}

:deep(.grid-stack-item) {
  background: transparent;
}

:deep(.grid-stack-item-content) {
  background: #374151;
  border-radius: 8px;
  overflow: hidden;
  height: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

:deep(.grid-stack-item-content:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

:deep(.grid-stack-item.ui-draggable-dragging) {
  opacity: 0.8;
  transform: rotate(1deg);
  z-index: 1000;
}

:deep(.grid-stack-item .ui-resizable-handle) {
  background: #3B82F6;
  opacity: 0;
  transition: opacity 0.2s;
}

:deep(.grid-stack-item:hover .ui-resizable-handle) {
  opacity: 0.7;
}

/* 操作按钮样式 */
:deep(.grid-stack-item-content-wrapper button) {
  font-family: Arial, sans-serif;
  font-weight: bold;
  line-height: 1;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

:deep(.grid-stack-item-content-wrapper button:hover) {
  transform: scale(1.1);
}

/* 按钮容器样式 */
:deep(.grid-stack-item-content-wrapper .flex) {
  opacity: 0;
  transition: opacity 0.2s ease;
}

:deep(.grid-stack-item:hover .grid-stack-item-content-wrapper .flex) {
  opacity: 1;
}

/* 拖拽占位符样式 */
:deep(.grid-stack-placeholder) {
  background: rgba(59, 130, 246, 0.3) !important;
  border: 2px dashed #3B82F6 !important;
  border-radius: 8px !important;
}

/* 外部拖拽时的样式 */
:deep(.grid-stack.ui-droppable-active) {
  background: rgba(34, 197, 94, 0.1) !important;
  border: 2px dashed #22C55E;
}
</style>
