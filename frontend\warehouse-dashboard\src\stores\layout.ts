import { defineStore } from 'pinia'
import { ref, watch, computed } from 'vue'

const LAYOUT_STORAGE_KEY = 'dashboardLayout'

// 布局项接口定义
export interface LayoutItem {
  id: string
  x: number
  y: number
  w: number
  h: number
  component: string
  props: Record<string, any>
}

// 新组件数据接口
export interface WidgetData {
  x: number
  y: number
  w: number
  h: number
  component: string
  props: Record<string, any>
}

// 默认布局配置
const getDefaultLayout = (): LayoutItem[] => [
  {
    id: '1',
    x: 0,
    y: 0,
    w: 4,
    h: 5,
    component: 'BasicInfoCard',
    props: { title: '基本信息' }
  },
  {
    id: '2',
    x: 4,
    y: 0,
    w: 4,
    h: 5,
    component: 'SalesChartCard',
    props: { title: '商品销售排行' }
  },
  {
    id: '3',
    x: 8,
    y: 0,
    w: 4,
    h: 10,
    component: 'KeyMetricCard',
    props: { title: '数量同比' }
  },
]

// 尝试从 localStorage 加载布局，如果没有则使用默认布局
const getInitialLayout = (): LayoutItem[] => {
  // 检查是否在浏览器环境中
  if (typeof window === 'undefined') {
    return getDefaultLayout()
  }

  try {
    const saved = localStorage.getItem(LAYOUT_STORAGE_KEY)
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.warn('Failed to load layout from localStorage:', error)
  }

  // 返回默认布局
  return getDefaultLayout()
}

export const useLayoutStore = defineStore('layout', () => {
  const layout = ref<LayoutItem[]>(getInitialLayout())

  // 新增 State: 追踪正在编辑的组件ID
  const editingWidgetId = ref<string | null>(null)

  // 新增 Computed: 获取正在编辑的组件的完整数据
  const editingWidget = computed(() => {
    if (!editingWidgetId.value) return null
    return layout.value.find(w => w.id === editingWidgetId.value) || null
  })

  // 监听布局变化，并将其自动保存到 localStorage
  watch(layout, (newLayout) => {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') return

    try {
      const serializedLayout = newLayout.map(item => ({
        x: item.x,
        y: item.y,
        w: item.w,
        h: item.h,
        id: item.id,
        component: item.component,
        props: item.props
      }))
      localStorage.setItem(LAYOUT_STORAGE_KEY, JSON.stringify(serializedLayout))
      console.log('Layout saved!', serializedLayout)
    } catch (error) {
      console.error('Failed to save layout to localStorage:', error)
    }
  }, { deep: true })

  // Action: 开始编辑
  const startEditing = (widgetId: string) => {
    editingWidgetId.value = widgetId
    console.log('Started editing widget:', widgetId)
  }

  // Action: 更新组件的 props
  const updateWidgetProps = (widgetId: string, newProps: Record<string, any>) => {
    const widget = layout.value.find(w => w.id === widgetId)
    if (widget) {
      widget.props = { ...widget.props, ...newProps }
      console.log('Updated widget props:', widgetId, newProps)
    }
    editingWidgetId.value = null // 关闭编辑模式
  }

  // Action: 取消编辑
  const cancelEditing = () => {
    editingWidgetId.value = null
    console.log('Cancelled editing')
  }

  // Action: 添加一个新组件
  const addWidget = (widgetData: WidgetData) => {
    const newWidget: LayoutItem = {
      ...widgetData,
      id: Date.now().toString() // 使用时间戳作为简单唯一的ID
    }
    layout.value.push(newWidget)
    console.log('Added widget:', newWidget)
  }

  // Action: 移除一个组件
  const removeWidget = (widgetId: string) => {
    const index = layout.value.findIndex(w => w.id === widgetId)
    if (index > -1) {
      layout.value.splice(index, 1)
      console.log('Removed widget:', widgetId)
    }
    // 如果正在编辑被删除的组件，取消编辑状态
    if (editingWidgetId.value === widgetId) {
      editingWidgetId.value = null
    }
  }

  // 更新布局
  const updateLayout = (newLayout: LayoutItem[]) => {
    layout.value = newLayout
  }

  // 添加新的布局项（保留向后兼容性）
  const addLayoutItem = (item: LayoutItem) => {
    layout.value.push(item)
  }

  // 移除布局项（保留向后兼容性）
  const removeLayoutItem = (id: string) => {
    const index = layout.value.findIndex(item => item.id === id)
    if (index !== -1) {
      layout.value.splice(index, 1)
    }
  }

  // 清空布局
  const clearLayout = () => {
    layout.value = []
    console.log('Layout cleared')
  }

  // 重置为默认布局
  const resetLayout = () => {
    layout.value = getInitialLayout()
  }

  return {
    layout,
    editingWidgetId,     // 暴露编辑状态
    editingWidget,       // 暴露正在编辑的组件
    addWidget,
    removeWidget,
    startEditing,        // 暴露编辑功能
    updateWidgetProps,   // 暴露属性更新功能
    cancelEditing,       // 暴露取消编辑功能
    updateLayout,
    addLayoutItem,
    removeLayoutItem,
    resetLayout,
    clearLayout
  }
})
