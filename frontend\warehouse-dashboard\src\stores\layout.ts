import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

// 布局项接口定义
export interface LayoutItem {
  id: string
  x: number
  y: number
  w: number
  h: number
  component: string
  props: Record<string, any>
}

// 默认布局配置
const getDefaultLayout = (): LayoutItem[] => [
  {
    id: '1',
    x: 0,
    y: 0,
    w: 4,
    h: 5,
    component: 'BasicInfoCard',
    props: { title: '基本信息' }
  },
  {
    id: '2',
    x: 4,
    y: 0,
    w: 4,
    h: 5,
    component: 'SalesChartCard',
    props: { title: '商品销售排行' }
  },
  {
    id: '3',
    x: 8,
    y: 0,
    w: 4,
    h: 10,
    component: 'KeyMetricCard',
    props: { title: '数量同比' }
  },
]

// 尝试从 localStorage 加载布局，如果没有则使用默认布局
const getInitialLayout = (): LayoutItem[] => {
  // 检查是否在浏览器环境中
  if (typeof window === 'undefined') {
    return getDefaultLayout()
  }

  try {
    const saved = localStorage.getItem('dashboardLayout')
    if (saved) {
      return JSON.parse(saved)
    }
  } catch (error) {
    console.warn('Failed to load layout from localStorage:', error)
  }

  // 返回默认布局
  return getDefaultLayout()
}

export const useLayoutStore = defineStore('layout', () => {
  const layout = ref<LayoutItem[]>(getInitialLayout())

  // 监听布局变化，并将其自动保存到 localStorage
  watch(layout, (newLayout) => {
    // 检查是否在浏览器环境中
    if (typeof window === 'undefined') return

    try {
      const serializedLayout = newLayout.map(item => ({
        x: item.x,
        y: item.y,
        w: item.w,
        h: item.h,
        id: item.id,
        component: item.component,
        props: item.props
      }))
      localStorage.setItem('dashboardLayout', JSON.stringify(serializedLayout))
      console.log('Layout saved!', serializedLayout)
    } catch (error) {
      console.error('Failed to save layout to localStorage:', error)
    }
  }, { deep: true })

  // 更新布局
  const updateLayout = (newLayout: LayoutItem[]) => {
    layout.value = newLayout
  }

  // 添加新的布局项
  const addLayoutItem = (item: LayoutItem) => {
    layout.value.push(item)
  }

  // 移除布局项
  const removeLayoutItem = (id: string) => {
    const index = layout.value.findIndex(item => item.id === id)
    if (index !== -1) {
      layout.value.splice(index, 1)
    }
  }

  // 重置为默认布局
  const resetLayout = () => {
    layout.value = getInitialLayout()
  }

  return { 
    layout, 
    updateLayout, 
    addLayoutItem, 
    removeLayoutItem, 
    resetLayout 
  }
})
