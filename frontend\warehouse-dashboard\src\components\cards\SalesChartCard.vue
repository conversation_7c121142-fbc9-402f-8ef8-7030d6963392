<template>
  <div class="sales-chart-card h-full bg-gradient-to-br from-green-600 to-green-800 rounded-lg p-4 text-white">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="text-2xl">📈</div>
    </div>
    
    <div class="h-full">
      <div ref="chartContainer" class="w-full" style="height: calc(100% - 60px);"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

interface Props {
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '商品销售排行'
})

const chartContainer = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null

// 模拟销售数据
const salesData = [
  { name: '电子产品', value: 2580 },
  { name: '服装鞋帽', value: 1890 },
  { name: '家居用品', value: 1456 },
  { name: '食品饮料', value: 1234 },
  { name: '图书文具', value: 987 },
  { name: '运动户外', value: 756 },
]

const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: salesData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          position: 'outside',
          color: '#fff',
          fontSize: 12,
          formatter: '{b}: {c}'
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: '#fff'
          }
        },
        itemStyle: {
          borderRadius: 8,
          borderColor: '#fff',
          borderWidth: 2
        }
      }
    ],
    color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
  }

  chartInstance.setOption(option)
  
  // 响应式调整
  window.addEventListener('resize', handleResize)
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    window.removeEventListener('resize', handleResize)
  }
})
</script>

<style scoped>
.sales-chart-card {
  min-height: 200px;
}
</style>
