<template>
  <div class="sales-chart-card h-full bg-gradient-to-br from-green-600 to-green-800 rounded-lg p-4 text-white">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="text-2xl">📈</div>
    </div>
    
    <div class="h-full">
      <div ref="chartContainer" class="w-full" style="height: calc(100% - 60px);"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { useDataSourceStore, type DataItem } from '@/stores/dataSource'

interface Props {
  title?: string
  dataSourceId?: string | null
}

const props = withDefaults(defineProps<Props>(), {
  title: '商品销售排行',
  dataSourceId: null
})

const chartContainer = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null
const dataSourceStore = useDataSourceStore()

// 默认销售数据（当没有选择数据源时使用）
const defaultSalesData: DataItem[] = [
  { name: '电子产品', value: 2580 },
  { name: '服装鞋帽', value: 1890 },
  { name: '家居用品', value: 1456 },
  { name: '食品饮料', value: 1234 },
  { name: '图书文具', value: 987 },
  { name: '运动户外', value: 756 },
]

// 核心逻辑：获取并更新图表
const updateChartData = async () => {
  if (!chartInstance) return

  let data: DataItem[] = []

  if (props.dataSourceId) {
    try {
      // 显示加载状态
      chartInstance.showLoading('default', {
        text: '加载数据中...',
        color: '#4ECDC4',
        textColor: '#fff',
        maskColor: 'rgba(0, 0, 0, 0.8)'
      })

      // 获取数据
      data = await dataSourceStore.fetchData(props.dataSourceId)
      chartInstance.hideLoading()

      if (data.length === 0) {
        // 如果没有数据，显示空状态
        chartInstance.clear()
        chartInstance.setOption({
          title: {
            text: '暂无数据',
            left: 'center',
            top: 'center',
            textStyle: {
              color: '#999',
              fontSize: 16
            }
          }
        })
        return
      }
    } catch (error) {
      console.error('Failed to fetch data:', error)
      chartInstance.hideLoading()
      // 发生错误时使用默认数据
      data = defaultSalesData
    }
  } else {
    // 没有选择数据源时使用默认数据
    data = defaultSalesData
  }

  // 更新图表配置
  const option = {
    title: {
      text: props.title,
      left: 'center',
      top: '10px',
      textStyle: {
        color: '#fff',
        fontSize: 16,
        fontWeight: 'bold'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(0,0,0,0.8)',
      textStyle: {
        color: '#fff'
      },
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      show: false // 在小组件中隐藏图例以节省空间
    },
    series: [
      {
        type: 'pie',
        radius: ['35%', '65%'],
        center: ['50%', '60%'],
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          position: 'outside',
          color: '#fff',
          fontSize: 10,
          formatter: '{b}: {c}'
        },
        labelLine: {
          show: true,
          lineStyle: {
            color: '#fff'
          }
        },
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 1
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: () => Math.random() * 200
      }
    ],
    color: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#74B9FF', '#FD79A8']
  }

  chartInstance.setOption(option, true) // true表示不合并，完全替换
}

const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)

  // 初始加载数据
  updateChartData()

  // 响应式调整
  window.addEventListener('resize', handleResize)
}

const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

// 监听 props 变化，自动刷新图表
watch(() => [props.dataSourceId, props.title], () => {
  updateChartData()
}, { deep: true })

onMounted(() => {
  initChart()
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
    window.removeEventListener('resize', handleResize)
  }
})
</script>

<style scoped>
.sales-chart-card {
  min-height: 200px;
}
</style>
