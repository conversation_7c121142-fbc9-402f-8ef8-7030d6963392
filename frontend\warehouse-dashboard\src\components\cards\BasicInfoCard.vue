<template>
  <div class="basic-info-card h-full bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg p-4 text-white">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="text-2xl">📊</div>
    </div>
    
    <div class="space-y-3">
      <div class="flex justify-between items-center">
        <span class="text-blue-200">总订单数</span>
        <span class="text-xl font-bold">{{ formatNumber(totalOrders) }}</span>
      </div>
      
      <div class="flex justify-between items-center">
        <span class="text-blue-200">今日新增</span>
        <span class="text-xl font-bold text-green-300">+{{ formatNumber(todayOrders) }}</span>
      </div>
      
      <div class="flex justify-between items-center">
        <span class="text-blue-200">处理中</span>
        <span class="text-xl font-bold text-yellow-300">{{ formatNumber(processingOrders) }}</span>
      </div>
      
      <div class="flex justify-between items-center">
        <span class="text-blue-200">已完成</span>
        <span class="text-xl font-bold text-green-300">{{ formatNumber(completedOrders) }}</span>
      </div>
    </div>
    
    <div class="mt-4 pt-3 border-t border-blue-500">
      <div class="flex justify-between items-center text-sm">
        <span class="text-blue-200">完成率</span>
        <span class="font-semibold">{{ completionRate }}%</span>
      </div>
      <div class="w-full bg-blue-700 rounded-full h-2 mt-2">
        <div 
          class="bg-green-400 h-2 rounded-full transition-all duration-500"
          :style="{ width: `${completionRate}%` }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'

interface Props {
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '基本信息'
})

// 模拟数据
const totalOrders = ref(12580)
const todayOrders = ref(156)
const processingOrders = ref(89)
const completedOrders = ref(11435)

// 计算完成率
const completionRate = computed(() => {
  if (totalOrders.value === 0) return 0
  return Math.round((completedOrders.value / totalOrders.value) * 100)
})

// 格式化数字
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

// 模拟数据更新
onMounted(() => {
  const interval = setInterval(() => {
    // 随机更新数据
    if (Math.random() > 0.7) {
      todayOrders.value += Math.floor(Math.random() * 3)
      totalOrders.value += Math.floor(Math.random() * 3)
      
      if (Math.random() > 0.5) {
        processingOrders.value += Math.floor(Math.random() * 2) - 1
        completedOrders.value += Math.floor(Math.random() * 2)
      }
    }
  }, 5000)

  // 清理定时器
  return () => clearInterval(interval)
})
</script>

<style scoped>
.basic-info-card {
  min-height: 200px;
}
</style>
