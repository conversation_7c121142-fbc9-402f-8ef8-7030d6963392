// 科技感ECharts主题配置
const THEME_COLOR = '#00BFFF' // 深天蓝
const BG_COLOR = 'rgba(10, 22, 52, 0.0)' // 透明背景

export const sciFiTheme = {
  // 主色调配置
  color: [
    '#00BFFF', // 深天蓝
    '#8A2BE2', // 蓝紫色
    '#32CD32', // 酸橙绿
    '#FFD700', // 金色
    '#FF69B4', // 热粉色
    '#00CED1', // 深绿松石色
    '#FF6347', // 番茄色
    '#9370DB', // 中紫色
    '#20B2AA', // 浅海绿色
    '#FFA500'  // 橙色
  ],
  
  // 背景配置
  backgroundColor: BG_COLOR,
  
  // 标题样式
  title: {
    textStyle: {
      color: '#EFEFEF',
      fontSize: 16,
      fontWeight: 'bold'
    },
    subtextStyle: {
      color: '#CCCCCC',
      fontSize: 12
    }
  },
  
  // 提示框样式
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0, 191, 255, 0.1)'
      }
    },
    backgroundColor: 'rgba(10, 22, 52, 0.9)',
    borderColor: 'rgba(0, 150, 255, 0.6)',
    borderWidth: 1,
    textStyle: {
      color: '#EFEFEF',
      fontSize: 12
    },
    extraCssText: 'backdrop-filter: blur(5px); box-shadow: 0 0 10px rgba(0, 150, 255, 0.3);'
  },
  
  // 图例样式
  legend: {
    textStyle: {
      color: '#EFEFEF',
      fontSize: 12
    },
    pageTextStyle: {
      color: '#EFEFEF'
    }
  },
  
  // 网格配置
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    top: '10%',
    containLabel: true,
    backgroundColor: 'transparent',
    borderColor: 'rgba(0, 150, 255, 0.2)',
    borderWidth: 1
  },
  
  // X轴样式
  xAxis: {
    type: 'category',
    axisLine: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.5)',
        width: 1
      }
    },
    axisTick: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.3)'
      }
    },
    axisLabel: {
      color: '#EFEFEF',
      fontSize: 11
    },
    splitLine: {
      show: false
    }
  },
  
  // Y轴样式
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.5)',
        width: 1
      }
    },
    axisTick: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.3)'
      }
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.1)',
        type: 'dashed'
      }
    },
    axisLabel: {
      color: '#EFEFEF',
      fontSize: 11
    }
  },
  
  // 系列样式
  series: [{
    // 柱状图样式
    type: 'bar',
    barWidth: '60%',
    itemStyle: {
      borderRadius: [4, 4, 0, 0],
      borderColor: 'rgba(0, 150, 255, 0.3)',
      borderWidth: 1,
      shadowColor: 'rgba(0, 150, 255, 0.3)',
      shadowBlur: 5
    },
    emphasis: {
      itemStyle: {
        shadowColor: 'rgba(0, 150, 255, 0.6)',
        shadowBlur: 10
      }
    }
  }],
  
  // 饼图样式
  pie: {
    itemStyle: {
      borderRadius: 6,
      borderColor: 'rgba(0, 150, 255, 0.3)',
      borderWidth: 2,
      shadowColor: 'rgba(0, 150, 255, 0.2)',
      shadowBlur: 8
    },
    label: {
      color: '#EFEFEF',
      fontSize: 11
    },
    labelLine: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.5)'
      }
    },
    emphasis: {
      itemStyle: {
        shadowColor: 'rgba(0, 150, 255, 0.5)',
        shadowBlur: 15
      }
    }
  },
  
  // 折线图样式
  line: {
    lineStyle: {
      width: 2,
      shadowColor: 'rgba(0, 150, 255, 0.3)',
      shadowBlur: 5
    },
    itemStyle: {
      borderWidth: 2,
      shadowColor: 'rgba(0, 150, 255, 0.3)',
      shadowBlur: 5
    },
    areaStyle: {
      opacity: 0.1
    }
  },
  
  // 散点图样式
  scatter: {
    itemStyle: {
      shadowColor: 'rgba(0, 150, 255, 0.3)',
      shadowBlur: 5
    }
  },
  
  // 雷达图样式
  radar: {
    name: {
      textStyle: {
        color: '#EFEFEF'
      }
    },
    splitLine: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.2)'
      }
    },
    splitArea: {
      areaStyle: {
        color: [
          'rgba(0, 150, 255, 0.05)',
          'rgba(0, 150, 255, 0.02)'
        ]
      }
    },
    axisLine: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.3)'
      }
    }
  },
  
  // 仪表盘样式
  gauge: {
    axisLine: {
      lineStyle: {
        color: [[0.2, '#32CD32'], [0.8, '#FFD700'], [1, '#FF6347']],
        width: 8
      }
    },
    axisTick: {
      lineStyle: {
        color: 'rgba(0, 150, 255, 0.5)'
      }
    },
    axisLabel: {
      color: '#EFEFEF'
    },
    detail: {
      color: '#EFEFEF'
    }
  },
  
  // 数据区域缩放组件样式
  dataZoom: {
    backgroundColor: 'rgba(10, 22, 52, 0.3)',
    dataBackgroundColor: 'rgba(0, 150, 255, 0.2)',
    fillerColor: 'rgba(0, 150, 255, 0.1)',
    handleColor: '#00BFFF',
    handleSize: 100,
    textStyle: {
      color: '#EFEFEF'
    }
  },
  
  // 时间轴样式
  timeline: {
    lineStyle: {
      color: 'rgba(0, 150, 255, 0.5)'
    },
    itemStyle: {
      color: '#00BFFF'
    },
    controlStyle: {
      color: '#00BFFF'
    },
    checkpointStyle: {
      color: '#FFD700'
    },
    label: {
      color: '#EFEFEF'
    }
  }
}

// 暗色主题变体
export const darkSciFiTheme = {
  ...sciFiTheme,
  backgroundColor: 'rgba(5, 11, 26, 0.8)',
  color: [
    '#00FFFF', // 青色
    '#FF00FF', // 洋红色
    '#FFFF00', // 黄色
    '#00FF00', // 绿色
    '#FF8C00', // 深橙色
    '#9400D3', // 紫罗兰色
    '#FF1493', // 深粉色
    '#00FA9A', // 中春绿色
    '#FFB6C1', // 浅粉色
    '#87CEEB'  // 天蓝色
  ]
}
