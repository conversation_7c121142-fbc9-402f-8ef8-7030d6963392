<template>
  <div class="key-metric-card h-full bg-gradient-to-br from-purple-600 to-purple-800 rounded-lg p-4 text-white">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      <div class="text-2xl">📊</div>
    </div>
    
    <div class="space-y-4">
      <!-- 主要指标 -->
      <div class="text-center">
        <div class="text-3xl font-bold mb-2">{{ formatNumber(currentValue) }}</div>
        <div class="text-purple-200 text-sm">当前数量</div>
      </div>
      
      <!-- 同比数据 -->
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center">
          <div class="text-xl font-semibold" :class="yearOverYearClass">
            {{ yearOverYearChange > 0 ? '+' : '' }}{{ yearOverYearChange }}%
          </div>
          <div class="text-purple-200 text-xs">同比去年</div>
        </div>
        
        <div class="text-center">
          <div class="text-xl font-semibold" :class="monthOverMonthClass">
            {{ monthOverMonthChange > 0 ? '+' : '' }}{{ monthOverMonthChange }}%
          </div>
          <div class="text-purple-200 text-xs">环比上月</div>
        </div>
      </div>
      
      <!-- 趋势图 -->
      <div class="mt-4">
        <div class="text-sm text-purple-200 mb-2">近7天趋势</div>
        <div ref="trendChart" class="w-full h-16"></div>
      </div>
      
      <!-- 目标完成度 -->
      <div class="mt-4">
        <div class="flex justify-between text-sm mb-1">
          <span class="text-purple-200">目标完成度</span>
          <span class="font-semibold">{{ targetCompletion }}%</span>
        </div>
        <div class="w-full bg-purple-700 rounded-full h-2">
          <div 
            class="bg-yellow-400 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${Math.min(targetCompletion, 100)}%` }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

interface Props {
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '数量同比'
})

const trendChart = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null

// 模拟数据
const currentValue = ref(8567)
const yearOverYearChange = ref(12.5)
const monthOverMonthChange = ref(-2.3)
const targetValue = ref(10000)

// 近7天数据
const trendData = ref([7800, 8100, 8300, 8200, 8400, 8500, 8567])

// 计算样式类
const yearOverYearClass = computed(() => 
  yearOverYearChange.value > 0 ? 'text-green-300' : 'text-red-300'
)

const monthOverMonthClass = computed(() => 
  monthOverMonthChange.value > 0 ? 'text-green-300' : 'text-red-300'
)

// 计算目标完成度
const targetCompletion = computed(() => 
  Math.round((currentValue.value / targetValue.value) * 100)
)

// 格式化数字
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

const initTrendChart = () => {
  if (!trendChart.value) return

  chartInstance = echarts.init(trendChart.value)
  
  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: ['Day1', 'Day2', 'Day3', 'Day4', 'Day5', 'Day6', 'Day7']
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        type: 'line',
        data: trendData.value,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#FFD700',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 215, 0, 0.3)' },
              { offset: 1, color: 'rgba(255, 215, 0, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

onMounted(() => {
  initTrendChart()
  
  // 模拟数据更新
  const interval = setInterval(() => {
    if (Math.random() > 0.8) {
      const change = Math.floor(Math.random() * 20) - 10
      currentValue.value += change
      trendData.value.shift()
      trendData.value.push(currentValue.value)
      
      if (chartInstance) {
        chartInstance.setOption({
          series: [{ data: trendData.value }]
        })
      }
    }
  }, 3000)

  return () => clearInterval(interval)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.key-metric-card {
  min-height: 200px;
}
</style>
