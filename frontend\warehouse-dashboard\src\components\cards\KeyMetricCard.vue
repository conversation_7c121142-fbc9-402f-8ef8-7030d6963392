<template>
  <div class="key-metric-card h-full bg-gradient-to-br from-purple-600 to-purple-800 rounded-lg p-4 text-white relative">
    <!-- 设置图标 -->
    <button
      @click="isEditing = true"
      class="absolute top-2 right-2 w-6 h-6 bg-black bg-opacity-30 hover:bg-opacity-50 rounded-full flex items-center justify-center transition-all duration-200 z-10"
      title="配置组件"
    >
      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      </svg>
    </button>

    <!-- 正常显示模式 -->
    <div v-if="!isEditing">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">{{ title }}</h3>
        <div class="text-2xl">📊</div>
      </div>
    
    <div class="space-y-4">
      <!-- 主要指标 -->
      <div class="text-center">
        <div class="text-3xl font-bold mb-2">{{ formatNumber(currentValue) }}</div>
        <div class="text-purple-200 text-sm">当前数量</div>
      </div>
      
      <!-- 同比数据 -->
      <div class="grid grid-cols-2 gap-4">
        <div class="text-center">
          <div class="text-xl font-semibold" :class="yearOverYearClass">
            {{ yearOverYearChange > 0 ? '+' : '' }}{{ yearOverYearChange }}%
          </div>
          <div class="text-purple-200 text-xs">同比去年</div>
        </div>
        
        <div class="text-center">
          <div class="text-xl font-semibold" :class="monthOverMonthClass">
            {{ monthOverMonthChange > 0 ? '+' : '' }}{{ monthOverMonthChange }}%
          </div>
          <div class="text-purple-200 text-xs">环比上月</div>
        </div>
      </div>
      
      <!-- 趋势图 -->
      <div class="mt-4">
        <div class="text-sm text-purple-200 mb-2">近7天趋势</div>
        <div ref="trendChart" class="w-full h-16"></div>
      </div>
      
      <!-- 目标完成度 -->
      <div class="mt-4">
        <div class="flex justify-between text-sm mb-1">
          <span class="text-purple-200">目标完成度</span>
          <span class="font-semibold">{{ targetCompletion }}%</span>
        </div>
        <div class="w-full bg-purple-700 rounded-full h-2">
          <div 
            class="bg-yellow-400 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${Math.min(targetCompletion, 100)}%` }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 配置模式 -->
    <div v-else class="h-full flex items-center justify-center">
      <WidgetConfigurator
        :initial-props="props"
        @save="handleSave"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'
import { useDataSourceStore, type DataItem } from '@/stores/dataSource'
import { useLayoutStore } from '@/stores/layout'
import WidgetConfigurator from '@/components/configurators/WidgetConfigurator.vue'

interface Props {
  title?: string
  dataSourceId?: string | null
  widgetId?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '数量同比',
  dataSourceId: null,
  widgetId: undefined
})

const trendChart = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null
const dataSourceStore = useDataSourceStore()
const layoutStore = useLayoutStore()

// 编辑状态
const isEditing = ref(false)

// 数据状态
const currentValue = ref(8567)
const yearOverYearChange = ref(12.5)
const monthOverMonthChange = ref(-2.3)
const targetValue = ref(10000)
const trendData = ref([7800, 8100, 8300, 8200, 8400, 8500, 8567])
const isLoading = ref(false)

// 更新数据的函数
const updateData = async () => {
  if (!props.dataSourceId) {
    // 使用默认数据
    currentValue.value = 8567
    yearOverYearChange.value = 12.5
    monthOverMonthChange.value = -2.3
    targetValue.value = 10000
    trendData.value = [7800, 8100, 8300, 8200, 8400, 8500, 8567]
    return
  }

  try {
    isLoading.value = true
    const data = await dataSourceStore.fetchData(props.dataSourceId)

    if (data.length > 0) {
      // 使用第一个数据项作为当前值
      currentValue.value = data[0].value || 0

      // 模拟计算同比和环比
      yearOverYearChange.value = (data[0] as any).growth || Math.random() * 20 - 10
      monthOverMonthChange.value = Math.random() * 10 - 5
      targetValue.value = Math.floor(currentValue.value * 1.2)

      // 生成趋势数据
      const baseValue = currentValue.value
      trendData.value = Array.from({ length: 7 }, (_, i) =>
        Math.floor(baseValue * (0.9 + Math.random() * 0.2))
      )
      trendData.value[6] = currentValue.value // 最后一个值是当前值
    }
  } catch (error) {
    console.error('Failed to update key metric data:', error)
  } finally {
    isLoading.value = false
  }
}

// 计算样式类
const yearOverYearClass = computed(() => 
  yearOverYearChange.value > 0 ? 'text-green-300' : 'text-red-300'
)

const monthOverMonthClass = computed(() => 
  monthOverMonthChange.value > 0 ? 'text-green-300' : 'text-red-300'
)

// 计算目标完成度
const targetCompletion = computed(() => 
  Math.round((currentValue.value / targetValue.value) * 100)
)

// 格式化数字
const formatNumber = (num: number): string => {
  return num.toLocaleString()
}

const initTrendChart = () => {
  if (!trendChart.value) return

  chartInstance = echarts.init(trendChart.value)
  
  const option = {
    grid: {
      left: 0,
      right: 0,
      top: 0,
      bottom: 0
    },
    xAxis: {
      type: 'category',
      show: false,
      data: ['Day1', 'Day2', 'Day3', 'Day4', 'Day5', 'Day6', 'Day7']
    },
    yAxis: {
      type: 'value',
      show: false
    },
    series: [
      {
        type: 'line',
        data: trendData.value,
        smooth: true,
        symbol: 'none',
        lineStyle: {
          color: '#FFD700',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(255, 215, 0, 0.3)' },
              { offset: 1, color: 'rgba(255, 215, 0, 0.1)' }
            ]
          }
        }
      }
    ]
  }

  chartInstance.setOption(option)
}

// 监听数据源变化
watch(() => [props.dataSourceId, props.title], () => {
  updateData()
  if (chartInstance) {
    chartInstance.setOption({
      series: [{ data: trendData.value }]
    })
  }
}, { deep: true })

onMounted(() => {
  initTrendChart()
  updateData() // 初始加载数据

  // 模拟数据更新（仅在没有数据源时）
  const interval = setInterval(() => {
    if (!props.dataSourceId && Math.random() > 0.8) {
      const change = Math.floor(Math.random() * 20) - 10
      currentValue.value += change
      trendData.value.shift()
      trendData.value.push(currentValue.value)

      if (chartInstance) {
        chartInstance.setOption({
          series: [{ data: trendData.value }]
        })
      }
    }
  }, 3000)

  return () => clearInterval(interval)
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})

// 配置相关事件处理
const handleSave = (newProps: Record<string, any>) => {
  if (props.widgetId) {
    layoutStore.updateWidgetProps(props.widgetId, newProps)
    console.log('保存指标配置:', props.widgetId, newProps)
  } else {
    console.warn('无法保存配置：缺少 widgetId')
  }
  isEditing.value = false
}

const handleCancel = () => {
  isEditing.value = false
}
</script>

<style scoped>
.key-metric-card {
  min-height: 200px;
}
</style>
