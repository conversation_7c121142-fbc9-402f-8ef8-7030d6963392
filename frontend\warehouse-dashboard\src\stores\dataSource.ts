import { defineStore } from 'pinia'
import { ref } from 'vue'

// 数据项接口定义
export interface DataItem {
  name: string
  value: number
  [key: string]: any
}

// 数据源接口定义
export interface DataSource {
  id: string
  name: string
  description?: string
  type?: 'chart' | 'table' | 'metric'
}

// 模拟的后端数据库
const MOCK_DB: Record<string, DataItem[]> = {
  'sales-q1': [
    { name: '笔记本', value: 4820, category: '电脑设备' },
    { name: '显示器', value: 3109, category: '电脑设备' },
    { name: '键盘', value: 1523, category: '外设' },
    { name: '鼠标', value: 987, category: '外设' },
    { name: '音响', value: 756, category: '外设' },
  ],
  'sales-q2': [
    { name: '笔记本', value: 5120, category: '电脑设备' },
    { name: '显示器', value: 3500, category: '电脑设备' },
    { name: '风扇', value: 2100, category: '散热设备' },
    { name: '键盘', value: 1321, category: '外设' },
    { name: '耳机', value: 890, category: '外设' },
  ],
  'user-growth': [
    { name: '一月', value: 120, growth: 15.2 },
    { name: '二月', value: 250, growth: 108.3 },
    { name: '三月', value: 180, growth: -28.0 },
    { name: '四月', value: 320, growth: 77.8 },
    { name: '五月', value: 280, growth: -12.5 },
    { name: '六月', value: 450, growth: 60.7 },
  ],
  'revenue-monthly': [
    { name: '1月', value: 85000, target: 80000 },
    { name: '2月', value: 92000, target: 85000 },
    { name: '3月', value: 78000, target: 90000 },
    { name: '4月', value: 105000, target: 95000 },
    { name: '5月', value: 98000, target: 100000 },
    { name: '6月', value: 112000, target: 105000 },
  ],
  'region-distribution': [
    { name: '华东', value: 35.6, count: 1250 },
    { name: '华南', value: 28.3, count: 980 },
    { name: '华北', value: 18.7, count: 650 },
    { name: '西南', value: 10.2, count: 360 },
    { name: '东北', value: 4.8, count: 170 },
    { name: '西北', value: 2.4, count: 85 },
  ],
  'product-performance': [
    { name: '产品A', value: 95.2, satisfaction: 4.8, sales: 2340 },
    { name: '产品B', value: 87.6, satisfaction: 4.5, sales: 1890 },
    { name: '产品C', value: 92.1, satisfaction: 4.7, sales: 2100 },
    { name: '产品D', value: 78.9, satisfaction: 4.2, sales: 1560 },
    { name: '产品E', value: 89.3, satisfaction: 4.6, sales: 1780 },
  ],
  'province-sales': [
    { name: '上海', value: 4820 }, { name: '北京', value: 3980 },
    { name: '广东', value: 3500 }, { name: '江苏', value: 3100 },
    { name: '浙江', value: 2900 }, { name: '山东', value: 2600 },
    { name: '四川', value: 2200 }, { name: '河南', value: 1800 },
    { name: '湖北', value: 1750 }, { name: '湖南', value: 1700 },
    { name: '福建', value: 1500 }, { name: '安徽', value: 1400 },
    { name: '陕西', value: 1300 }, { name: '辽宁', value: 1100 },
    { name: '重庆', value: 900 },  { name: '新疆', value: 500 },
  ]
}

export const useDataSourceStore = defineStore('dataSource', () => {
  // 可用数据源列表
  const sources = ref<Record<string, DataSource>>({
    'sales-q1': { 
      id: 'sales-q1', 
      name: 'Q1 销售数据', 
      description: '第一季度产品销售统计',
      type: 'chart'
    },
    'sales-q2': { 
      id: 'sales-q2', 
      name: 'Q2 销售数据', 
      description: '第二季度产品销售统计',
      type: 'chart'
    },
    'user-growth': { 
      id: 'user-growth', 
      name: '用户增长曲线', 
      description: '月度用户增长趋势分析',
      type: 'chart'
    },
    'revenue-monthly': { 
      id: 'revenue-monthly', 
      name: '月度营收数据', 
      description: '每月营收与目标对比',
      type: 'chart'
    },
    'region-distribution': { 
      id: 'region-distribution', 
      name: '区域分布统计', 
      description: '各地区业务分布情况',
      type: 'chart'
    },
    'product-performance': {
      id: 'product-performance',
      name: '产品性能指标',
      description: '产品综合表现评估',
      type: 'metric'
    },
    'province-sales': {
      id: 'province-sales',
      name: '各省销售数据',
      description: '全国各省份销售业绩分布',
      type: 'chart'
    }
  })

  // 加载状态管理
  const loadingStates = ref<Record<string, boolean>>({})

  // 模拟 API 调用
  const fetchData = async (sourceId: string): Promise<DataItem[]> => {
    if (!sourceId || !MOCK_DB[sourceId]) {
      console.warn(`Data source not found: ${sourceId}`)
      return []
    }

    console.log(`Fetching data for: ${sourceId}`)
    
    // 设置加载状态
    loadingStates.value[sourceId] = true

    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500))
      
      // 模拟偶尔的网络错误（5%概率）
      if (Math.random() < 0.05) {
        throw new Error('Network error: Failed to fetch data')
      }

      const data = MOCK_DB[sourceId] || []
      console.log(`Data fetched successfully for ${sourceId}:`, data)
      
      return [...data] // 返回数据的副本
    } catch (error) {
      console.error(`Error fetching data for ${sourceId}:`, error)
      throw error
    } finally {
      // 清除加载状态
      loadingStates.value[sourceId] = false
    }
  }

  // 获取数据源信息
  const getDataSource = (sourceId: string): DataSource | null => {
    return sources.value[sourceId] || null
  }

  // 获取所有数据源列表
  const getAllDataSources = (): DataSource[] => {
    return Object.values(sources.value)
  }

  // 检查数据源是否存在
  const hasDataSource = (sourceId: string): boolean => {
    return sourceId in sources.value
  }

  // 获取加载状态
  const isLoading = (sourceId: string): boolean => {
    return loadingStates.value[sourceId] || false
  }

  // 预加载数据（可选功能）
  const preloadData = async (sourceIds: string[]): Promise<void> => {
    const promises = sourceIds
      .filter(id => hasDataSource(id))
      .map(id => fetchData(id).catch(err => {
        console.warn(`Failed to preload data for ${id}:`, err)
        return []
      }))
    
    await Promise.all(promises)
    console.log('Data preloading completed')
  }

  return { 
    sources, 
    loadingStates,
    fetchData, 
    getDataSource,
    getAllDataSources,
    hasDataSource,
    isLoading,
    preloadData
  }
})
