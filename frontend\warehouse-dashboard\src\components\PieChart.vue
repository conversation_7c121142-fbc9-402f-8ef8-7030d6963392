<template>
  <div class="chart-card">
    <div class="card-title">{{ title }}</div>
    <div class="card-content" :id="chartId" ref="chartContainer"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

// 定义 props
const props = defineProps({
  title: {
    type: String,
    default: '商品分类占比'
  },
  chartId: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  }
})

// 图表实例和容器引用
const chartInstance = ref(null)
const chartContainer = ref(null)

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance.value = echarts.init(chartContainer.value)
  updateChart()
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance.value) return
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#1E90FF',
      textStyle: {
        color: '#ffffff'
      }
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'center',
      textStyle: {
        color: '#ffffff',
        fontSize: 12
      },
      itemWidth: 14,
      itemHeight: 14
    },
    series: [{
      name: props.title,
      type: 'pie',
      radius: ['30%', '70%'], // 环形饼图
      center: ['65%', '50%'], // 向右偏移，为图例留空间
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '16',
          fontWeight: 'bold',
          color: '#ffffff'
        },
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(30, 144, 255, 0.5)'
        }
      },
      labelLine: {
        show: false
      },
      data: props.data.length > 0 ? props.data : getDefaultData(),
      itemStyle: {
        borderRadius: 5,
        borderColor: '#06164A',
        borderWidth: 2
      }
    }],
    color: ['#1E90FF', '#00CED1', '#32CD32', '#FFD700', '#FF6347', '#9370DB']
  }
  
  chartInstance.value.setOption(option, true)
}

// 默认数据
const getDefaultData = () => [
  { value: 35, name: '电子产品' },
  { value: 25, name: '服装鞋帽' },
  { value: 20, name: '食品饮料' },
  { value: 12, name: '家居用品' },
  { value: 8, name: '其他' }
]

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

// 组件挂载
onMounted(() => {
  initChart()
})

// 组件卸载
onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  window.removeEventListener('resize', handleResize)
})

// 暴露方法供父组件调用
defineExpose({
  updateChart,
  chartInstance
})
</script>

<style scoped>
/* 卡片样式 */
.chart-card {
  background-color: rgba(14, 38, 92, 0.6);
  border: 1px solid #1E90FF;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
}

/* 卡片标题 */
.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2);
  border-bottom: 1px solid #1E90FF;
  color: #ffffff;
}

/* 卡片内容区 */
.card-content {
  flex: 1;
  padding: 15px;
  width: 100%;
  height: 100%;
  min-height: 200px;
}
</style>
