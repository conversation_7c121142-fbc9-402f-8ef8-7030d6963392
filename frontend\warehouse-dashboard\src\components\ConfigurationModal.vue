<template>
  <div 
    v-if="store.editingWidget"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    @click.self="store.cancelEditing"
  >
    <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4 transform transition-all duration-200 scale-100">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-bold text-gray-800">
          配置组件: {{ store.editingWidget.props.title || store.editingWidget.component }}
        </h3>
        <button 
          @click="store.cancelEditing"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
      <!-- Configuration Form -->
      <form @submit.prevent="saveChanges" class="space-y-4">
        <!-- 组件标题配置 -->
        <div>
          <label for="widgetTitle" class="block text-sm font-medium text-gray-700 mb-1">
            组件标题
          </label>
          <input 
            type="text" 
            id="widgetTitle" 
            v-model="editableProps.title"
            class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            placeholder="请输入组件标题"
          >
        </div>

        <!-- 组件类型显示 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            组件类型
          </label>
          <div class="w-full bg-gray-100 rounded-md py-2 px-3 text-gray-600">
            {{ getComponentDisplayName(store.editingWidget.component) }}
          </div>
        </div>

        <!-- 数据源选择 -->
        <div>
          <label for="widgetDataSource" class="block text-sm font-medium text-gray-700 mb-1">
            数据源
          </label>
          <select
            id="widgetDataSource"
            v-model="editableProps.dataSourceId"
            class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          >
            <option :value="null">-- 请选择数据源 --</option>
            <option
              v-for="source in dataSourceStore.getAllDataSources()"
              :key="source.id"
              :value="source.id"
            >
              {{ source.name }}
            </option>
          </select>
          <p v-if="editableProps.dataSourceId && getSelectedDataSourceInfo()" class="text-xs text-gray-500 mt-1">
            {{ getSelectedDataSourceInfo()?.description }}
          </p>
        </div>

        <!-- 组件尺寸配置 -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label for="widgetWidth" class="block text-sm font-medium text-gray-700 mb-1">
              宽度 (列)
            </label>
            <input 
              type="number" 
              id="widgetWidth" 
              v-model.number="editableProps.width"
              min="1" 
              max="12"
              class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
          </div>
          <div>
            <label for="widgetHeight" class="block text-sm font-medium text-gray-700 mb-1">
              高度 (行)
            </label>
            <input 
              type="number" 
              id="widgetHeight" 
              v-model.number="editableProps.height"
              min="1" 
              max="20"
              class="w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            >
          </div>
        </div>

        <!-- 预览区域 -->
        <div class="bg-gray-50 rounded-md p-3 border">
          <div class="text-sm font-medium text-gray-700 mb-2">配置预览</div>
          <div class="text-sm text-gray-600 space-y-1">
            <div>标题: {{ editableProps.title || '未设置' }}</div>
            <div>尺寸: {{ editableProps.width || 4 }} × {{ editableProps.height || 5 }}</div>
            <div>数据源: {{ getSelectedDataSourceInfo()?.name || '未选择' }}</div>
            <div v-if="editableProps.dataSourceId" class="text-xs text-blue-600">
              ✓ 组件将显示来自"{{ getSelectedDataSourceInfo()?.name }}"的动态数据
            </div>
            <div v-else class="text-xs text-orange-600">
              ⚠ 未选择数据源，组件将显示默认数据
            </div>
          </div>
        </div>

        <!-- 按钮组 -->
        <div class="flex justify-end space-x-3 pt-4 border-t">
          <button 
            type="button" 
            @click="store.cancelEditing"
            class="bg-gray-200 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-300 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            取消
          </button>
          <button 
            type="submit"
            class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            保存
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useLayoutStore } from '@/stores/layout'
import { useDataSourceStore } from '@/stores/dataSource'

const store = useLayoutStore()
const dataSourceStore = useDataSourceStore()
const editableProps = ref<Record<string, any>>({})

// 组件类型显示名称映射
const componentDisplayNames: Record<string, string> = {
  'BasicInfoCard': '基本信息卡片',
  'SalesChartCard': '销售图表卡片',
  'KeyMetricCard': '关键指标卡片'
}

const getComponentDisplayName = (componentName: string): string => {
  return componentDisplayNames[componentName] || componentName
}

// 获取选中数据源的信息
const getSelectedDataSourceInfo = () => {
  if (!editableProps.value.dataSourceId) return null
  return dataSourceStore.getDataSource(editableProps.value.dataSourceId)
}

// 当编辑的组件变化时 (即模态框打开时)，深度克隆其 props 到本地
watch(() => store.editingWidget, (newWidget) => {
  if (newWidget) {
    // 深度克隆 props 避免直接修改原始数据
    editableProps.value = JSON.parse(JSON.stringify(newWidget.props))
    
    // 如果没有设置宽度和高度，从布局中获取
    if (!editableProps.value.width) {
      editableProps.value.width = newWidget.w || 4
    }
    if (!editableProps.value.height) {
      editableProps.value.height = newWidget.h || 5
    }
  }
}, { deep: true, immediate: true })

const saveChanges = () => {
  if (!store.editingWidgetId) return
  
  // 保存配置更改
  const updatedProps = { ...editableProps.value }
  
  // 如果尺寸发生变化，需要同时更新布局
  const currentWidget = store.editingWidget
  if (currentWidget) {
    const sizeChanged = 
      currentWidget.w !== updatedProps.width || 
      currentWidget.h !== updatedProps.height
    
    if (sizeChanged) {
      // 更新布局尺寸
      const widget = store.layout.find(w => w.id === store.editingWidgetId)
      if (widget) {
        widget.w = updatedProps.width
        widget.h = updatedProps.height
      }
    }
  }
  
  // 更新组件属性
  store.updateWidgetProps(store.editingWidgetId, updatedProps)
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    store.cancelEditing()
  }
}

// 监听键盘事件
watch(() => store.editingWidget, (newWidget) => {
  if (newWidget) {
    document.addEventListener('keydown', handleKeydown)
  } else {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<style scoped>
/* 模态框动画 */
.fixed {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.bg-white {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

/* 输入框聚焦效果 */
input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮悬停效果 */
button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}
</style>
