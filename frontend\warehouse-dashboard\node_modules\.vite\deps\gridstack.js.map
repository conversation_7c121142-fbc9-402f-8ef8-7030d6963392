{"version": 3, "sources": ["../../gridstack/src/utils.ts", "../../gridstack/src/gridstack-engine.ts", "../../gridstack/src/types.ts", "../../gridstack/src/dd-manager.ts", "../../gridstack/src/dd-touch.ts", "../../gridstack/src/dd-resizable-handle.ts", "../../gridstack/src/dd-base-impl.ts", "../../gridstack/src/dd-resizable.ts", "../../gridstack/src/dd-draggable.ts", "../../gridstack/src/dd-droppable.ts", "../../gridstack/src/dd-element.ts", "../../gridstack/src/dd-gridstack.ts", "../../gridstack/src/gridstack.ts"], "sourcesContent": ["/**\r\n * utils.ts 12.2.2\r\n * Copyright (c) 2021-2024 <PERSON> - see GridStack root license\r\n */\r\n\r\nimport { GridStackElement, GridStackNode, GridStackOptions, numberOrString, GridStackPosition, GridStackWidget } from './types';\r\n\r\nexport interface HeightData {\r\n  h: number;\r\n  unit: string;\r\n}\r\n\r\nexport interface DragTransform {\r\n  xScale: number;\r\n  yScale: number;\r\n  xOffset: number;\r\n  yOffset: number;\r\n}\r\n\r\n/** checks for obsolete method names */\r\n// eslint-disable-next-line\r\nexport function obsolete(self, f, oldName: string, newName: string, rev: string): (...args: any[]) => any {\r\n  const wrapper = (...args) => {\r\n    console.warn('gridstack.js: Function `' + oldName + '` is deprecated in ' + rev + ' and has been replaced ' +\r\n    'with `' + newName + '`. It will be **removed** in a future release');\r\n    return f.apply(self, args);\r\n  }\r\n  wrapper.prototype = f.prototype;\r\n  return wrapper;\r\n}\r\n\r\n/** checks for obsolete grid options (can be used for any fields, but msg is about options) */\r\nexport function obsoleteOpts(opts: GridStackOptions, oldName: string, newName: string, rev: string): void {\r\n  if (opts[oldName] !== undefined) {\r\n    opts[newName] = opts[oldName];\r\n    console.warn('gridstack.js: Option `' + oldName + '` is deprecated in ' + rev + ' and has been replaced with `' +\r\n      newName + '`. It will be **removed** in a future release');\r\n  }\r\n}\r\n\r\n/** checks for obsolete grid options which are gone */\r\nexport function obsoleteOptsDel(opts: GridStackOptions, oldName: string, rev: string, info: string): void {\r\n  if (opts[oldName] !== undefined) {\r\n    console.warn('gridstack.js: Option `' + oldName + '` is deprecated in ' + rev + info);\r\n  }\r\n}\r\n\r\n/** checks for obsolete Jquery element attributes */\r\nexport function obsoleteAttr(el: HTMLElement, oldName: string, newName: string, rev: string): void {\r\n  const oldAttr = el.getAttribute(oldName);\r\n  if (oldAttr !== null) {\r\n    el.setAttribute(newName, oldAttr);\r\n    console.warn('gridstack.js: attribute `' + oldName + '`=' + oldAttr + ' is deprecated on this object in ' + rev + ' and has been replaced with `' +\r\n      newName + '`. It will be **removed** in a future release');\r\n  }\r\n}\r\n\r\n/**\r\n * Utility methods\r\n */\r\nexport class Utils {\r\n\r\n  /** convert a potential selector into actual list of html elements. optional root which defaults to document (for shadow dom) */\r\n  static getElements(els: GridStackElement, root: HTMLElement | Document = document): HTMLElement[] {\r\n    if (typeof els === 'string') {\r\n      const doc = ('getElementById' in root) ? root as Document : undefined;\r\n\r\n      // Note: very common for people use to id='1,2,3' which is only legal as HTML5 id, but not CSS selectors\r\n      // so if we start with a number, assume it's an id and just return that one item...\r\n      // see https://github.com/gridstack/gridstack.js/issues/2234#issuecomment-1523796562\r\n      if (doc && !isNaN(+els[0])) { // start with digit\r\n        const el = doc.getElementById(els);\r\n        return el ? [el] : [];\r\n      }\r\n\r\n      let list = root.querySelectorAll(els);\r\n      if (!list.length && els[0] !== '.' && els[0] !== '#') {\r\n        list = root.querySelectorAll('.' + els);\r\n        if (!list.length) { list = root.querySelectorAll('#' + els) }\r\n      }\r\n      return Array.from(list) as HTMLElement[];\r\n    }\r\n    return [els];\r\n  }\r\n\r\n  /** convert a potential selector into actual single element. optional root which defaults to document (for shadow dom) */\r\n  static getElement(els: GridStackElement, root: HTMLElement | Document = document): HTMLElement {\r\n    if (typeof els === 'string') {\r\n      const doc = ('getElementById' in root) ? root as Document : undefined;\r\n      if (!els.length) return null;\r\n      if (doc && els[0] === '#') {\r\n        return doc.getElementById(els.substring(1));\r\n      }\r\n      if (els[0] === '#' || els[0] === '.' || els[0] === '[') {\r\n        return root.querySelector(els);\r\n      }\r\n\r\n      // if we start with a digit, assume it's an id (error calling querySelector('#1')) as class are not valid CSS\r\n      if (doc && !isNaN(+els[0])) { // start with digit\r\n        return doc.getElementById(els);\r\n      }\r\n\r\n      // finally try string, then id, then class\r\n      let el = root.querySelector(els);\r\n      if (doc && !el) { el = doc.getElementById(els) }\r\n      if (!el) { el = root.querySelector('.' + els) }\r\n      return el as HTMLElement;\r\n    }\r\n    return els;\r\n  }\r\n\r\n  /** true if widget (or grid) makes this item lazyLoad */\r\n  static lazyLoad(n: GridStackNode): boolean {\r\n    return n.lazyLoad || n.grid?.opts?.lazyLoad && n.lazyLoad !== false;\r\n  }\r\n\r\n  /** create a div with the given classes */\r\n  static createDiv(classes: string[], parent?: HTMLElement): HTMLElement {\r\n    const el = document.createElement('div');\r\n    classes.forEach(c => {if (c) el.classList.add(c)});\r\n    parent?.appendChild(el);\r\n    return el;\r\n  }\r\n\r\n  /** true if we should resize to content. strict=true when only 'sizeToContent:true' and not a number which lets user adjust */\r\n  static shouldSizeToContent(n: GridStackNode | undefined, strict = false): boolean {\r\n    return n?.grid && (strict ?\r\n      (n.sizeToContent === true || (n.grid.opts.sizeToContent === true && n.sizeToContent === undefined)) :\r\n      (!!n.sizeToContent || (n.grid.opts.sizeToContent && n.sizeToContent !== false)));\r\n  }\r\n\r\n  /** returns true if a and b overlap */\r\n  static isIntercepted(a: GridStackPosition, b: GridStackPosition): boolean {\r\n    return !(a.y >= b.y + b.h || a.y + a.h <= b.y || a.x + a.w <= b.x || a.x >= b.x + b.w);\r\n  }\r\n\r\n  /** returns true if a and b touch edges or corners */\r\n  static isTouching(a: GridStackPosition, b: GridStackPosition): boolean {\r\n    return Utils.isIntercepted(a, {x: b.x-0.5, y: b.y-0.5, w: b.w+1, h: b.h+1})\r\n  }\r\n\r\n  /** returns the area a and b overlap */\r\n  static areaIntercept(a: GridStackPosition, b: GridStackPosition): number {\r\n    const x0 = (a.x > b.x) ? a.x : b.x;\r\n    const x1 = (a.x+a.w < b.x+b.w) ? a.x+a.w : b.x+b.w;\r\n    if (x1 <= x0) return 0; // no overlap\r\n    const y0 = (a.y > b.y) ? a.y : b.y;\r\n    const y1 = (a.y+a.h < b.y+b.h) ? a.y+a.h : b.y+b.h;\r\n    if (y1 <= y0) return 0; // no overlap\r\n    return (x1-x0) * (y1-y0);\r\n  }\r\n\r\n  /** returns the area */\r\n  static area(a: GridStackPosition): number {\r\n    return a.w * a.h;\r\n  }\r\n\r\n  /**\r\n   * Sorts array of nodes\r\n   * @param nodes array to sort\r\n   * @param dir 1 for ascending, -1 for descending (optional)\r\n   **/\r\n  static sort(nodes: GridStackNode[], dir: 1 | -1 = 1): GridStackNode[] {\r\n    const und = 10000;\r\n    return nodes.sort((a, b) => {\r\n      const diffY = dir * ((a.y ?? und) - (b.y ?? und));\r\n      if (diffY === 0) return dir * ((a.x ?? und) - (b.x ?? und));\r\n      return diffY;\r\n    });\r\n  }\r\n\r\n  /** find an item by id */\r\n  static find(nodes: GridStackNode[], id: string): GridStackNode | undefined {\r\n    return id ? nodes.find(n => n.id === id) : undefined;\r\n  }\r\n\r\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n  static toBool(v: unknown): boolean {\r\n    if (typeof v === 'boolean') {\r\n      return v;\r\n    }\r\n    if (typeof v === 'string') {\r\n      v = v.toLowerCase();\r\n      return !(v === '' || v === 'no' || v === 'false' || v === '0');\r\n    }\r\n    return Boolean(v);\r\n  }\r\n\r\n  static toNumber(value: null | string): number {\r\n    return (value === null || value.length === 0) ? undefined : Number(value);\r\n  }\r\n\r\n  static parseHeight(val: numberOrString): HeightData {\r\n    let h: number;\r\n    let unit = 'px';\r\n    if (typeof val === 'string') {\r\n      if (val === 'auto' || val === '') h = 0;\r\n      else {\r\n        const match = val.match(/^(-[0-9]+\\.[0-9]+|[0-9]*\\.[0-9]+|-[0-9]+|[0-9]+)(px|em|rem|vh|vw|%|cm|mm)?$/);\r\n        if (!match) {\r\n          throw new Error(`Invalid height val = ${val}`);\r\n        }\r\n        unit = match[2] || 'px';\r\n        h = parseFloat(match[1]);\r\n      }\r\n    } else {\r\n      h = val;\r\n    }\r\n    return { h, unit };\r\n  }\r\n\r\n  /** copies unset fields in target to use the given default sources values */\r\n  // eslint-disable-next-line\r\n  static defaults(target, ...sources): {} {\r\n\r\n    sources.forEach(source => {\r\n      for (const key in source) {\r\n        if (!source.hasOwnProperty(key)) return;\r\n        if (target[key] === null || target[key] === undefined) {\r\n          target[key] = source[key];\r\n        } else if (typeof source[key] === 'object' && typeof target[key] === 'object') {\r\n          // property is an object, recursively add it's field over... #1373\r\n          this.defaults(target[key], source[key]);\r\n        }\r\n      }\r\n    });\r\n\r\n    return target;\r\n  }\r\n\r\n  /** given 2 objects return true if they have the same values. Checks for Object {} having same fields and values (just 1 level down) */\r\n  static same(a: unknown, b: unknown): boolean {\r\n    if (typeof a !== 'object')  return a == b;\r\n    if (typeof a !== typeof b) return false;\r\n    // else we have object, check just 1 level deep for being same things...\r\n    if (Object.keys(a).length !== Object.keys(b).length) return false;\r\n    for (const key in a) {\r\n      if (a[key] !== b[key]) return false;\r\n    }\r\n    return true;\r\n  }\r\n\r\n  /** copies over b size & position (GridStackPosition), and optionally min/max as well */\r\n  static copyPos(a: GridStackWidget, b: GridStackWidget, doMinMax = false): GridStackWidget {\r\n    if (b.x !== undefined) a.x = b.x;\r\n    if (b.y !== undefined) a.y = b.y;\r\n    if (b.w !== undefined) a.w = b.w;\r\n    if (b.h !== undefined) a.h = b.h;\r\n    if (doMinMax) {\r\n      if (b.minW) a.minW = b.minW;\r\n      if (b.minH) a.minH = b.minH;\r\n      if (b.maxW) a.maxW = b.maxW;\r\n      if (b.maxH) a.maxH = b.maxH;\r\n    }\r\n    return a;\r\n  }\r\n\r\n  /** true if a and b has same size & position */\r\n  static samePos(a: GridStackPosition, b: GridStackPosition): boolean {\r\n    return a && b && a.x === b.x && a.y === b.y && (a.w || 1) === (b.w || 1) && (a.h || 1) === (b.h || 1);\r\n  }\r\n\r\n  /** given a node, makes sure it's min/max are valid */\r\n  static sanitizeMinMax(node: GridStackNode) {\r\n    // remove 0, undefine, null\r\n    if (!node.minW) { delete node.minW; }\r\n    if (!node.minH) { delete node.minH; }\r\n    if (!node.maxW) { delete node.maxW; }\r\n    if (!node.maxH) { delete node.maxH; }\r\n  }\r\n\r\n  /** removes field from the first object if same as the second objects (like diffing) and internal '_' for saving */\r\n  static removeInternalAndSame(a: unknown, b: unknown):void {\r\n    if (typeof a !== 'object' || typeof b !== 'object') return;\r\n    for (let key in a) {\r\n      const aVal = a[key];\r\n      const bVal = b[key];\r\n      if (key[0] === '_' || aVal === bVal) {\r\n        delete a[key]\r\n      } else if (aVal && typeof aVal === 'object' && bVal !== undefined) {\r\n        Utils.removeInternalAndSame(aVal, bVal);\r\n        if (!Object.keys(aVal).length) { delete a[key] }\r\n      }\r\n    }\r\n  }\r\n\r\n  /** removes internal fields '_' and default values for saving */\r\n  static removeInternalForSave(n: GridStackNode, removeEl = true): void {\r\n    for (let key in n) { if (key[0] === '_' || n[key] === null || n[key] === undefined ) delete n[key]; }\r\n    delete n.grid;\r\n    if (removeEl) delete n.el;\r\n    // delete default values (will be re-created on read)\r\n    if (!n.autoPosition) delete n.autoPosition;\r\n    if (!n.noResize) delete n.noResize;\r\n    if (!n.noMove) delete n.noMove;\r\n    if (!n.locked) delete n.locked;\r\n    if (n.w === 1 || n.w === n.minW) delete n.w;\r\n    if (n.h === 1 || n.h === n.minH) delete n.h;\r\n  }\r\n\r\n  /** return the closest parent (or itself) matching the given class */\r\n  // static closestUpByClass(el: HTMLElement, name: string): HTMLElement {\r\n  //   while (el) {\r\n  //     if (el.classList.contains(name)) return el;\r\n  //     el = el.parentElement\r\n  //   }\r\n  //   return null;\r\n  // }\r\n\r\n  /** delay calling the given function for given delay, preventing new calls from happening while waiting */\r\n  static throttle(func: () => void, delay: number): () => void {\r\n    let isWaiting = false;\r\n    return (...args) => {\r\n      if (!isWaiting) {\r\n        isWaiting = true;\r\n        setTimeout(() => { func(...args); isWaiting = false; }, delay);\r\n      }\r\n    }\r\n  }\r\n\r\n  static removePositioningStyles(el: HTMLElement): void {\r\n    const style = el.style;\r\n    if (style.position) {\r\n      style.removeProperty('position');\r\n    }\r\n    if (style.left) {\r\n      style.removeProperty('left');\r\n    }\r\n    if (style.top) {\r\n      style.removeProperty('top');\r\n    }\r\n    if (style.width) {\r\n      style.removeProperty('width');\r\n    }\r\n    if (style.height) {\r\n      style.removeProperty('height');\r\n    }\r\n  }\r\n\r\n  /** @internal returns the passed element if scrollable, else the closest parent that will, up to the entire document scrolling element */\r\n  static getScrollElement(el?: HTMLElement): HTMLElement {\r\n    if (!el) return document.scrollingElement as HTMLElement || document.documentElement; // IE support\r\n    const style = getComputedStyle(el);\r\n    const overflowRegex = /(auto|scroll)/;\r\n\r\n    if (overflowRegex.test(style.overflow + style.overflowY)) {\r\n      return el;\r\n    } else {\r\n      return this.getScrollElement(el.parentElement);\r\n    }\r\n  }\r\n\r\n  /** @internal */\r\n  static updateScrollPosition(el: HTMLElement, position: {top: number}, distance: number): void {\r\n    // is widget in view?\r\n    const rect = el.getBoundingClientRect();\r\n    const innerHeightOrClientHeight = (window.innerHeight || document.documentElement.clientHeight);\r\n    if (rect.top < 0 ||\r\n      rect.bottom > innerHeightOrClientHeight\r\n    ) {\r\n      // set scrollTop of first parent that scrolls\r\n      // if parent is larger than el, set as low as possible\r\n      // to get entire widget on screen\r\n      const offsetDiffDown = rect.bottom - innerHeightOrClientHeight;\r\n      const offsetDiffUp = rect.top;\r\n      const scrollEl = this.getScrollElement(el);\r\n      if (scrollEl !== null) {\r\n        const prevScroll = scrollEl.scrollTop;\r\n        if (rect.top < 0 && distance < 0) {\r\n          // moving up\r\n          if (el.offsetHeight > innerHeightOrClientHeight) {\r\n            scrollEl.scrollTop += distance;\r\n          } else {\r\n            scrollEl.scrollTop += Math.abs(offsetDiffUp) > Math.abs(distance) ? distance : offsetDiffUp;\r\n          }\r\n        } else if (distance > 0) {\r\n          // moving down\r\n          if (el.offsetHeight > innerHeightOrClientHeight) {\r\n            scrollEl.scrollTop += distance;\r\n          } else {\r\n            scrollEl.scrollTop += offsetDiffDown > distance ? distance : offsetDiffDown;\r\n          }\r\n        }\r\n        // move widget y by amount scrolled\r\n        position.top += scrollEl.scrollTop - prevScroll;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * @internal Function used to scroll the page.\r\n   *\r\n   * @param event `MouseEvent` that triggers the resize\r\n   * @param el `HTMLElement` that's being resized\r\n   * @param distance Distance from the V edges to start scrolling\r\n   */\r\n  static updateScrollResize(event: MouseEvent, el: HTMLElement, distance: number): void {\r\n    const scrollEl = this.getScrollElement(el);\r\n    const height = scrollEl.clientHeight;\r\n    // #1727 event.clientY is relative to viewport, so must compare this against position of scrollEl getBoundingClientRect().top\r\n    // #1745 Special situation if scrollEl is document 'html': here browser spec states that\r\n    // clientHeight is height of viewport, but getBoundingClientRect() is rectangle of html element;\r\n    // this discrepancy arises because in reality scrollbar is attached to viewport, not html element itself.\r\n    const offsetTop = (scrollEl === this.getScrollElement()) ? 0 : scrollEl.getBoundingClientRect().top;\r\n    const pointerPosY = event.clientY - offsetTop;\r\n    const top = pointerPosY < distance;\r\n    const bottom = pointerPosY > height - distance;\r\n\r\n    if (top) {\r\n      // This also can be done with a timeout to keep scrolling while the mouse is\r\n      // in the scrolling zone. (will have smoother behavior)\r\n      scrollEl.scrollBy({ behavior: 'smooth', top: pointerPosY - distance});\r\n    } else if (bottom) {\r\n      scrollEl.scrollBy({ behavior: 'smooth', top: distance - (height - pointerPosY)});\r\n    }\r\n  }\r\n\r\n  /** single level clone, returning a new object with same top fields. This will share sub objects and arrays */\r\n  static clone<T>(obj: T): T {\r\n    if (obj === null || obj === undefined || typeof(obj) !== 'object') {\r\n      return obj;\r\n    }\r\n    // return Object.assign({}, obj);\r\n    if (obj instanceof Array) {\r\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n      return [...obj] as any;\r\n    }\r\n    return {...obj};\r\n  }\r\n\r\n  /**\r\n   * Recursive clone version that returns a full copy, checking for nested objects and arrays ONLY.\r\n   * Note: this will use as-is any key starting with double __ (and not copy inside) some lib have circular dependencies.\r\n   */\r\n  static cloneDeep<T>(obj: T): T {\r\n    // list of fields we will skip during cloneDeep (nested objects, other internal)\r\n    const skipFields = ['parentGrid', 'el', 'grid', 'subGrid', 'engine'];\r\n    // return JSON.parse(JSON.stringify(obj)); // doesn't work with date format ?\r\n    const ret = Utils.clone(obj);\r\n    for (const key in ret) {\r\n      // NOTE: we don't support function/circular dependencies so skip those properties for now...\r\n      if (ret.hasOwnProperty(key) && typeof(ret[key]) === 'object' && key.substring(0, 2) !== '__' && !skipFields.find(k => k === key)) {\r\n        ret[key] = Utils.cloneDeep(obj[key]);\r\n      }\r\n    }\r\n    return ret;\r\n  }\r\n\r\n  /** deep clone the given HTML node, removing teh unique id field */\r\n  public static cloneNode(el: HTMLElement): HTMLElement {\r\n    const node = el.cloneNode(true) as HTMLElement;\r\n    node.removeAttribute('id');\r\n    return node;\r\n  }\r\n\r\n  public static appendTo(el: HTMLElement, parent: string | HTMLElement): void {\r\n    let parentNode: HTMLElement;\r\n    if (typeof parent === 'string') {\r\n      parentNode = Utils.getElement(parent);\r\n    } else {\r\n      parentNode = parent;\r\n    }\r\n    if (parentNode) {\r\n      parentNode.appendChild(el);\r\n    }\r\n  }\r\n\r\n  // public static setPositionRelative(el: HTMLElement): void {\r\n  //   if (!(/^(?:r|a|f)/).test(getComputedStyle(el).position)) {\r\n  //     el.style.position = \"relative\";\r\n  //   }\r\n  // }\r\n\r\n  public static addElStyles(el: HTMLElement, styles: { [prop: string]: string | string[] }): void {\r\n    if (styles instanceof Object) {\r\n      for (const s in styles) {\r\n        if (styles.hasOwnProperty(s)) {\r\n          if (Array.isArray(styles[s])) {\r\n            // support fallback value\r\n            (styles[s] as string[]).forEach(val => {\r\n              el.style[s] = val;\r\n            });\r\n          } else {\r\n            el.style[s] = styles[s];\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  public static initEvent<T>(e: DragEvent | MouseEvent, info: { type: string; target?: EventTarget }): T {\r\n    const evt = { type: info.type };\r\n    const obj = {\r\n      button: 0,\r\n      which: 0,\r\n      buttons: 1,\r\n      bubbles: true,\r\n      cancelable: true,\r\n      target: info.target ? info.target : e.target\r\n    };\r\n    ['altKey','ctrlKey','metaKey','shiftKey'].forEach(p => evt[p] = e[p]); // keys\r\n    ['pageX','pageY','clientX','clientY','screenX','screenY'].forEach(p => evt[p] = e[p]); // point info\r\n    return {...evt, ...obj} as unknown as T;\r\n  }\r\n\r\n  /** copies the MouseEvent (or convert Touch) properties and sends it as another event to the given target */\r\n  public static simulateMouseEvent(e: MouseEvent | Touch, simulatedType: string, target?: EventTarget): void {\r\n    const me = e as MouseEvent;\r\n    const simulatedEvent = new MouseEvent(simulatedType, {\r\n      bubbles: true,\r\n      composed: true,\r\n      cancelable: true,\r\n      view: window,\r\n      detail: 1,\r\n      screenX: e.screenX,\r\n      screenY: e.screenY,\r\n      clientX: e.clientX,\r\n      clientY: e.clientY,\r\n      ctrlKey: me.ctrlKey??false,\r\n      altKey: me.altKey??false,\r\n      shiftKey: me.shiftKey??false,\r\n      metaKey: me.metaKey??false,\r\n      button: 0,\r\n      relatedTarget: e.target\r\n    });\r\n\r\n    (target || e.target).dispatchEvent(simulatedEvent);\r\n  }\r\n\r\n  /**\r\n   * defines an element that is used to get the offset and scale from grid transforms\r\n   * returns the scale and offsets from said element\r\n  */\r\n  public static getValuesFromTransformedElement(parent: HTMLElement): DragTransform {\r\n    const transformReference = document.createElement('div');\r\n    Utils.addElStyles(transformReference, {\r\n      opacity: '0',\r\n      position: 'fixed',\r\n      top: 0 + 'px',\r\n      left: 0 + 'px',\r\n      width: '1px',\r\n      height: '1px',\r\n      zIndex: '-999999',\r\n    });\r\n    parent.appendChild(transformReference);\r\n    const transformValues = transformReference.getBoundingClientRect();\r\n    parent.removeChild(transformReference);\r\n    transformReference.remove();\r\n    return {\r\n      xScale: 1 / transformValues.width,\r\n      yScale: 1 / transformValues.height,\r\n      xOffset: transformValues.left,\r\n      yOffset: transformValues.top,\r\n    }\r\n  }\r\n\r\n  /** swap the given object 2 field values */\r\n  public static swap(o: unknown, a: string, b: string): void {\r\n    if (!o) return;\r\n    const tmp = o[a]; o[a] = o[b]; o[b] = tmp;\r\n  }\r\n\r\n  /** returns true if event is inside the given element rectangle */\r\n  // Note: Safari Mac has null event.relatedTarget which causes #1684 so check if DragEvent is inside the coordinates instead\r\n  //    this.el.contains(event.relatedTarget as HTMLElement)\r\n  // public static inside(e: MouseEvent, el: HTMLElement): boolean {\r\n  //   // srcElement, toElement, target: all set to placeholder when leaving simple grid, so we can't use that (Chrome)\r\n  //   const target: HTMLElement = e.relatedTarget || (e as any).fromElement;\r\n  //   if (!target) {\r\n  //     const { bottom, left, right, top } = el.getBoundingClientRect();\r\n  //     return (e.x < right && e.x > left && e.y < bottom && e.y > top);\r\n  //   }\r\n  //   return el.contains(target);\r\n  // }\r\n\r\n  /** true if the item can be rotated (checking for prop, not space available) */\r\n  public static canBeRotated(n: GridStackNode): boolean {\r\n    return !(!n || n.w === n.h || n.locked || n.noResize || n.grid?.opts.disableResize || (n.minW && n.minW === n.maxW) || (n.minH && n.minH === n.maxH));\r\n  }\r\n}", "/**\n * gridstack-engine.ts 12.2.2\n * Copyright (c) 2021-2024  <PERSON> - see GridStack root license\n */\n\nimport { Utils } from './utils';\nimport { GridStackNode, ColumnOptions, GridStackPosition, GridStackMoveOpts, SaveFcn, CompactOptions } from './types';\n\n/** callback to update the DOM attributes since this class is generic (no HTML or other info) for items that changed - see _notify() */\ntype OnChangeCB = (nodes: GridStackNode[]) => void;\n\n/** options used during creation - similar to GridStackOptions */\nexport interface GridStackEngineOptions {\n  column?: number;\n  maxRow?: number;\n  float?: boolean;\n  nodes?: GridStackNode[];\n  onChange?: OnChangeCB;\n}\n\n/**\n * Defines the GridStack engine that does most no DOM grid manipulation.\n * See GridStack methods and vars for descriptions.\n *\n * NOTE: values should not be modified directly - call the main GridStack API instead\n */\nexport class GridStackEngine {\n  public column: number;\n  public maxRow: number;\n  public nodes: GridStackNode[];\n  public addedNodes: GridStackNode[] = [];\n  public removedNodes: GridStackNode[] = [];\n  public batchMode: boolean;\n  public defaultColumn = 12;\n  /** @internal callback to update the DOM attributes */\n  protected onChange: OnChangeCB;\n  /** @internal */\n  protected _float: boolean;\n  /** @internal */\n  protected _prevFloat: boolean;\n  /** @internal cached layouts of difference column count so we can restore back (eg 12 -> 1 -> 12) */\n  protected _layouts?: GridStackNode[][]; // maps column # to array of values nodes\n  /** @internal set during loading (which is sorted) so item gets added AFTER collision nodes */\n  public _loading?: boolean\n  /** @internal true while we are resizing widgets during column resize to skip certain parts */\n  protected _inColumnResize?: boolean;\n  /** true when grid.load() already cached the layout and can skip out of bound caching info */\n  public skipCacheUpdate?: boolean;\n  /** @internal true if we have some items locked */\n  protected _hasLocked: boolean;\n  /** @internal unique global internal _id counter */\n  public static _idSeq = 0;\n\n  public constructor(opts: GridStackEngineOptions = {}) {\n    this.column = opts.column || this.defaultColumn;\n    if (this.column > this.defaultColumn) this.defaultColumn = this.column;\n    this.maxRow = opts.maxRow;\n    this._float = opts.float;\n    this.nodes = opts.nodes || [];\n    this.onChange = opts.onChange;\n  }\n\n  public batchUpdate(flag = true, doPack = true): GridStackEngine {\n    if (!!this.batchMode === flag) return this;\n    this.batchMode = flag;\n    if (flag) {\n      this._prevFloat = this._float;\n      this._float = true; // let things go anywhere for now... will restore and possibly reposition later\n      this.cleanNodes();\n      this.saveInitial(); // since begin update (which is called multiple times) won't do this\n    } else {\n      this._float = this._prevFloat;\n      delete this._prevFloat;\n      if (doPack) this._packNodes();\n      this._notify();\n    }\n    return this;\n  }\n\n  // use entire row for hitting area (will use bottom reverse sorted first) if we not actively moving DOWN and didn't already skip\n  protected _useEntireRowArea(node: GridStackNode, nn: GridStackPosition): boolean {\n    return (!this.float || this.batchMode && !this._prevFloat) && !this._hasLocked && (!node._moving || node._skipDown || nn.y <= node.y);\n  }\n\n  /** @internal fix collision on given 'node', going to given new location 'nn', with optional 'collide' node already found.\n   * return true if we moved. */\n  protected _fixCollisions(node: GridStackNode, nn = node, collide?: GridStackNode, opt: GridStackMoveOpts = {}): boolean {\n    this.sortNodes(-1); // from last to first, so recursive collision move items in the right order\n\n    collide = collide || this.collide(node, nn); // REAL area collide for swap and skip if none...\n    if (!collide) return false;\n\n    // swap check: if we're actively moving in gravity mode, see if we collide with an object the same size\n    if (node._moving && !opt.nested && !this.float) {\n      if (this.swap(node, collide)) return true;\n    }\n\n    // during while() collisions MAKE SURE to check entire row so larger items don't leap frog small ones (push them all down starting last in grid)\n    let area = nn;\n    if (!this._loading && this._useEntireRowArea(node, nn)) {\n      area = {x: 0, w: this.column, y: nn.y, h: nn.h};\n      collide = this.collide(node, area, opt.skip); // force new hit\n    }\n\n    let didMove = false;\n    const newOpt: GridStackMoveOpts = {nested: true, pack: false};\n    let counter = 0;\n    while (collide = collide || this.collide(node, area, opt.skip)) { // could collide with more than 1 item... so repeat for each\n      if (counter++ > this.nodes.length * 2) {\n        throw new Error(\"Infinite collide check\");\n      }\n      let moved: boolean;\n      // if colliding with a locked item OR loading (move after) OR moving down with top gravity (and collide could move up) -> skip past the collide,\n      // but remember that skip down so we only do this once (and push others otherwise).\n      if (collide.locked || this._loading || node._moving && !node._skipDown && nn.y > node.y && !this.float &&\n        // can take space we had, or before where we're going\n        (!this.collide(collide, {...collide, y: node.y}, node) || !this.collide(collide, {...collide, y: nn.y - collide.h}, node))) {\n\n        node._skipDown = (node._skipDown || nn.y > node.y);\n        const newNN = {...nn, y: collide.y + collide.h, ...newOpt};\n        // pretent we moved to where we are now so we can continue any collision checks #2492\n        moved = this._loading && Utils.samePos(node, newNN) ? true : this.moveNode(node, newNN);\n\n        if ((collide.locked || this._loading) && moved) {\n          Utils.copyPos(nn, node); // moving after lock become our new desired location\n        } else if (!collide.locked && moved && opt.pack) {\n          // we moved after and will pack: do it now and keep the original drop location, but past the old collide to see what else we might push way\n          this._packNodes();\n          nn.y = collide.y + collide.h;\n          Utils.copyPos(node, nn);\n        }\n        didMove = didMove || moved;\n      } else {\n        // move collide down *after* where we will be, ignoring where we are now (don't collide with us)\n        moved = this.moveNode(collide, {...collide, y: nn.y + nn.h, skip: node, ...newOpt});\n      }\n\n      if (!moved) return didMove; // break inf loop if we couldn't move after all (ex: maxRow, fixed)\n\n      collide = undefined;\n    }\n    return didMove;\n  }\n\n  /** return the nodes that intercept the given node. Optionally a different area can be used, as well as a second node to skip */\n  public collide(skip: GridStackNode, area = skip, skip2?: GridStackNode): GridStackNode | undefined {\n    const skipId = skip._id;\n    const skip2Id = skip2?._id;\n    return this.nodes.find(n => n._id !== skipId && n._id !== skip2Id && Utils.isIntercepted(n, area));\n  }\n  public collideAll(skip: GridStackNode, area = skip, skip2?: GridStackNode): GridStackNode[] {\n    const skipId = skip._id;\n    const skip2Id = skip2?._id;\n    return this.nodes.filter(n => n._id !== skipId && n._id !== skip2Id && Utils.isIntercepted(n, area));\n  }\n\n  /** does a pixel coverage collision based on where we started, returning the node that has the most coverage that is >50% mid line */\n  protected directionCollideCoverage(node: GridStackNode, o: GridStackMoveOpts, collides: GridStackNode[]): GridStackNode | undefined {\n    if (!o.rect || !node._rect) return;\n    const r0 = node._rect; // where started\n    const r = {...o.rect}; // where we are\n\n    // update dragged rect to show where it's coming from (above or below, etc...)\n    if (r.y > r0.y) {\n      r.h += r.y - r0.y;\n      r.y = r0.y;\n    } else {\n      r.h += r0.y - r.y;\n    }\n    if (r.x > r0.x) {\n      r.w += r.x - r0.x;\n      r.x = r0.x;\n    } else {\n      r.w += r0.x - r.x;\n    }\n\n    let collide: GridStackNode;\n    let overMax = 0.5; // need >50%\n    for (let n of collides) {\n      if (n.locked || !n._rect) {\n        break;\n      }\n      const r2 = n._rect; // overlapping target\n      let yOver = Number.MAX_VALUE, xOver = Number.MAX_VALUE;\n      // depending on which side we started from, compute the overlap % of coverage\n      // (ex: from above/below we only compute the max horizontal line coverage)\n      if (r0.y < r2.y) { // from above\n        yOver = ((r.y + r.h) - r2.y) / r2.h;\n      } else if (r0.y + r0.h > r2.y + r2.h) { // from below\n        yOver = ((r2.y + r2.h) - r.y) / r2.h;\n      }\n      if (r0.x < r2.x) { // from the left\n        xOver = ((r.x + r.w) - r2.x) / r2.w;\n      } else if (r0.x + r0.w > r2.x + r2.w) { // from the right\n        xOver = ((r2.x + r2.w) - r.x) / r2.w;\n      }\n      const over = Math.min(xOver, yOver);\n      if (over > overMax) {\n        overMax = over;\n        collide = n;\n      }\n    }\n    o.collide = collide; // save it so we don't have to find it again\n    return collide;\n  }\n\n  /** does a pixel coverage returning the node that has the most coverage by area */\n  /*\n  protected collideCoverage(r: GridStackPosition, collides: GridStackNode[]): {collide: GridStackNode, over: number} {\n    const collide: GridStackNode;\n    const overMax = 0;\n    collides.forEach(n => {\n      if (n.locked || !n._rect) return;\n      const over = Utils.areaIntercept(r, n._rect);\n      if (over > overMax) {\n        overMax = over;\n        collide = n;\n      }\n    });\n    return {collide, over: overMax};\n  }\n  */\n\n  /** called to cache the nodes pixel rectangles used for collision detection during drag */\n  public cacheRects(w: number, h: number, top: number, right: number, bottom: number, left: number): GridStackEngine\n  {\n    this.nodes.forEach(n =>\n      n._rect = {\n        y: n.y * h + top,\n        x: n.x * w + left,\n        w: n.w * w - left - right,\n        h: n.h * h - top - bottom\n      }\n    );\n    return this;\n  }\n\n  /** called to possibly swap between 2 nodes (same size or column, not locked, touching), returning true if successful */\n  public swap(a: GridStackNode, b: GridStackNode): boolean | undefined {\n    if (!b || b.locked || !a || a.locked) return false;\n\n    function _doSwap(): true { // assumes a is before b IFF they have different height (put after rather than exact swap)\n      const x = b.x, y = b.y;\n      b.x = a.x; b.y = a.y; // b -> a position\n      if (a.h != b.h) {\n        a.x = x; a.y = b.y + b.h; // a -> goes after b\n      } else if (a.w != b.w) {\n        a.x = b.x + b.w; a.y = y; // a -> goes after b\n      } else {\n        a.x = x; a.y = y; // a -> old b position\n      }\n      a._dirty = b._dirty = true;\n      return true;\n    }\n    let touching: boolean; // remember if we called it (vs undefined)\n\n    // same size and same row or column, and touching\n    if (a.w === b.w && a.h === b.h && (a.x === b.x || a.y === b.y) && (touching = Utils.isTouching(a, b)))\n      return _doSwap();\n    if (touching === false) return; // IFF ran test and fail, bail out\n\n    // check for taking same columns (but different height) and touching\n    if (a.w === b.w && a.x === b.x && (touching || (touching = Utils.isTouching(a, b)))) {\n      if (b.y < a.y) { const t = a; a = b; b = t; } // swap a <-> b vars so a is first\n      return _doSwap();\n    }\n    if (touching === false) return;\n\n    // check if taking same row (but different width) and touching\n    if (a.h === b.h && a.y === b.y && (touching || (touching = Utils.isTouching(a, b)))) {\n      if (b.x < a.x) { const t = a; a = b; b = t; } // swap a <-> b vars so a is first\n      return _doSwap();\n    }\n    return false;\n  }\n\n  public isAreaEmpty(x: number, y: number, w: number, h: number): boolean {\n    const nn: GridStackNode = {x: x || 0, y: y || 0, w: w || 1, h: h || 1};\n    return !this.collide(nn);\n  }\n\n  /** re-layout grid items to reclaim any empty space - optionally keeping the sort order exactly the same ('list' mode) vs truly finding an empty spaces */\n  public compact(layout: CompactOptions = 'compact', doSort = true): GridStackEngine {\n    if (this.nodes.length === 0) return this;\n    if (doSort) this.sortNodes();\n    const wasBatch = this.batchMode;\n    if (!wasBatch) this.batchUpdate();\n    const wasColumnResize = this._inColumnResize;\n    if (!wasColumnResize) this._inColumnResize = true; // faster addNode()\n    const copyNodes = this.nodes;\n    this.nodes = []; // pretend we have no nodes to conflict layout to start with...\n    copyNodes.forEach((n, index, list) => {\n      let after: GridStackNode;\n      if (!n.locked) {\n        n.autoPosition = true;\n        if (layout === 'list' && index) after = list[index - 1];\n      }\n      this.addNode(n, false, after); // 'false' for add event trigger\n    });\n    if (!wasColumnResize) delete this._inColumnResize;\n    if (!wasBatch) this.batchUpdate(false);\n    return this;\n  }\n\n  /** enable/disable floating widgets (default: `false`) See [example](http://gridstackjs.com/demo/float.html) */\n  public set float(val: boolean) {\n    if (this._float === val) return;\n    this._float = val || false;\n    if (!val) {\n      this._packNodes()._notify();\n    }\n  }\n\n  /** float getter method */\n  public get float(): boolean { return this._float || false; }\n\n  /** sort the nodes array from first to last, or reverse. Called during collision/placement to force an order */\n  public sortNodes(dir: 1 | -1 = 1): GridStackEngine {\n    this.nodes = Utils.sort(this.nodes, dir);\n    return this;\n  }\n\n  /** @internal called to top gravity pack the items back OR revert back to original Y positions when floating */\n  protected _packNodes(): GridStackEngine {\n    if (this.batchMode) { return this; }\n    this.sortNodes(); // first to last\n\n    if (this.float) {\n      // restore original Y pos\n      this.nodes.forEach(n => {\n        if (n._updating || n._orig === undefined || n.y === n._orig.y) return;\n        let newY = n.y;\n        while (newY > n._orig.y) {\n          --newY;\n          const collide = this.collide(n, {x: n.x, y: newY, w: n.w, h: n.h});\n          if (!collide) {\n            n._dirty = true;\n            n.y = newY;\n          }\n        }\n      });\n    } else {\n      // top gravity pack\n      this.nodes.forEach((n, i) => {\n        if (n.locked) return;\n        while (n.y > 0) {\n          const newY = i === 0 ? 0 : n.y - 1;\n          const canBeMoved = i === 0 || !this.collide(n, {x: n.x, y: newY, w: n.w, h: n.h});\n          if (!canBeMoved) break;\n          // Note: must be dirty (from last position) for GridStack::OnChange CB to update positions\n          // and move items back. The user 'change' CB should detect changes from the original\n          // starting position instead.\n          n._dirty = (n.y !== newY);\n          n.y = newY;\n        }\n      });\n    }\n    return this;\n  }\n\n  /**\n   * given a random node, makes sure it's coordinates/values are valid in the current grid\n   * @param node to adjust\n   * @param resizing if out of bound, resize down or move into the grid to fit ?\n   */\n  public prepareNode(node: GridStackNode, resizing?: boolean): GridStackNode {\n    node._id = node._id ?? GridStackEngine._idSeq++;\n\n    // make sure USER supplied id are unique in our list, else assign a new one as it will create issues during load/update/etc...\n    const id = node.id;\n    if (id) {\n      let count = 1; // append nice _n rather than some random number\n      while (this.nodes.find(n => n.id === node.id && n !== node)) {\n        node.id = id + '_' + (count++);\n      }\n    }\n\n    // if we're missing position, have the grid position us automatically (before we set them to 0,0)\n    if (node.x === undefined || node.y === undefined || node.x === null || node.y === null) {\n      node.autoPosition = true;\n    }\n\n    // assign defaults for missing required fields\n    const defaults: GridStackNode = { x: 0, y: 0, w: 1, h: 1};\n    Utils.defaults(node, defaults);\n\n    if (!node.autoPosition) { delete node.autoPosition; }\n    if (!node.noResize) { delete node.noResize; }\n    if (!node.noMove) { delete node.noMove; }\n    Utils.sanitizeMinMax(node);\n\n    // check for NaN (in case messed up strings were passed. can't do parseInt() || defaults.x above as 0 is valid #)\n    if (typeof node.x == 'string') { node.x = Number(node.x); }\n    if (typeof node.y == 'string') { node.y = Number(node.y); }\n    if (typeof node.w == 'string') { node.w = Number(node.w); }\n    if (typeof node.h == 'string') { node.h = Number(node.h); }\n    if (isNaN(node.x)) { node.x = defaults.x; node.autoPosition = true; }\n    if (isNaN(node.y)) { node.y = defaults.y; node.autoPosition = true; }\n    if (isNaN(node.w)) { node.w = defaults.w; }\n    if (isNaN(node.h)) { node.h = defaults.h; }\n\n    this.nodeBoundFix(node, resizing);\n    return node;\n  }\n\n  /** part2 of preparing a node to fit inside our grid - checks for x,y,w from grid dimensions */\n  public nodeBoundFix(node: GridStackNode, resizing?: boolean): GridStackEngine {\n\n    const before = node._orig || Utils.copyPos({}, node);\n\n    if (node.maxW) { node.w = Math.min(node.w || 1, node.maxW); }\n    if (node.maxH) { node.h = Math.min(node.h || 1, node.maxH); }\n    if (node.minW) { node.w = Math.max(node.w || 1, node.minW); }\n    if (node.minH) { node.h = Math.max(node.h || 1, node.minH); }\n\n    // if user loaded a larger than allowed widget for current # of columns,\n    // remember it's position & width so we can restore back (1 -> 12 column) #1655 #1985\n    // IFF we're not in the middle of column resizing!\n    const saveOrig = (node.x || 0) + (node.w || 1) > this.column;\n    if (saveOrig && this.column < this.defaultColumn && !this._inColumnResize && !this.skipCacheUpdate && node._id != null  && this.findCacheLayout(node, this.defaultColumn) === -1) {\n      const copy = {...node}; // need _id + positions\n      if (copy.autoPosition || copy.x === undefined) { delete copy.x; delete copy.y; }\n      else copy.x = Math.min(this.defaultColumn - 1, copy.x);\n      copy.w = Math.min(this.defaultColumn, copy.w || 1);\n      this.cacheOneLayout(copy, this.defaultColumn);\n    }\n    \n    if (node.w > this.column) {\n      node.w = this.column;\n    } else if (node.w < 1) {\n      node.w = 1;\n    }\n\n    if (this.maxRow && node.h > this.maxRow) {\n      node.h = this.maxRow;\n    } else if (node.h < 1) {\n      node.h = 1;\n    }\n\n    if (node.x < 0) {\n      node.x = 0;\n    }\n    if (node.y < 0) {\n      node.y = 0;\n    }\n\n    if (node.x + node.w > this.column) {\n      if (resizing) {\n        node.w = this.column - node.x;\n      } else {\n        node.x = this.column - node.w;\n      }\n    }\n    if (this.maxRow && node.y + node.h > this.maxRow) {\n      if (resizing) {\n        node.h = this.maxRow - node.y;\n      } else {\n        node.y = this.maxRow - node.h;\n      }\n    }\n\n    if (!Utils.samePos(node, before)) {\n      node._dirty = true;\n    }\n\n    return this;\n  }\n\n  /** returns a list of modified nodes from their original values */\n  public getDirtyNodes(verify?: boolean): GridStackNode[] {\n    // compare original x,y,w,h instead as _dirty can be a temporary state\n    if (verify) {\n      return this.nodes.filter(n => n._dirty && !Utils.samePos(n, n._orig));\n    }\n    return this.nodes.filter(n => n._dirty);\n  }\n\n  /** @internal call this to call onChange callback with dirty nodes so DOM can be updated */\n  protected _notify(removedNodes?: GridStackNode[]): GridStackEngine {\n    if (this.batchMode || !this.onChange) return this;\n    const dirtyNodes = (removedNodes || []).concat(this.getDirtyNodes());\n    this.onChange(dirtyNodes);\n    return this;\n  }\n\n  /** @internal remove dirty and last tried info */\n  public cleanNodes(): GridStackEngine {\n    if (this.batchMode) return this;\n    this.nodes.forEach(n => {\n      delete n._dirty;\n      delete n._lastTried;\n    });\n    return this;\n  }\n\n  /** @internal called to save initial position/size to track real dirty state.\n   * Note: should be called right after we call change event (so next API is can detect changes)\n   * as well as right before we start move/resize/enter (so we can restore items to prev values) */\n  public saveInitial(): GridStackEngine {\n    this.nodes.forEach(n => {\n      n._orig = Utils.copyPos({}, n);\n      delete n._dirty;\n    });\n    this._hasLocked = this.nodes.some(n => n.locked);\n    return this;\n  }\n\n  /** @internal restore all the nodes back to initial values (called when we leave) */\n  public restoreInitial(): GridStackEngine {\n    this.nodes.forEach(n => {\n      if (!n._orig || Utils.samePos(n, n._orig)) return;\n      Utils.copyPos(n, n._orig);\n      n._dirty = true;\n    });\n    this._notify();\n    return this;\n  }\n\n  /** find the first available empty spot for the given node width/height, updating the x,y attributes. return true if found.\n   * optionally you can pass your own existing node list and column count, otherwise defaults to that engine data.\n   * Optionally pass a widget to start search AFTER, meaning the order will remain the same but possibly have empty slots we skipped\n   */\n  public findEmptyPosition(node: GridStackNode, nodeList = this.nodes, column = this.column, after?: GridStackNode): boolean {\n    const start = after ? after.y * column + (after.x + after.w) : 0;\n    let found = false;\n    for (let i = start; !found; ++i) {\n      const x = i % column;\n      const y = Math.floor(i / column);\n      if (x + node.w > column) {\n        continue;\n      }\n      const box = {x, y, w: node.w, h: node.h};\n      if (!nodeList.find(n => Utils.isIntercepted(box, n))) {\n        if (node.x !== x || node.y !== y) node._dirty = true;\n        node.x = x;\n        node.y = y;\n        delete node.autoPosition;\n        found = true;\n      }\n    }\n    return found;\n  }\n\n  /** call to add the given node to our list, fixing collision and re-packing */\n  public addNode(node: GridStackNode, triggerAddEvent = false, after?: GridStackNode): GridStackNode {\n    const dup = this.nodes.find(n => n._id === node._id);\n    if (dup) return dup; // prevent inserting twice! return it instead.\n\n    // skip prepareNode if we're in middle of column resize (not new) but do check for bounds!\n    this._inColumnResize ? this.nodeBoundFix(node) : this.prepareNode(node);\n    delete node._temporaryRemoved;\n    delete node._removeDOM;\n\n    let skipCollision: boolean;\n    if (node.autoPosition && this.findEmptyPosition(node, this.nodes, this.column, after)) {\n      delete node.autoPosition; // found our slot\n      skipCollision = true;\n    }\n\n    this.nodes.push(node);\n    if (triggerAddEvent) { this.addedNodes.push(node); }\n\n    if (!skipCollision) this._fixCollisions(node);\n    if (!this.batchMode) { this._packNodes()._notify(); }\n    return node;\n  }\n\n  public removeNode(node: GridStackNode, removeDOM = true, triggerEvent = false): GridStackEngine {\n    if (!this.nodes.find(n => n._id === node._id)) {\n      // TEST console.log(`Error: GridStackEngine.removeNode() node._id=${node._id} not found!`)\n      return this;\n    }\n    if (triggerEvent) { // we wait until final drop to manually track removed items (rather than during drag)\n      this.removedNodes.push(node);\n    }\n    if (removeDOM) node._removeDOM = true; // let CB remove actual HTML (used to set _id to null, but then we loose layout info)\n    // don't use 'faster' .splice(findIndex(),1) in case node isn't in our list, or in multiple times.\n    this.nodes = this.nodes.filter(n => n._id !== node._id);\n    if (!node._isAboutToRemove) this._packNodes(); // if dragged out, no need to relayout as already done...\n    this._notify([node]);\n    return this;\n  }\n\n  public removeAll(removeDOM = true, triggerEvent = true): GridStackEngine {\n    delete this._layouts;\n    if (!this.nodes.length) return this;\n    removeDOM && this.nodes.forEach(n => n._removeDOM = true); // let CB remove actual HTML (used to set _id to null, but then we loose layout info)\n    const removedNodes = this.nodes;\n    this.removedNodes = triggerEvent ? removedNodes : [];\n    this.nodes = [];\n    return this._notify(removedNodes);\n  }\n\n  /** checks if item can be moved (layout constrain) vs moveNode(), returning true if was able to move.\n   * In more complicated cases (maxRow) it will attempt at moving the item and fixing\n   * others in a clone first, then apply those changes if still within specs. */\n  public moveNodeCheck(node: GridStackNode, o: GridStackMoveOpts): boolean {\n    // if (node.locked) return false;\n    if (!this.changedPosConstrain(node, o)) return false;\n    o.pack = true;\n\n    // simpler case: move item directly...\n    if (!this.maxRow) {\n      return this.moveNode(node, o);\n    }\n\n    // complex case: create a clone with NO maxRow (will check for out of bounds at the end)\n    let clonedNode: GridStackNode;\n    const clone = new GridStackEngine({\n      column: this.column,\n      float: this.float,\n      nodes: this.nodes.map(n => {\n        if (n._id === node._id) {\n          clonedNode = {...n};\n          return clonedNode;\n        }\n        return {...n};\n      })\n    });\n    if (!clonedNode) return false;\n\n    // check if we're covering 50% collision and could move, while still being under maxRow or at least not making it worse\n    // (case where widget was somehow added past our max #2449)\n    const canMove = clone.moveNode(clonedNode, o) && clone.getRow() <= Math.max(this.getRow(), this.maxRow);\n    // else check if we can force a swap (float=true, or different shapes) on non-resize\n    if (!canMove && !o.resizing && o.collide) {\n      const collide = o.collide.el.gridstackNode; // find the source node the clone collided with at 50%\n      if (this.swap(node, collide)) { // swaps and mark dirty\n        this._notify();\n        return true;\n      }\n    }\n    if (!canMove) return false;\n\n    // if clone was able to move, copy those mods over to us now instead of caller trying to do this all over!\n    // Note: we can't use the list directly as elements and other parts point to actual node, so copy content\n    clone.nodes.filter(n => n._dirty).forEach(c => {\n      const n = this.nodes.find(a => a._id === c._id);\n      if (!n) return;\n      Utils.copyPos(n, c);\n      n._dirty = true;\n    });\n    this._notify();\n    return true;\n  }\n\n  /** return true if can fit in grid height constrain only (always true if no maxRow) */\n  public willItFit(node: GridStackNode): boolean {\n    delete node._willFitPos;\n    if (!this.maxRow) return true;\n    // create a clone with NO maxRow and check if still within size\n    const clone = new GridStackEngine({\n      column: this.column,\n      float: this.float,\n      nodes: this.nodes.map(n => {return {...n}})\n    });\n    const n = {...node}; // clone node so we don't mod any settings on it but have full autoPosition and min/max as well! #1687\n    this.cleanupNode(n);\n    delete n.el; delete n._id; delete n.content; delete n.grid;\n    clone.addNode(n);\n    if (clone.getRow() <= this.maxRow) {\n      node._willFitPos = Utils.copyPos({}, n);\n      return true;\n    }\n    return false;\n  }\n\n  /** true if x,y or w,h are different after clamping to min/max */\n  public changedPosConstrain(node: GridStackNode, p: GridStackPosition): boolean {\n    // first make sure w,h are set for caller\n    p.w = p.w || node.w;\n    p.h = p.h || node.h;\n    if (node.x !== p.x || node.y !== p.y) return true;\n    // check constrained w,h\n    if (node.maxW) { p.w = Math.min(p.w, node.maxW); }\n    if (node.maxH) { p.h = Math.min(p.h, node.maxH); }\n    if (node.minW) { p.w = Math.max(p.w, node.minW); }\n    if (node.minH) { p.h = Math.max(p.h, node.minH); }\n    return (node.w !== p.w || node.h !== p.h);\n  }\n\n  /** return true if the passed in node was actually moved (checks for no-op and locked) */\n  public moveNode(node: GridStackNode, o: GridStackMoveOpts): boolean {\n    if (!node || /*node.locked ||*/ !o) return false;\n    let wasUndefinedPack: boolean;\n    if (o.pack === undefined && !this.batchMode) {\n      wasUndefinedPack = o.pack = true;\n    }\n\n    // constrain the passed in values and check if we're still changing our node\n    if (typeof o.x !== 'number') { o.x = node.x; }\n    if (typeof o.y !== 'number') { o.y = node.y; }\n    if (typeof o.w !== 'number') { o.w = node.w; }\n    if (typeof o.h !== 'number') { o.h = node.h; }\n    const resizing = (node.w !== o.w || node.h !== o.h);\n    const nn: GridStackNode = Utils.copyPos({}, node, true); // get min/max out first, then opt positions next\n    Utils.copyPos(nn, o);\n    this.nodeBoundFix(nn, resizing);\n    Utils.copyPos(o, nn);\n\n    if (!o.forceCollide && Utils.samePos(node, o)) return false;\n    const prevPos: GridStackPosition = Utils.copyPos({}, node);\n\n    // check if we will need to fix collision at our new location\n    const collides = this.collideAll(node, nn, o.skip);\n    let needToMove = true;\n    if (collides.length) {\n      const activeDrag = node._moving && !o.nested;\n      // check to make sure we actually collided over 50% surface area while dragging\n      let collide = activeDrag ? this.directionCollideCoverage(node, o, collides) : collides[0];\n      // if we're enabling creation of sub-grids on the fly, see if we're covering 80% of either one, if we didn't already do that\n      if (activeDrag && collide && node.grid?.opts?.subGridDynamic && !node.grid._isTemp) {\n        const over = Utils.areaIntercept(o.rect, collide._rect);\n        const a1 = Utils.area(o.rect);\n        const a2 = Utils.area(collide._rect);\n        const perc = over / (a1 < a2 ? a1 : a2);\n        if (perc > .8) {\n          collide.grid.makeSubGrid(collide.el, undefined, node);\n          collide = undefined;\n        }\n      }\n\n      if (collide) {\n        needToMove = !this._fixCollisions(node, nn, collide, o); // check if already moved...\n      } else {\n        needToMove = false; // we didn't cover >50% for a move, skip...\n        if (wasUndefinedPack) delete o.pack;\n      }\n    }\n\n    // now move (to the original ask vs the collision version which might differ) and repack things\n    if (needToMove && !Utils.samePos(node, nn)) {\n      node._dirty = true;\n      Utils.copyPos(node, nn);\n    }\n    if (o.pack) {\n      this._packNodes()\n        ._notify();\n    }\n    return !Utils.samePos(node, prevPos); // pack might have moved things back\n  }\n\n  public getRow(): number {\n    return this.nodes.reduce((row, n) => Math.max(row, n.y + n.h), 0);\n  }\n\n  public beginUpdate(node: GridStackNode): GridStackEngine {\n    if (!node._updating) {\n      node._updating = true;\n      delete node._skipDown;\n      if (!this.batchMode) this.saveInitial();\n    }\n    return this;\n  }\n\n  public endUpdate(): GridStackEngine {\n    const n = this.nodes.find(n => n._updating);\n    if (n) {\n      delete n._updating;\n      delete n._skipDown;\n    }\n    return this;\n  }\n\n  /** saves a copy of the largest column layout (eg 12 even when rendering oneColumnMode) so we don't loose orig layout,\n   * returning a list of widgets for serialization */\n  public save(saveElement = true, saveCB?: SaveFcn): GridStackNode[] {\n    // use the highest layout for any saved info so we can have full detail on reload #1849\n    const len = this._layouts?.length;\n    const layout = len && this.column !== (len - 1) ? this._layouts[len - 1] : null;\n    const list: GridStackNode[] = [];\n    this.sortNodes();\n    this.nodes.forEach(n => {\n      const wl = layout?.find(l => l._id === n._id);\n      // use layout info fields instead if set\n      const w: GridStackNode = {...n, ...(wl || {})};\n      Utils.removeInternalForSave(w, !saveElement);\n      if (saveCB) saveCB(n, w);\n      list.push(w);\n    });\n    return list;\n  }\n\n  /** @internal called whenever a node is added or moved - updates the cached layouts */\n  public layoutsNodesChange(nodes: GridStackNode[]): GridStackEngine {\n    if (!this._layouts || this._inColumnResize) return this;\n    // remove smaller layouts - we will re-generate those on the fly... larger ones need to update\n    this._layouts.forEach((layout, column) => {\n      if (!layout || column === this.column) return this;\n      if (column < this.column) {\n        this._layouts[column] = undefined;\n      }\n      else {\n        // we save the original x,y,w (h isn't cached) to see what actually changed to propagate better.\n        // NOTE: we don't need to check against out of bound scaling/moving as that will be done when using those cache values. #1785\n        const ratio = column / this.column;\n        nodes.forEach(node => {\n          if (!node._orig) return; // didn't change (newly added ?)\n          const n = layout.find(l => l._id === node._id);\n          if (!n) return; // no cache for new nodes. Will use those values.\n          // Y changed, push down same amount\n          // TODO: detect doing item 'swaps' will help instead of move (especially in 1 column mode)\n          if (n.y >= 0 && node.y !== node._orig.y) {\n            n.y += (node.y - node._orig.y);\n          }\n          // X changed, scale from new position\n          if (node.x !== node._orig.x) {\n            n.x = Math.round(node.x * ratio);\n          }\n          // width changed, scale from new width\n          if (node.w !== node._orig.w) {\n            n.w = Math.round(node.w * ratio);\n          }\n          // ...height always carries over from cache\n        });\n      }\n    });\n    return this;\n  }\n\n  /**\n   * @internal Called to scale the widget width & position up/down based on the column change.\n   * Note we store previous layouts (especially original ones) to make it possible to go\n   * from say 12 -> 1 -> 12 and get back to where we were.\n   *\n   * @param prevColumn previous number of columns\n   * @param column  new column number\n   * @param layout specify the type of re-layout that will happen (position, size, etc...).\n   * Note: items will never be outside of the current column boundaries. default (moveScale). Ignored for 1 column\n   */\n  public columnChanged(prevColumn: number, column: number, layout: ColumnOptions = 'moveScale'): GridStackEngine {\n    if (!this.nodes.length || !column || prevColumn === column) return this;\n\n    // simpler shortcuts layouts\n    const doCompact = layout === 'compact' || layout === 'list';\n    if (doCompact) {\n      this.sortNodes(1); // sort with original layout once and only once (new column will affect order otherwise)\n    }\n\n    // cache the current layout in case they want to go back (like 12 -> 1 -> 12) as it requires original data IFF we're sizing down (see below)\n    if (column < prevColumn) this.cacheLayout(this.nodes, prevColumn);\n    this.batchUpdate(); // do this EARLY as it will call saveInitial() so we can detect where we started for _dirty and collision\n    let newNodes: GridStackNode[] = [];\n    let nodes = doCompact ? this.nodes : Utils.sort(this.nodes, -1); // current column reverse sorting so we can insert last to front (limit collision)\n\n    // see if we have cached previous layout IFF we are going up in size (restore) otherwise always\n    // generate next size down from where we are (looks more natural as you gradually size down).\n    if (column > prevColumn && this._layouts) {\n      const cacheNodes = this._layouts[column] || [];\n      // ...if not, start with the largest layout (if not already there) as down-scaling is more accurate\n      // by pretending we came from that larger column by assigning those values as starting point\n      const lastIndex = this._layouts.length - 1;\n      if (!cacheNodes.length && prevColumn !== lastIndex && this._layouts[lastIndex]?.length) {\n        prevColumn = lastIndex;\n        this._layouts[lastIndex].forEach(cacheNode => {\n          const n = nodes.find(n => n._id === cacheNode._id);\n          if (n) {\n            // still current, use cache info positions\n            if (!doCompact && !cacheNode.autoPosition) {\n              n.x = cacheNode.x ?? n.x;\n              n.y = cacheNode.y ?? n.y;\n            }\n            n.w = cacheNode.w ?? n.w;\n            if (cacheNode.x == undefined || cacheNode.y === undefined) n.autoPosition = true;\n          }\n        });\n      }\n\n      // if we found cache re-use those nodes that are still current\n      cacheNodes.forEach(cacheNode => {\n        const j = nodes.findIndex(n => n._id === cacheNode._id);\n        if (j !== -1) {\n          const n = nodes[j];\n          // still current, use cache info positions\n          if (doCompact) {\n            n.w = cacheNode.w; // only w is used, and don't trim the list\n            return;\n          }\n          if (cacheNode.autoPosition || isNaN(cacheNode.x) || isNaN(cacheNode.y)) {\n            this.findEmptyPosition(cacheNode, newNodes);\n          }\n          if (!cacheNode.autoPosition) {\n            n.x = cacheNode.x ?? n.x;\n            n.y = cacheNode.y ?? n.y;\n            n.w = cacheNode.w ?? n.w;\n            newNodes.push(n);\n          }\n          nodes.splice(j, 1);\n        }\n      });\n    }\n\n    // much simpler layout that just compacts\n    if (doCompact) {\n      this.compact(layout, false);\n    } else {\n      // ...and add any extra non-cached ones\n      if (nodes.length) {\n        if (typeof layout === 'function') {\n          layout(column, prevColumn, newNodes, nodes);\n        } else {\n          const ratio = (doCompact || layout === 'none') ? 1 : column / prevColumn;\n          const move = (layout === 'move' || layout === 'moveScale');\n          const scale = (layout === 'scale' || layout === 'moveScale');\n          nodes.forEach(node => {\n            // NOTE: x + w could be outside of the grid, but addNode() below will handle that\n            node.x = (column === 1 ? 0 : (move ? Math.round(node.x * ratio) : Math.min(node.x, column - 1)));\n            node.w = ((column === 1 || prevColumn === 1) ? 1 : scale ? (Math.round(node.w * ratio) || 1) : (Math.min(node.w, column)));\n            newNodes.push(node);\n          });\n          nodes = [];\n        }\n      }\n\n      // finally re-layout them in reverse order (to get correct placement)\n      newNodes = Utils.sort(newNodes, -1);\n      this._inColumnResize = true; // prevent cache update\n      this.nodes = []; // pretend we have no nodes to start with (add() will use same structures) to simplify layout\n      newNodes.forEach(node => {\n        this.addNode(node, false); // 'false' for add event trigger\n        delete node._orig; // make sure the commit doesn't try to restore things back to original\n      });\n    }\n\n    this.nodes.forEach(n => delete n._orig); // clear _orig before batch=false so it doesn't handle float=true restore\n    this.batchUpdate(false, !doCompact);\n    delete this._inColumnResize;\n    return this;\n  }\n\n  /**\n   * call to cache the given layout internally to the given location so we can restore back when column changes size\n   * @param nodes list of nodes\n   * @param column corresponding column index to save it under\n   * @param clear if true, will force other caches to be removed (default false)\n   */\n  public cacheLayout(nodes: GridStackNode[], column: number, clear = false): GridStackEngine {\n    const copy: GridStackNode[] = [];\n    nodes.forEach((n, i) => {\n      // make sure we have an id in case this is new layout, else re-use id already set\n      if (n._id === undefined) {\n        const existing = n.id ? this.nodes.find(n2 => n2.id === n.id) : undefined; // find existing node using users id\n        n._id = existing?._id ?? GridStackEngine._idSeq++;\n      }\n      copy[i] = {x: n.x, y: n.y, w: n.w, _id: n._id} // only thing we change is x,y,w and id to find it back\n    });\n    this._layouts = clear ? [] : this._layouts || []; // use array to find larger quick\n    this._layouts[column] = copy;\n    return this;\n  }\n\n  /**\n   * call to cache the given node layout internally to the given location so we can restore back when column changes size\n   * @param node single node to cache\n   * @param column corresponding column index to save it under\n   */\n  public cacheOneLayout(n: GridStackNode, column: number): GridStackEngine {\n    n._id = n._id ?? GridStackEngine._idSeq++;\n    const l: GridStackNode = {x: n.x, y: n.y, w: n.w, _id: n._id}\n    if (n.autoPosition || n.x === undefined) { delete l.x; delete l.y; if (n.autoPosition) l.autoPosition = true; }\n    this._layouts = this._layouts || [];\n    this._layouts[column] = this._layouts[column] || [];\n    const index = this.findCacheLayout(n, column);\n    if (index === -1)\n      this._layouts[column].push(l);\n    else\n      this._layouts[column][index] = l;\n    return this;\n  }\n\n  protected findCacheLayout(n: GridStackNode, column: number): number | undefined {\n    return this._layouts?.[column]?.findIndex(l => l._id === n._id) ?? -1;\n  }\n\n  public removeNodeFromLayoutCache(n: GridStackNode) {\n    if (!this._layouts) {\n      return;\n    }\n    for (let i = 0; i < this._layouts.length; i++) {\n      const index = this.findCacheLayout(n, i);\n      if (index !== -1) {\n        this._layouts[i].splice(index, 1);\n      }\n    }\n  }\n\n  /** called to remove all internal values but the _id */\n  public cleanupNode(node: GridStackNode): GridStackEngine {\n    for (const prop in node) {\n      if (prop[0] === '_' && prop !== '_id') delete node[prop];\n    }\n    return this;\n  }\n}\n", "/**\r\n * types.ts 12.2.2\r\n * Copyright (c) 2021-2024 <PERSON> - see GridStack root license\r\n */\r\n\r\nimport { GridStack } from './gridstack';\r\nimport { GridStackEngine } from './gridstack-engine';\r\n\r\n// default values for grid options - used during init and when saving out\r\nexport const gridDefaults: GridStackOptions = {\r\n  alwaysShowResizeHandle: 'mobile',\r\n  animate: true,\r\n  auto: true,\r\n  cellHeight: 'auto',\r\n  cellHeightThrottle: 100,\r\n  cellHeightUnit: 'px',\r\n  column: 12,\r\n  draggable: { handle: '.grid-stack-item-content', appendTo: 'body', scroll: true },\r\n  handle: '.grid-stack-item-content',\r\n  itemClass: 'grid-stack-item',\r\n  margin: 10,\r\n  marginUnit: 'px',\r\n  maxRow: 0,\r\n  minRow: 0,\r\n  placeholderClass: 'grid-stack-placeholder',\r\n  placeholderText: '',\r\n  removableOptions: { accept: 'grid-stack-item', decline: 'grid-stack-non-removable'},\r\n  resizable: { handles: 'se' },\r\n  rtl: 'auto',\r\n\r\n  // **** same as not being set ****\r\n  // disableDrag: false,\r\n  // disableResize: false,\r\n  // float: false,\r\n  // handleClass: null,\r\n  // removable: false,\r\n  // staticGrid: false,\r\n  //removable\r\n};\r\n\r\n/**\r\n * different layout options when changing # of columns, including a custom function that takes new/old column count, and array of new/old positions\r\n * Note: new list may be partially already filled if we have a cache of the layout at that size and new items were added later.\r\n * Options are:\r\n * 'list' - treat items as sorted list, keeping items (un-sized unless too big for column count) sequentially reflowing them\r\n * 'compact' - similar to list, but using compact() method which will possibly re-order items if an empty slots are available due to a larger item needing to be pushed to next row\r\n * 'moveScale' - will scale and move items by the ratio new newColumnCount / oldColumnCount\r\n * 'move' | 'scale' - will only size or move items\r\n * 'none' will leave items unchanged, unless they don't fit in column count\r\n */\r\nexport type ColumnOptions = 'list' | 'compact' | 'moveScale' | 'move' | 'scale' | 'none' |\r\n  ((column: number, oldColumn: number, nodes: GridStackNode[], oldNodes: GridStackNode[]) => void);\r\nexport type CompactOptions = 'list' | 'compact';\r\nexport type numberOrString = number | string;\r\nexport interface GridItemHTMLElement extends HTMLElement {\r\n  /** pointer to grid node instance */\r\n  gridstackNode?: GridStackNode;\r\n  /** @internal */\r\n  _gridstackNodeOrig?: GridStackNode;\r\n}\r\n\r\nexport type GridStackElement = string | HTMLElement | GridItemHTMLElement;\r\n\r\n/** specific and general event handlers for the .on() method */\r\nexport type GridStackEventHandler = (event: Event) => void;\r\nexport type GridStackElementHandler = (event: Event, el: GridItemHTMLElement) => void;\r\nexport type GridStackNodesHandler = (event: Event, nodes: GridStackNode[]) => void;\r\nexport type GridStackDroppedHandler = (event: Event, previousNode: GridStackNode, newNode: GridStackNode) => void;\r\nexport type GridStackEventHandlerCallback = GridStackEventHandler | GridStackElementHandler | GridStackNodesHandler | GridStackDroppedHandler;\r\n\r\n/** optional function called during load() to callback the user on new added/remove grid items | grids */\r\nexport type AddRemoveFcn = (parent: HTMLElement, w: GridStackWidget, add: boolean, grid: boolean) => HTMLElement | undefined;\r\n\r\n/** optional function called during save() to let the caller add additional custom data to the GridStackWidget structure that will get returned */\r\nexport type SaveFcn = (node: GridStackNode, w: GridStackWidget) => void;\r\n\r\n/** optional function called during load()/addWidget() to let the caller create custom content other than plan text */\r\nexport type RenderFcn = (el: HTMLElement, w: GridStackWidget) => void;\r\n\r\nexport type ResizeToContentFcn = (el: GridItemHTMLElement) => void;\r\n\r\n/** describes the responsive nature of the grid. NOTE: make sure to have correct extra CSS to support this. */\r\nexport interface Responsive {\r\n  /** wanted width to maintain (+-50%) to dynamically pick a column count. NOTE: make sure to have correct extra CSS to support this. */\r\n  columnWidth?: number;\r\n  /** maximum number of columns allowed (default: 12). NOTE: make sure to have correct extra CSS to support this. */\r\n  columnMax?: number;\r\n  /** explicit width:column breakpoints instead of automatic 'columnWidth'. NOTE: make sure to have correct extra CSS to support this. */\r\n  breakpoints?: Breakpoint[];\r\n  /** specify if breakpoints are for window size or grid size (default:false = grid) */\r\n  breakpointForWindow?: boolean;\r\n  /** global re-layout mode when changing columns */\r\n  layout?: ColumnOptions;\r\n}\r\n\r\nexport interface Breakpoint {\r\n  /** <= width for the breakpoint to trigger */\r\n  w?: number;\r\n  /** column count */\r\n  c: number;\r\n  /** re-layout mode if different from global one */\r\n  layout?: ColumnOptions;\r\n  /** TODO: children layout, which spells out exact locations and could omit/add some children */\r\n  // children?: GridStackWidget[];\r\n}\r\n\r\n/**\r\n * Defines the options for a Grid\r\n */\r\nexport interface GridStackOptions {\r\n  /**\r\n   * accept widgets dragged from other grids or from outside (default: `false`). Can be:\r\n   * `true` (uses `'.grid-stack-item'` class filter) or `false`,\r\n   * string for explicit class name,\r\n   * function returning a boolean. See [example](http://gridstack.github.io/gridstack.js/demo/two.html)\r\n   */\r\n  acceptWidgets?: boolean | string | ((element: Element) => boolean);\r\n\r\n  /** possible values (default: `mobile`) - does not apply to non-resizable widgets\r\n    * `false` the resizing handles are only shown while hovering over a widget\r\n    * `true` the resizing handles are always shown\r\n    * 'mobile' if running on a mobile device, default to `true` (since there is no hovering per say), else `false`.\r\n    See [example](http://gridstack.github.io/gridstack.js/demo/mobile.html) */\r\n  alwaysShowResizeHandle?: true | false | 'mobile';\r\n\r\n  /** turns animation on (default?: true) */\r\n  animate?: boolean;\r\n\r\n  /** if false gridstack will not initialize existing items (default?: true) */\r\n  auto?: boolean;\r\n\r\n  /**\r\n   * one cell height (default?: 'auto'). Can be:\r\n   *  an integer (px)\r\n   *  a string (ex: '100px', '10em', '10rem'). Note: % doesn't work right - see demo/cell-height.html\r\n   *  0, in which case the library will not generate styles for rows. Everything must be defined in your own CSS files.\r\n   *  'auto' - height will be calculated for square cells (width / column) and updated live as you resize the window - also see `cellHeightThrottle`\r\n   *  'initial' - similar to 'auto' (start at square cells) but stay that size during window resizing.\r\n   */\r\n  cellHeight?: numberOrString;\r\n\r\n  /** throttle time delay (in ms) used when cellHeight='auto' to improve performance vs usability (default?: 100).\r\n   * A value of 0 will make it instant at a cost of re-creating the CSS file at ever window resize event!\r\n   * */\r\n  cellHeightThrottle?: number;\r\n\r\n  /** (internal) unit for cellHeight (default? 'px') which is set when a string cellHeight with a unit is passed (ex: '10rem') */\r\n  cellHeightUnit?: string;\r\n\r\n  /** list of children item to create when calling load() or addGrid() */\r\n  children?: GridStackWidget[];\r\n\r\n  /** number of columns (default?: 12). Note: IF you change this, CSS also have to change. See https://github.com/gridstack/gridstack.js#change-grid-columns.\r\n   * Note: for nested grids, it is recommended to use 'auto' which will always match the container grid-item current width (in column) to keep inside and outside\r\n   * items always the same. flag is NOT supported for regular non-nested grids.\r\n   */\r\n  column?: number | 'auto';\r\n\r\n  /** responsive column layout for width:column behavior */\r\n  columnOpts?: Responsive;\r\n\r\n  /** additional class on top of '.grid-stack' (which is required for our CSS) to differentiate this instance.\r\n  Note: only used by addGrid(), else your element should have the needed class */\r\n  class?: string;\r\n\r\n  /** disallows dragging of widgets (default?: false) */\r\n  disableDrag?: boolean;\r\n\r\n  /** disallows resizing of widgets (default?: false). */\r\n  disableResize?: boolean;\r\n\r\n  /** allows to override UI draggable options. (default?: { handle?: '.grid-stack-item-content', appendTo?: 'body' }) */\r\n  draggable?: DDDragOpt;\r\n\r\n  /** let user drag nested grid items out of a parent or not (default true - not supported yet) */\r\n  //dragOut?: boolean;\r\n\r\n  /** the type of engine to create (so you can subclass) default to GridStackEngine */\r\n  engineClass?: typeof GridStackEngine;\r\n\r\n  /** enable floating widgets (default?: false) See example (http://gridstack.github.io/gridstack.js/demo/float.html) */\r\n  float?: boolean;\r\n\r\n  /** draggable handle selector (default?: '.grid-stack-item-content') */\r\n  handle?: string;\r\n\r\n  /** draggable handle class (e.g. 'grid-stack-item-content'). If set 'handle' is ignored (default?: null) */\r\n  handleClass?: string;\r\n\r\n  /** additional widget class (default?: 'grid-stack-item') */\r\n  itemClass?: string;\r\n\r\n  /** re-layout mode when we're a subgrid and we are being resized. default to 'list' */\r\n  layout?: ColumnOptions;\r\n\r\n  /** true when widgets are only created when they scroll into view (visible) */\r\n  lazyLoad?: boolean;\r\n\r\n  /**\r\n   * gap between grid item and content (default?: 10). This will set all 4 sides and support the CSS formats below\r\n   *  an integer (px)\r\n   *  a string with possible units (ex: '2em', '20px', '2rem')\r\n   *  string with space separated values (ex: '5px 10px 0 20px' for all 4 sides, or '5em 10em' for top/bottom and left/right pairs like CSS).\r\n   * Note: all sides must have same units (last one wins, default px)\r\n   */\r\n  margin?: numberOrString;\r\n\r\n  /** OLD way to optionally set each side - use margin: '5px 10px 0 20px' instead. Used internally to store each side. */\r\n  marginTop?: numberOrString;\r\n  marginRight?: numberOrString;\r\n  marginBottom?: numberOrString;\r\n  marginLeft?: numberOrString;\r\n\r\n  /** (internal) unit for margin (default? 'px') set when `margin` is set as string with unit (ex: 2rem') */\r\n  marginUnit?: string;\r\n\r\n  /** maximum rows amount. Default? is 0 which means no maximum rows */\r\n  maxRow?: number;\r\n\r\n  /** minimum rows amount which is handy to prevent grid from collapsing when empty. Default is `0`.\r\n   * When no set the `min-height` CSS attribute on the grid div (in pixels) can be used, which will round to the closest row.\r\n   */\r\n  minRow?: number;\r\n\r\n  /** If you are using a nonce-based Content Security Policy, pass your nonce here and\r\n   * GridStack will add it to the <style> elements it creates. */\r\n  nonce?: string;\r\n\r\n  /** class for placeholder (default?: 'grid-stack-placeholder') */\r\n  placeholderClass?: string;\r\n\r\n  /** placeholder default content (default?: '') */\r\n  placeholderText?: string;\r\n\r\n  /** allows to override UI resizable options. (default?: { handles: 'se' }) */\r\n  resizable?: DDResizeOpt;\r\n\r\n  /**\r\n   * if true widgets could be removed by dragging outside of the grid. It could also be a selector string (ex: \".trash\"),\r\n   * in this case widgets will be removed by dropping them there (default?: false)\r\n   * See example (http://gridstack.github.io/gridstack.js/demo/two.html)\r\n   */\r\n  removable?: boolean | string;\r\n\r\n  /** allows to override UI removable options. (default?: { accept: '.grid-stack-item' }) */\r\n  removableOptions?: DDRemoveOpt;\r\n\r\n  /** fix grid number of rows. This is a shortcut of writing `minRow:N, maxRow:N`. (default `0` no constrain) */\r\n  row?: number;\r\n\r\n  /**\r\n   * if true turns grid to RTL. Possible values are true, false, 'auto' (default?: 'auto')\r\n   * See [example](http://gridstack.github.io/gridstack.js/demo/right-to-left(rtl).html)\r\n   */\r\n  rtl?: boolean | 'auto';\r\n\r\n  /** set to true if all grid items (by default, but item can also override) height should be based on content size instead of WidgetItem.h to avoid v-scrollbars.\r\n   * Note: this is still row based, not pixels, so it will use ceil(getBoundingClientRect().height / getCellHeight())\r\n   */\r\n  sizeToContent?: boolean;\r\n\r\n  /**\r\n   * makes grid static (default?: false). If `true` widgets are not movable/resizable.\r\n   * You don't even need draggable/resizable. A CSS class\r\n   * 'grid-stack-static' is also added to the element.\r\n   */\r\n  staticGrid?: boolean;\r\n\r\n  /**\r\n   * @deprecated Not used anymore, styles are now implemented with local CSS variables\r\n   */\r\n  styleInHead?: boolean;\r\n\r\n  /** list of differences in options for automatically created sub-grids under us (inside our grid-items) */\r\n  subGridOpts?: GridStackOptions;\r\n\r\n  /** enable/disable the creation of sub-grids on the fly by dragging items completely\r\n   * over others (nest) vs partially (push). Forces `DDDragOpt.pause=true` to accomplish that. */\r\n  subGridDynamic?: boolean;\r\n}\r\n\r\n/** options used during GridStackEngine.moveNode() */\r\nexport interface GridStackMoveOpts extends GridStackPosition {\r\n  /** node to skip collision */\r\n  skip?: GridStackNode;\r\n  /** do we pack (default true) */\r\n  pack?: boolean;\r\n  /** true if we are calling this recursively to prevent simple swap or coverage collision - default false*/\r\n  nested?: boolean;\r\n  /** vars to calculate other cells coordinates */\r\n  cellWidth?: number;\r\n  cellHeight?: number;\r\n  marginTop?: number;\r\n  marginBottom?: number;\r\n  marginLeft?: number;\r\n  marginRight?: number;\r\n  /** position in pixels of the currently dragged items (for overlap check) */\r\n  rect?: GridStackPosition;\r\n  /** true if we're live resizing */\r\n  resizing?: boolean;\r\n  /** best node (most coverage) we collied with */\r\n  collide?: GridStackNode;\r\n  /** for collision check even if we don't move */\r\n  forceCollide?: boolean;\r\n}\r\n\r\nexport interface GridStackPosition {\r\n  /** widget position x (default?: 0) */\r\n  x?: number;\r\n  /** widget position y (default?: 0) */\r\n  y?: number;\r\n  /** widget dimension width (default?: 1) */\r\n  w?: number;\r\n  /** widget dimension height (default?: 1) */\r\n  h?: number;\r\n}\r\n\r\n/**\r\n * GridStack Widget creation options\r\n */\r\nexport interface GridStackWidget extends GridStackPosition {\r\n  /** if true then x, y parameters will be ignored and widget will be places on the first available position (default?: false) */\r\n  autoPosition?: boolean;\r\n  /** minimum width allowed during resize/creation (default?: undefined = un-constrained) */\r\n  minW?: number;\r\n  /** maximum width allowed during resize/creation (default?: undefined = un-constrained) */\r\n  maxW?: number;\r\n  /** minimum height allowed during resize/creation (default?: undefined = un-constrained) */\r\n  minH?: number;\r\n  /** maximum height allowed during resize/creation (default?: undefined = un-constrained) */\r\n  maxH?: number;\r\n  /** prevent direct resizing by the user (default?: undefined = un-constrained) */\r\n  noResize?: boolean;\r\n  /** prevents direct moving by the user (default?: undefined = un-constrained) */\r\n  noMove?: boolean;\r\n  /** prevents being pushed by other widgets or api (default?: undefined = un-constrained), which is different from `noMove` (user action only) */\r\n  locked?: boolean;\r\n  /** value for `gs-id` stored on the widget (default?: undefined) */\r\n  id?: string;\r\n  /** html to append inside as content */\r\n  content?: string;\r\n  /** true when widgets are only created when they scroll into view (visible) */\r\n  lazyLoad?: boolean;\r\n  /** local (vs grid) override - see GridStackOptions.\r\n   * Note: This also allow you to set a maximum h value (but user changeable during normal resizing) to prevent unlimited content from taking too much space (get scrollbar) */\r\n  sizeToContent?: boolean | number;\r\n  /** local override of GridStack.resizeToContentParent that specify the class to use for the parent (actual) vs child (wanted) height */\r\n  resizeToContentParent?: string;\r\n  /** optional nested grid options and list of children, which then turns into actual instance at runtime to get options from */\r\n  subGridOpts?: GridStackOptions;\r\n}\r\n\r\n/** Drag&Drop resize options */\r\nexport interface DDResizeOpt {\r\n  /** do resize handle hide by default until mouse over ? - default: true on desktop, false on mobile*/\r\n  autoHide?: boolean;\r\n  /**\r\n   * sides where you can resize from (ex: 'e, se, s, sw, w') - default 'se' (south-east)\r\n   * Note: it is not recommended to resize from the top sides as weird side effect may occur.\r\n  */\r\n  handles?: string;\r\n}\r\n\r\n/** Drag&Drop remove options */\r\nexport interface DDRemoveOpt {\r\n  /** class that can be removed (default?: opts.itemClass) */\r\n  accept?: string;\r\n  /** class that cannot be removed (default: 'grid-stack-non-removable') */\r\n  decline?: string;\r\n}\r\n\r\n/** Drag&Drop dragging options */\r\nexport interface DDDragOpt {\r\n  /** class selector of items that can be dragged. default to '.grid-stack-item-content' */\r\n  handle?: string;\r\n  /** default to 'body' */\r\n  appendTo?: string;\r\n  /** if set (true | msec), dragging placement (collision) will only happen after a pause by the user. Note: this is Global */\r\n  pause?: boolean | number;\r\n  /** default to `true` */\r\n  scroll?: boolean;\r\n  /** prevents dragging from starting on specified elements, listed as comma separated selectors (eg: '.no-drag'). default built in is 'input,textarea,button,select,option' */\r\n  cancel?: string;\r\n  /** helper function when dropping: 'clone' or your own method */\r\n  helper?: 'clone' | ((el: HTMLElement) => HTMLElement);\r\n  /** callbacks */\r\n  start?: (event: Event, ui: DDUIData) => void;\r\n  stop?: (event: Event) => void;\r\n  drag?: (event: Event, ui: DDUIData) => void;\r\n}\r\nexport interface Size {\r\n  width: number;\r\n  height: number;\r\n}\r\nexport interface Position {\r\n  top: number;\r\n  left: number;\r\n}\r\nexport interface Rect extends Size, Position {}\r\n\r\n/** data that is passed during drag and resizing callbacks */\r\nexport interface DDUIData {\r\n  position?: Position;\r\n  size?: Size;\r\n  draggable?: HTMLElement;\r\n  /* fields not used by GridStack but sent by jq ? leave in case we go back to them...\r\n  originalPosition? : Position;\r\n  offset?: Position;\r\n  originalSize?: Size;\r\n  element?: HTMLElement[];\r\n  helper?: HTMLElement[];\r\n  originalElement?: HTMLElement[];\r\n  */\r\n}\r\n\r\n/**\r\n * internal runtime descriptions describing the widgets in the grid\r\n */\r\nexport interface GridStackNode extends GridStackWidget {\r\n  /** pointer back to HTML element */\r\n  el?: GridItemHTMLElement;\r\n  /** pointer back to parent Grid instance */\r\n  grid?: GridStack;\r\n  /** actual sub-grid instance */\r\n  subGrid?: GridStack;\r\n  /** allow delay creation when visible */\r\n  visibleObservable?: IntersectionObserver;\r\n  /** @internal internal id used to match when cloning engines or saving column layouts */\r\n  _id?: number;\r\n  /** @internal does the node attr ned to be updated due to changed x,y,w,h values */\r\n  _dirty?: boolean;\r\n  /** @internal */\r\n  _updating?: boolean;\r\n  /** @internal true when over trash/another grid so we don't bother removing drag CSS style that would animate back to old position */\r\n  _isAboutToRemove?: boolean;\r\n  /** @internal true if item came from outside of the grid -> actual item need to be moved over */\r\n  _isExternal?: boolean;\r\n  /** @internal Mouse event that's causing moving|resizing */\r\n  _event?: MouseEvent;\r\n  /** @internal moving vs resizing */\r\n  _moving?: boolean;\r\n  /** @internal is resizing? */\r\n  _resizing?: boolean;\r\n  /** @internal true if we jumped down past item below (one time jump so we don't have to totally pass it) */\r\n  _skipDown?: boolean;\r\n  /** @internal original values before a drag/size */\r\n  _orig?: GridStackPosition;\r\n  /** @internal position in pixels used during collision check  */\r\n  _rect?: GridStackPosition;\r\n  /** @internal top/left pixel location before a drag so we can detect direction of move from last position*/\r\n  _lastUiPosition?: Position;\r\n  /** @internal set on the item being dragged/resized remember the last positions we've tried (but failed) so we don't try again during drag/resize */\r\n  _lastTried?: GridStackPosition;\r\n  /** @internal position willItFit() will use to position the item */\r\n  _willFitPos?: GridStackPosition;\r\n  /** @internal last drag Y pixel position used to incrementally update V scroll bar */\r\n  _prevYPix?: number;\r\n  /** @internal true if we've remove the item from ourself (dragging out) but might revert it back (release on nothing -> goes back) */\r\n  _temporaryRemoved?: boolean;\r\n  /** @internal true if we should remove DOM element on _notify() rather than clearing _id (old way) */\r\n  _removeDOM?: boolean;\r\n  /** @internal original position/size of item if dragged from sidebar */\r\n  _sidebarOrig?: GridStackPosition;\r\n  /** @internal had drag&drop been initialized */\r\n  _initDD?: boolean;\r\n}\r\n", "/**\n * dd-manager.ts 12.2.2\n * Copyright (c) 2021-2024 <PERSON> - see GridStack root license\n */\n\nimport { DDDraggable } from './dd-draggable';\nimport { DDDroppable } from './dd-droppable';\nimport { DDResizable } from './dd-resizable';\n\n/**\n * globals that are shared across Drag & Drop instances\n */\nexport class DDManager {\n  /** if set (true | in msec), dragging placement (collision) will only happen after a pause by the user*/\n  public static pauseDrag: boolean | number;\n\n  /** true if a mouse down event was handled */\n  public static mouseHandled: boolean;\n\n  /** item being dragged */\n  public static dragElement: DDDraggable;\n\n  /** item we are currently over as drop target */\n  public static dropElement: DDDroppable;\n\n  /** current item we're over for resizing purpose (ignore nested grid resize handles) */\n  public static overResizeElement: DDResizable;\n\n}\n", "/**\n * touch.ts 12.2.2\n * Copyright (c) 2021-2024 <PERSON> - see GridStack root license\n */\n\nimport { DDManager } from './dd-manager';\nimport { Utils } from './utils';\n\n/**\n * Detect touch support - Windows Surface devices and other touch devices\n * should we use this instead ? (what we had for always showing resize handles)\n * /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)\n */\nexport const isTouch: boolean = typeof window !== 'undefined' && typeof document !== 'undefined' &&\n  ( 'ontouchstart' in document\n    || 'ontouchstart' in window\n    // || !!window.TouchEvent // true on Windows 10 Chrome desktop so don't use this\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    || ((window as any).DocumentTouch && document instanceof (window as any).DocumentTouch)\n    || navigator.maxTouchPoints > 0\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    || (navigator as any).msMaxTouchPoints > 0\n  );\n\n// interface TouchCoord {x: number, y: number};\n\nclass DDTouch {\n  public static touchHandled: boolean;\n  public static pointerLeaveTimeout: number;\n}\n\n/**\n* Get the x,y position of a touch event\n*/\n// function getTouchCoords(e: TouchEvent): TouchCoord {\n//   return {\n//     x: e.changedTouches[0].pageX,\n//     y: e.changedTouches[0].pageY\n//   };\n// }\n\n/**\n * Simulate a mouse event based on a corresponding touch event\n * @param {Object} e A touch event\n * @param {String} simulatedType The corresponding mouse event\n */\nfunction simulateMouseEvent(e: TouchEvent, simulatedType: string) {\n\n  // Ignore multi-touch events\n  if (e.touches.length > 1) return;\n\n  // Prevent \"Ignored attempt to cancel a touchmove event with cancelable=false\" errors\n  if (e.cancelable) e.preventDefault();\n\n  // Dispatch the simulated event to the target element\n  Utils.simulateMouseEvent(e.changedTouches[0], simulatedType);\n}\n\n/**\n * Simulate a mouse event based on a corresponding Pointer event\n * @param {Object} e A pointer event\n * @param {String} simulatedType The corresponding mouse event\n */\nfunction simulatePointerMouseEvent(e: PointerEvent, simulatedType: string) {\n\n  // Prevent \"Ignored attempt to cancel a touchmove event with cancelable=false\" errors\n  if (e.cancelable) e.preventDefault();\n\n  // Dispatch the simulated event to the target element\n  Utils.simulateMouseEvent(e, simulatedType);\n}\n\n\n/**\n * Handle the touchstart events\n * @param {Object} e The widget element's touchstart event\n */\nexport function touchstart(e: TouchEvent): void {\n  // Ignore the event if another widget is already being handled\n  if (DDTouch.touchHandled) return;\n  DDTouch.touchHandled = true;\n\n  // Simulate the mouse events\n  // simulateMouseEvent(e, 'mouseover');\n  // simulateMouseEvent(e, 'mousemove');\n  simulateMouseEvent(e, 'mousedown');\n}\n\n/**\n * Handle the touchmove events\n * @param {Object} e The document's touchmove event\n */\nexport function touchmove(e: TouchEvent): void {\n  // Ignore event if not handled by us\n  if (!DDTouch.touchHandled) return;\n\n  simulateMouseEvent(e, 'mousemove');\n}\n\n/**\n * Handle the touchend events\n * @param {Object} e The document's touchend event\n */\nexport function touchend(e: TouchEvent): void {\n\n  // Ignore event if not handled\n  if (!DDTouch.touchHandled) return;\n\n  // cancel delayed leave event when we release on ourself which happens BEFORE we get this!\n  if (DDTouch.pointerLeaveTimeout) {\n    window.clearTimeout(DDTouch.pointerLeaveTimeout);\n    delete DDTouch.pointerLeaveTimeout;\n  }\n\n  const wasDragging = !!DDManager.dragElement;\n\n  // Simulate the mouseup event\n  simulateMouseEvent(e, 'mouseup');\n  // simulateMouseEvent(event, 'mouseout');\n\n  // If the touch interaction did not move, it should trigger a click\n  if (!wasDragging) {\n    simulateMouseEvent(e, 'click');\n  }\n\n  // Unset the flag to allow other widgets to inherit the touch event\n  DDTouch.touchHandled = false;\n}\n\n/**\n * Note we don't get touchenter/touchleave (which are deprecated)\n * see https://stackoverflow.com/questions/27908339/js-touch-equivalent-for-mouseenter\n * so instead of PointerEvent to still get enter/leave and send the matching mouse event.\n */\nexport function pointerdown(e: PointerEvent): void {\n  // console.log(\"pointer down\")\n  if (e.pointerType === 'mouse') return;\n  (e.target as HTMLElement).releasePointerCapture(e.pointerId) // <- Important!\n}\n\nexport function pointerenter(e: PointerEvent): void {\n  // ignore the initial one we get on pointerdown on ourself\n  if (!DDManager.dragElement) {\n    // console.log('pointerenter ignored');\n    return;\n  }\n  // console.log('pointerenter');\n  if (e.pointerType === 'mouse') return;\n  simulatePointerMouseEvent(e, 'mouseenter');\n}\n\nexport function pointerleave(e: PointerEvent): void {\n  // ignore the leave on ourself we get before releasing the mouse over ourself\n  // by delaying sending the event and having the up event cancel us\n  if (!DDManager.dragElement) {\n    // console.log('pointerleave ignored');\n    return;\n  }\n  if (e.pointerType === 'mouse') return;\n  DDTouch.pointerLeaveTimeout = window.setTimeout(() => {\n    delete DDTouch.pointerLeaveTimeout;\n    // console.log('pointerleave delayed');\n    simulatePointerMouseEvent(e, 'mouseleave');\n  }, 10);\n}\n\n", "/**\n * dd-resizable-handle.ts 12.2.2\n * Copyright (c) 2021-2024  <PERSON> - see GridStack root license\n */\n\nimport { isTouch, pointerdown, touchend, touchmove, touchstart } from './dd-touch';\nimport { GridItemHTMLElement } from './gridstack';\n\nexport interface DDResizableHandleOpt {\n  start?: (event) => void;\n  move?: (event) => void;\n  stop?: (event) => void;\n}\n\nexport class DDResizableHandle {\n  /** @internal */\n  protected el: HTMLElement;\n  /** @internal true after we've moved enough pixels to start a resize */\n  protected moving = false;\n  /** @internal */\n  protected mouseDownEvent: MouseEvent;\n  /** @internal */\n  protected static prefix = 'ui-resizable-';\n\n  constructor(protected host: GridItemHTMLElement, protected dir: string, protected option: DDResizableHandleOpt) {\n    // create var event binding so we can easily remove and still look like TS methods (unlike anonymous functions)\n    this._mouseDown = this._mouseDown.bind(this);\n    this._mouseMove = this._mouseMove.bind(this);\n    this._mouseUp = this._mouseUp.bind(this);\n    this._keyEvent = this._keyEvent.bind(this);\n\n    this._init();\n  }\n\n  /** @internal */\n  protected _init(): DDResizableHandle {\n    const el = this.el = document.createElement('div');\n    el.classList.add('ui-resizable-handle');\n    el.classList.add(`${DDResizableHandle.prefix}${this.dir}`);\n    el.style.zIndex = '100';\n    el.style.userSelect = 'none';\n    this.host.appendChild(this.el);\n    this.el.addEventListener('mousedown', this._mouseDown);\n    if (isTouch) {\n      this.el.addEventListener('touchstart', touchstart);\n      this.el.addEventListener('pointerdown', pointerdown);\n      // this.el.style.touchAction = 'none'; // not needed unlike pointerdown doc comment\n    }\n    return this;\n  }\n\n  /** call this when resize handle needs to be removed and cleaned up */\n  public destroy(): DDResizableHandle {\n    if (this.moving) this._mouseUp(this.mouseDownEvent);\n    this.el.removeEventListener('mousedown', this._mouseDown);\n    if (isTouch) {\n      this.el.removeEventListener('touchstart', touchstart);\n      this.el.removeEventListener('pointerdown', pointerdown);\n    }\n    this.host.removeChild(this.el);\n    delete this.el;\n    delete this.host;\n    return this;\n  }\n\n  /** @internal called on mouse down on us: capture move on the entire document (mouse might not stay on us) until we release the mouse */\n  protected _mouseDown(e: MouseEvent): void {\n    this.mouseDownEvent = e;\n    document.addEventListener('mousemove', this._mouseMove, { capture: true, passive: true}); // capture, not bubble\n    document.addEventListener('mouseup', this._mouseUp, true);\n    if (isTouch) {\n      this.el.addEventListener('touchmove', touchmove);\n      this.el.addEventListener('touchend', touchend);\n    }\n    e.stopPropagation();\n    e.preventDefault();\n  }\n\n  /** @internal */\n  protected _mouseMove(e: MouseEvent): void {\n    const s = this.mouseDownEvent;\n    if (this.moving) {\n      this._triggerEvent('move', e);\n    } else if (Math.abs(e.x - s.x) + Math.abs(e.y - s.y) > 2) {\n      // don't start unless we've moved at least 3 pixels\n      this.moving = true;\n      this._triggerEvent('start', this.mouseDownEvent);\n      this._triggerEvent('move', e);\n      // now track keyboard events to cancel\n      document.addEventListener('keydown', this._keyEvent);\n    }\n    e.stopPropagation();\n    // e.preventDefault(); passive = true\n  }\n\n  /** @internal */\n  protected _mouseUp(e: MouseEvent): void {\n    if (this.moving) {\n      this._triggerEvent('stop', e);\n      document.removeEventListener('keydown', this._keyEvent);\n    }\n    document.removeEventListener('mousemove', this._mouseMove, true);\n    document.removeEventListener('mouseup', this._mouseUp, true);\n    if (isTouch) {\n      this.el.removeEventListener('touchmove', touchmove);\n      this.el.removeEventListener('touchend', touchend);\n    }\n    delete this.moving;\n    delete this.mouseDownEvent;\n    e.stopPropagation();\n    e.preventDefault();\n  }\n\n  /** @internal call when keys are being pressed - use Esc to cancel */\n  protected _keyEvent(e: KeyboardEvent): void {\n    if (e.key === 'Escape') {\n      this.host.gridstackNode?.grid?.engine.restoreInitial();\n      this._mouseUp(this.mouseDownEvent);\n    }\n  }\n\n\n\n  /** @internal */\n  protected _triggerEvent(name: string, event: MouseEvent): DDResizableHandle {\n    if (this.option[name]) this.option[name](event);\n    return this;\n  }\n}\n", "/**\n * dd-base-impl.ts 12.2.2\n * Copyright (c) 2021-2024  <PERSON> - see GridStack root license\n */\n\nexport type EventCallback = (event: Event) => boolean|void;\nexport abstract class DDBaseImplement {\n  /** returns the enable state, but you have to call enable()/disable() to change (as other things need to happen) */\n  public get disabled(): boolean   { return this._disabled; }\n\n  /** @internal */\n  protected _disabled: boolean; // initial state to differentiate from false\n  /** @internal */\n  protected _eventRegister: {\n    [eventName: string]: EventCallback;\n  } = {};\n\n  public on(event: string, callback: EventCallback): void {\n    this._eventRegister[event] = callback;\n  }\n\n  public off(event: string): void {\n    delete this._eventRegister[event];\n  }\n\n  public enable(): void {\n    this._disabled = false;\n  }\n\n  public disable(): void {\n    this._disabled = true;\n  }\n\n  public destroy(): void {\n    delete this._eventRegister;\n  }\n\n  public triggerEvent(eventName: string, event: Event): boolean|void {\n    if (!this.disabled && this._eventRegister && this._eventRegister[eventName])\n      return this._eventRegister[eventName](event);\n  }\n}\n\nexport interface HTMLElementExtendOpt<T> {\n  el: HTMLElement;\n  option: T;\n  updateOption(T): DDBaseImplement;\n}\n", "/**\n * dd-resizable.ts 12.2.2\n * Copyright (c) 2021-2024  <PERSON> - see GridStack root license\n */\n\nimport { DDResizableHandle } from './dd-resizable-handle';\nimport { DDBaseImplement, HTMLElementExtendOpt } from './dd-base-impl';\nimport { Utils } from './utils';\nimport { DDUIData, GridItemHTMLElement, Rect, Size } from './types';\nimport { DDManager } from './dd-manager';\n\n// import { GridItemHTMLElement } from './types'; let count = 0; // TEST\n\n// TODO: merge with DDDragOpt\nexport interface DDResizableOpt {\n  autoHide?: boolean;\n  handles?: string;\n  maxHeight?: number;\n  maxHeightMoveUp?: number;\n  maxWidth?: number;\n  maxWidthMoveLeft?: number;\n  minHeight?: number;\n  minWidth?: number;\n  start?: (event: Event, ui: DDUIData) => void;\n  stop?: (event: Event) => void;\n  resize?: (event: Event, ui: DDUIData) => void;\n}\n\ninterface RectScaleReciprocal {\n  x: number;\n  y: number;\n}\n\nexport class DDResizable extends DDBaseImplement implements HTMLElementExtendOpt<DDResizableOpt> {\n  /** @internal */\n  protected handlers: DDResizableHandle[];\n  /** @internal */\n  protected originalRect: Rect;\n  /** @internal */\n  protected rectScale: RectScaleReciprocal = { x: 1, y: 1 };\n  /** @internal */\n  protected temporalRect: Rect;\n  /** @internal */\n  protected scrollY: number;\n  /** @internal */\n  protected scrolled: number;\n  /** @internal */\n  protected scrollEl: HTMLElement;\n  /** @internal */\n  protected startEvent: MouseEvent;\n  /** @internal value saved in the same order as _originStyleProp[] */\n  protected elOriginStyleVal: string[];\n  /** @internal */\n  protected parentOriginStylePosition: string;\n  /** @internal */\n  protected static _originStyleProp = ['width', 'height', 'position', 'left', 'top', 'opacity', 'zIndex'];\n  /** @internal */\n  protected sizeToContent: boolean;\n\n  // have to be public else complains for HTMLElementExtendOpt ?\n  constructor(public el: GridItemHTMLElement, public option: DDResizableOpt = {}) {\n    super();\n    // create var event binding so we can easily remove and still look like TS methods (unlike anonymous functions)\n    this._mouseOver = this._mouseOver.bind(this);\n    this._mouseOut = this._mouseOut.bind(this);\n    this.enable();\n    this._setupAutoHide(this.option.autoHide);\n    this._setupHandlers();\n  }\n\n  public on(event: 'resizestart' | 'resize' | 'resizestop', callback: (event: DragEvent) => void): void {\n    super.on(event, callback);\n  }\n\n  public off(event: 'resizestart' | 'resize' | 'resizestop'): void {\n    super.off(event);\n  }\n\n  public enable(): void {\n    super.enable();\n    this.el.classList.remove('ui-resizable-disabled');\n    this._setupAutoHide(this.option.autoHide);\n  }\n\n  public disable(): void {\n    super.disable();\n    this.el.classList.add('ui-resizable-disabled');\n    this._setupAutoHide(false);\n  }\n\n  public destroy(): void {\n    this._removeHandlers();\n    this._setupAutoHide(false);\n    delete this.el;\n    super.destroy();\n  }\n\n  public updateOption(opts: DDResizableOpt): DDResizable {\n    const updateHandles = (opts.handles && opts.handles !== this.option.handles);\n    const updateAutoHide = (opts.autoHide && opts.autoHide !== this.option.autoHide);\n    Object.keys(opts).forEach(key => this.option[key] = opts[key]);\n    if (updateHandles) {\n      this._removeHandlers();\n      this._setupHandlers();\n    }\n    if (updateAutoHide) {\n      this._setupAutoHide(this.option.autoHide);\n    }\n    return this;\n  }\n\n  /** @internal turns auto hide on/off */\n  protected _setupAutoHide(auto: boolean): DDResizable {\n    if (auto) {\n      this.el.classList.add('ui-resizable-autohide');\n      // use mouseover and not mouseenter to get better performance and track for nested cases\n      this.el.addEventListener('mouseover', this._mouseOver);\n      this.el.addEventListener('mouseout', this._mouseOut);\n    } else {\n      this.el.classList.remove('ui-resizable-autohide');\n      this.el.removeEventListener('mouseover', this._mouseOver);\n      this.el.removeEventListener('mouseout', this._mouseOut);\n      if (DDManager.overResizeElement === this) {\n        delete DDManager.overResizeElement;\n      }\n    }\n    return this;\n  }\n\n  /** @internal */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  protected _mouseOver(e: Event): void {\n    // console.log(`${count++} pre-enter ${(this.el as GridItemHTMLElement).gridstackNode._id}`)\n    // already over a child, ignore. Ideally we just call e.stopPropagation() but see https://github.com/gridstack/gridstack.js/issues/2018\n    if (DDManager.overResizeElement || DDManager.dragElement) return;\n    DDManager.overResizeElement = this;\n    // console.log(`${count++} enter ${(this.el as GridItemHTMLElement).gridstackNode._id}`)\n    this.el.classList.remove('ui-resizable-autohide');\n  }\n\n  /** @internal */\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  protected _mouseOut(e: Event): void {\n    // console.log(`${count++} pre-leave ${(this.el as GridItemHTMLElement).gridstackNode._id}`)\n    if (DDManager.overResizeElement !== this) return;\n    delete DDManager.overResizeElement;\n    // console.log(`${count++} leave ${(this.el as GridItemHTMLElement).gridstackNode._id}`)\n    this.el.classList.add('ui-resizable-autohide');\n  }\n\n  /** @internal */\n  protected _setupHandlers(): DDResizable {\n    this.handlers = this.option.handles.split(',')\n      .map(dir => dir.trim())\n      .map(dir => new DDResizableHandle(this.el, dir, {\n        start: (event: MouseEvent) => {\n          this._resizeStart(event);\n        },\n        stop: (event: MouseEvent) => {\n          this._resizeStop(event);\n        },\n        move: (event: MouseEvent) => {\n          this._resizing(event, dir);\n        }\n      }));\n    return this;\n  }\n\n  /** @internal */\n  protected _resizeStart(event: MouseEvent): DDResizable {\n    this.sizeToContent = Utils.shouldSizeToContent(this.el.gridstackNode, true); // strick true only and not number\n    this.originalRect = this.el.getBoundingClientRect();\n    this.scrollEl = Utils.getScrollElement(this.el);\n    this.scrollY = this.scrollEl.scrollTop;\n    this.scrolled = 0;\n    this.startEvent = event;\n    this._setupHelper();\n    this._applyChange();\n    const ev = Utils.initEvent<MouseEvent>(event, { type: 'resizestart', target: this.el });\n    if (this.option.start) {\n      this.option.start(ev, this._ui());\n    }\n    this.el.classList.add('ui-resizable-resizing');\n    this.triggerEvent('resizestart', ev);\n    return this;\n  }\n\n  /** @internal */\n  protected _resizing(event: MouseEvent, dir: string): DDResizable {\n    this.scrolled = this.scrollEl.scrollTop - this.scrollY;\n    this.temporalRect = this._getChange(event, dir);\n    this._applyChange();\n    const ev = Utils.initEvent<MouseEvent>(event, { type: 'resize', target: this.el });\n    if (this.option.resize) {\n      this.option.resize(ev, this._ui());\n    }\n    this.triggerEvent('resize', ev);\n    return this;\n  }\n\n  /** @internal */\n  protected _resizeStop(event: MouseEvent): DDResizable {\n    const ev = Utils.initEvent<MouseEvent>(event, { type: 'resizestop', target: this.el });\n    // Remove style attr now, so the stop handler can rebuild style attrs\n    this._cleanHelper();\n    if (this.option.stop) {\n      this.option.stop(ev); // Note: ui() not used by gridstack so don't pass\n    }\n    this.el.classList.remove('ui-resizable-resizing');\n    this.triggerEvent('resizestop', ev);\n    delete this.startEvent;\n    delete this.originalRect;\n    delete this.temporalRect;\n    delete this.scrollY;\n    delete this.scrolled;\n    return this;\n  }\n\n  /** @internal */\n  protected _setupHelper(): DDResizable {\n    this.elOriginStyleVal = DDResizable._originStyleProp.map(prop => this.el.style[prop]);\n    this.parentOriginStylePosition = this.el.parentElement.style.position;\n\n    const parent = this.el.parentElement;\n    const dragTransform = Utils.getValuesFromTransformedElement(parent);\n    this.rectScale = {\n      x: dragTransform.xScale,\n      y: dragTransform.yScale\n    };\n\n    if (getComputedStyle(this.el.parentElement).position.match(/static/)) {\n      this.el.parentElement.style.position = 'relative';\n    }\n    this.el.style.position = 'absolute';\n    this.el.style.opacity = '0.8';\n    return this;\n  }\n\n  /** @internal */\n  protected _cleanHelper(): DDResizable {\n    DDResizable._originStyleProp.forEach((prop, i) => {\n      this.el.style[prop] = this.elOriginStyleVal[i] || null;\n    });\n    this.el.parentElement.style.position = this.parentOriginStylePosition || null;\n    return this;\n  }\n\n  /** @internal */\n  protected _getChange(event: MouseEvent, dir: string): Rect {\n    const oEvent = this.startEvent;\n    const newRect = { // Note: originalRect is a complex object, not a simple Rect, so copy out.\n      width: this.originalRect.width,\n      height: this.originalRect.height + this.scrolled,\n      left: this.originalRect.left,\n      top: this.originalRect.top - this.scrolled\n    };\n\n    const offsetX = event.clientX - oEvent.clientX;\n    const offsetY = this.sizeToContent ? 0 : event.clientY - oEvent.clientY; // prevent vert resize\n    let moveLeft: boolean;\n    let moveUp: boolean;\n\n    if (dir.indexOf('e') > -1) {\n      newRect.width += offsetX;\n    } else if (dir.indexOf('w') > -1) {\n      newRect.width -= offsetX;\n      newRect.left += offsetX;\n      moveLeft = true;\n    }\n    if (dir.indexOf('s') > -1) {\n      newRect.height += offsetY;\n    } else if (dir.indexOf('n') > -1) {\n      newRect.height -= offsetY;\n      newRect.top += offsetY\n      moveUp = true;\n    }\n    const constrain = this._constrainSize(newRect.width, newRect.height, moveLeft, moveUp);\n    if (Math.round(newRect.width) !== Math.round(constrain.width)) { // round to ignore slight round-off errors\n      if (dir.indexOf('w') > -1) {\n        newRect.left += newRect.width - constrain.width;\n      }\n      newRect.width = constrain.width;\n    }\n    if (Math.round(newRect.height) !== Math.round(constrain.height)) {\n      if (dir.indexOf('n') > -1) {\n        newRect.top += newRect.height - constrain.height;\n      }\n      newRect.height = constrain.height;\n    }\n    return newRect;\n  }\n\n  /** @internal constrain the size to the set min/max values */\n  protected _constrainSize(oWidth: number, oHeight: number, moveLeft: boolean, moveUp: boolean): Size {\n    const o = this.option;\n    const maxWidth = (moveLeft ? o.maxWidthMoveLeft : o.maxWidth) || Number.MAX_SAFE_INTEGER;\n    const minWidth = o.minWidth / this.rectScale.x || oWidth;\n    const maxHeight = (moveUp ? o.maxHeightMoveUp : o.maxHeight) || Number.MAX_SAFE_INTEGER;\n    const minHeight = o.minHeight / this.rectScale.y || oHeight;\n    const width = Math.min(maxWidth, Math.max(minWidth, oWidth));\n    const height = Math.min(maxHeight, Math.max(minHeight, oHeight));\n    return { width, height };\n  }\n\n  /** @internal */\n  protected _applyChange(): DDResizable {\n    let containmentRect = { left: 0, top: 0, width: 0, height: 0 };\n    if (this.el.style.position === 'absolute') {\n      const containmentEl = this.el.parentElement;\n      const { left, top } = containmentEl.getBoundingClientRect();\n      containmentRect = { left, top, width: 0, height: 0 };\n    }\n    if (!this.temporalRect) return this;\n    Object.keys(this.temporalRect).forEach(key => {\n      const value = this.temporalRect[key];\n      const scaleReciprocal = key === 'width' || key === 'left' ? this.rectScale.x : key === 'height' || key === 'top' ? this.rectScale.y : 1;\n      this.el.style[key] = (value - containmentRect[key]) * scaleReciprocal + 'px';\n    });\n    return this;\n  }\n\n  /** @internal */\n  protected _removeHandlers(): DDResizable {\n    this.handlers.forEach(handle => handle.destroy());\n    delete this.handlers;\n    return this;\n  }\n\n  /** @internal */\n  protected _ui = (): DDUIData => {\n    const containmentEl = this.el.parentElement;\n    const containmentRect = containmentEl.getBoundingClientRect();\n    const newRect = { // Note: originalRect is a complex object, not a simple Rect, so copy out.\n      width: this.originalRect.width,\n      height: this.originalRect.height + this.scrolled,\n      left: this.originalRect.left,\n      top: this.originalRect.top - this.scrolled\n    };\n    const rect = this.temporalRect || newRect;\n    return {\n      position: {\n        left: (rect.left - containmentRect.left) * this.rectScale.x,\n        top: (rect.top - containmentRect.top) * this.rectScale.y\n      },\n      size: {\n        width: rect.width * this.rectScale.x,\n        height: rect.height * this.rectScale.y\n      }\n      /* Gridstack ONLY needs position set above... keep around in case.\n      element: [this.el], // The object representing the element to be resized\n      helper: [], // TODO: not support yet - The object representing the helper that's being resized\n      originalElement: [this.el],// we don't wrap here, so simplify as this.el //The object representing the original element before it is wrapped\n      originalPosition: { // The position represented as { left, top } before the resizable is resized\n        left: this.originalRect.left - containmentRect.left,\n        top: this.originalRect.top - containmentRect.top\n      },\n      originalSize: { // The size represented as { width, height } before the resizable is resized\n        width: this.originalRect.width,\n        height: this.originalRect.height\n      }\n      */\n    };\n  }\n}\n", "/**\n * dd-draggable.ts 12.2.2\n * Copyright (c) 2021-2024  <PERSON> - see GridStack root license\n */\n\nimport { DDManager } from './dd-manager';\nimport { DragTransform, Utils } from './utils';\nimport { DDBaseImplement, HTMLElementExtendOpt } from './dd-base-impl';\nimport { GridItemHTMLElement, DDUIData, GridStackNode, GridStackPosition, DDDragOpt } from './types';\nimport { DDElementHost } from './dd-element';\nimport { isTouch, touchend, touchmove, touchstart, pointerdown } from './dd-touch';\nimport { GridHTMLElement } from './gridstack';\n\ninterface DragOffset {\n  left: number;\n  top: number;\n  width: number;\n  height: number;\n  offsetLeft: number;\n  offsetTop: number;\n}\n\ninterface GridStackNodeRotate extends GridStackNode {\n  _origRotate?: GridStackPosition;\n}\n\ntype DDDragEvent = 'drag' | 'dragstart' | 'dragstop';\n\n// make sure we are not clicking on known object that handles mouseDown\nconst skipMouseDown = 'input,textarea,button,select,option,[contenteditable=\"true\"],.ui-resizable-handle';\n\n// let count = 0; // TEST\n\nexport class DDDraggable extends DDBaseImplement implements HTMLElementExtendOpt<DDDragOpt> {\n  public helper: HTMLElement; // used by GridStackDDNative\n\n  /** @internal */\n  protected mouseDownEvent: MouseEvent;\n  /** @internal */\n  protected dragOffset: DragOffset;\n  /** @internal */\n  protected dragElementOriginStyle: Array<string>;\n  /** @internal */\n  protected dragEls: HTMLElement[];\n  /** @internal true while we are dragging an item around */\n  protected dragging: boolean;\n  /** @internal last drag event */\n  protected lastDrag: DragEvent;\n  /** @internal */\n  protected parentOriginStylePosition: string;\n  /** @internal */\n  protected helperContainment: HTMLElement;\n  /** @internal properties we change during dragging, and restore back */\n  protected static originStyleProp = ['width', 'height', 'transform', 'transform-origin', 'transition', 'pointerEvents', 'position', 'left', 'top', 'minWidth', 'willChange'];\n  /** @internal pause before we call the actual drag hit collision code */\n  protected dragTimeout: number;\n  /** @internal */\n  protected dragTransform: DragTransform = {\n    xScale: 1,\n    yScale: 1,\n    xOffset: 0,\n    yOffset: 0\n  };\n\n  constructor(public el: GridItemHTMLElement, public option: DDDragOpt = {}) {\n    super();\n\n    // get the element that is actually supposed to be dragged by\n    const handleName = option?.handle?.substring(1);\n    const n = el.gridstackNode;\n    this.dragEls = !handleName || el.classList.contains(handleName) ? [el] : (n?.subGrid ? [el.querySelector(option.handle) || el] : Array.from(el.querySelectorAll(option.handle)));\n    if (this.dragEls.length === 0) {\n      this.dragEls = [el];\n    }\n    // create var event binding so we can easily remove and still look like TS methods (unlike anonymous functions)\n    this._mouseDown = this._mouseDown.bind(this);\n    this._mouseMove = this._mouseMove.bind(this);\n    this._mouseUp = this._mouseUp.bind(this);\n    this._keyEvent = this._keyEvent.bind(this);\n    this.enable();\n  }\n\n  public on(event: DDDragEvent, callback: (event: DragEvent) => void): void {\n    super.on(event, callback);\n  }\n\n  public off(event: DDDragEvent): void {\n    super.off(event);\n  }\n\n  public enable(): void {\n    if (this.disabled === false) return;\n    super.enable();\n    this.dragEls.forEach(dragEl => {\n      dragEl.addEventListener('mousedown', this._mouseDown);\n      if (isTouch) {\n        dragEl.addEventListener('touchstart', touchstart);\n        dragEl.addEventListener('pointerdown', pointerdown);\n        // dragEl.style.touchAction = 'none'; // not needed unlike pointerdown doc comment\n      }\n    });\n    this.el.classList.remove('ui-draggable-disabled');\n  }\n\n  public disable(forDestroy = false): void {\n    if (this.disabled === true) return;\n    super.disable();\n    this.dragEls.forEach(dragEl => {\n      dragEl.removeEventListener('mousedown', this._mouseDown);\n      if (isTouch) {\n        dragEl.removeEventListener('touchstart', touchstart);\n        dragEl.removeEventListener('pointerdown', pointerdown);\n      }\n    });\n    if (!forDestroy) this.el.classList.add('ui-draggable-disabled');\n  }\n\n  public destroy(): void {\n    if (this.dragTimeout) window.clearTimeout(this.dragTimeout);\n    delete this.dragTimeout;\n    if (this.mouseDownEvent) this._mouseUp(this.mouseDownEvent);\n    this.disable(true);\n    delete this.el;\n    delete this.helper;\n    delete this.option;\n    super.destroy();\n  }\n\n  public updateOption(opts: DDDragOpt): DDDraggable {\n    Object.keys(opts).forEach(key => this.option[key] = opts[key]);\n    return this;\n  }\n\n  /** @internal call when mouse goes down before a dragstart happens */\n  protected _mouseDown(e: MouseEvent): boolean {\n    // don't let more than one widget handle mouseStart\n    if (DDManager.mouseHandled) return;\n    if (e.button !== 0) return true; // only left click\n\n    // make sure we are not clicking on known object that handles mouseDown, or ones supplied by the user\n    if (!this.dragEls.find(el => el === e.target) && (e.target as HTMLElement).closest(skipMouseDown)) return true;\n    if (this.option.cancel) {\n      if ((e.target as HTMLElement).closest(this.option.cancel)) return true;\n    }\n\n    this.mouseDownEvent = e;\n    delete this.dragging;\n    delete DDManager.dragElement;\n    delete DDManager.dropElement;\n    // document handler so we can continue receiving moves as the item is 'fixed' position, and capture=true so WE get a first crack\n    document.addEventListener('mousemove', this._mouseMove, { capture: true, passive: true }); // true=capture, not bubble\n    document.addEventListener('mouseup', this._mouseUp, true);\n    if (isTouch) {\n      e.currentTarget.addEventListener('touchmove', touchmove);\n      e.currentTarget.addEventListener('touchend', touchend);\n    }\n\n    e.preventDefault();\n    // preventDefault() prevents blur event which occurs just after mousedown event.\n    // if an editable content has focus, then blur must be call\n    if (document.activeElement) (document.activeElement as HTMLElement).blur();\n\n    DDManager.mouseHandled = true;\n    return true;\n  }\n\n  /** @internal method to call actual drag event */\n  protected _callDrag(e: DragEvent): void {\n    if (!this.dragging) return;\n    const ev = Utils.initEvent<DragEvent>(e, { target: this.el, type: 'drag' });\n    if (this.option.drag) {\n      this.option.drag(ev, this.ui());\n    }\n    this.triggerEvent('drag', ev);\n  }\n\n  /** @internal called when the main page (after successful mousedown) receives a move event to drag the item around the screen */\n  protected _mouseMove(e: DragEvent): boolean {\n    // console.log(`${count++} move ${e.x},${e.y}`)\n    const s = this.mouseDownEvent;\n    this.lastDrag = e;\n\n    if (this.dragging) {\n      this._dragFollow(e);\n      // delay actual grid handling drag until we pause for a while if set\n      if (DDManager.pauseDrag) {\n        const pause = Number.isInteger(DDManager.pauseDrag) ? DDManager.pauseDrag as number : 100;\n        if (this.dragTimeout) window.clearTimeout(this.dragTimeout);\n        this.dragTimeout = window.setTimeout(() => this._callDrag(e), pause);\n      } else {\n        this._callDrag(e);\n      }\n    } else if (Math.abs(e.x - s.x) + Math.abs(e.y - s.y) > 3) {\n      /**\n       * don't start unless we've moved at least 3 pixels\n       */\n      this.dragging = true;\n      DDManager.dragElement = this;\n      // if we're dragging an actual grid item, set the current drop as the grid (to detect enter/leave)\n      const grid = this.el.gridstackNode?.grid;\n      if (grid) {\n        DDManager.dropElement = (grid.el as DDElementHost).ddElement.ddDroppable;\n      } else {\n        delete DDManager.dropElement;\n      }\n      this.helper = this._createHelper();\n      this._setupHelperContainmentStyle();\n      this.dragTransform = Utils.getValuesFromTransformedElement(this.helperContainment);\n      this.dragOffset = this._getDragOffset(e, this.el, this.helperContainment);\n      this._setupHelperStyle(e);\n\n      const ev = Utils.initEvent<DragEvent>(e, { target: this.el, type: 'dragstart' });\n      if (this.option.start) {\n        this.option.start(ev, this.ui());\n      }\n      this.triggerEvent('dragstart', ev);\n      // now track keyboard events to cancel or rotate\n      document.addEventListener('keydown', this._keyEvent);\n    }\n    // e.preventDefault(); // passive = true. OLD: was needed otherwise we get text sweep text selection as we drag around\n    return true;\n  }\n\n  /** @internal call when the mouse gets released to drop the item at current location */\n  protected _mouseUp(e: MouseEvent): void {\n    document.removeEventListener('mousemove', this._mouseMove, true);\n    document.removeEventListener('mouseup', this._mouseUp, true);\n    if (isTouch && e.currentTarget) { // destroy() during nested grid call us again wit fake _mouseUp\n      e.currentTarget.removeEventListener('touchmove', touchmove, true);\n      e.currentTarget.removeEventListener('touchend', touchend, true);\n    }\n    if (this.dragging) {\n      delete this.dragging;\n      delete (this.el.gridstackNode as GridStackNodeRotate)?._origRotate;\n      document.removeEventListener('keydown', this._keyEvent);\n\n      // reset the drop target if dragging over ourself (already parented, just moving during stop callback below)\n      if (DDManager.dropElement?.el === this.el.parentElement) {\n        delete DDManager.dropElement;\n      }\n\n      this.helperContainment.style.position = this.parentOriginStylePosition || null;\n      if (this.helper !== this.el) this.helper.remove(); // hide now\n      this._removeHelperStyle();\n\n      const ev = Utils.initEvent<DragEvent>(e, { target: this.el, type: 'dragstop' });\n      if (this.option.stop) {\n        this.option.stop(ev); // NOTE: destroy() will be called when removing item, so expect NULL ptr after!\n      }\n      this.triggerEvent('dragstop', ev);\n\n      // call the droppable method to receive the item\n      if (DDManager.dropElement) {\n        DDManager.dropElement.drop(e);\n      }\n    }\n    delete this.helper;\n    delete this.mouseDownEvent;\n    delete DDManager.dragElement;\n    delete DDManager.dropElement;\n    delete DDManager.mouseHandled;\n    e.preventDefault();\n  }\n\n  /** @internal call when keys are being pressed - use Esc to cancel, R to rotate */\n  protected _keyEvent(e: KeyboardEvent): void {\n    const n = this.el.gridstackNode as GridStackNodeRotate;\n    const grid = n?.grid || (DDManager.dropElement?.el as GridHTMLElement)?.gridstack;\n\n    if (e.key === 'Escape') {\n      if (n && n._origRotate) {\n        n._orig = n._origRotate;\n        delete n._origRotate;\n      }\n      grid?.cancelDrag();\n      this._mouseUp(this.mouseDownEvent);\n    } else if (n && grid && (e.key === 'r' || e.key === 'R')) {\n      if (!Utils.canBeRotated(n)) return;\n      n._origRotate = n._origRotate || { ...n._orig }; // store the real orig size in case we Esc after doing rotation\n      delete n._moving; // force rotate to happen (move waits for >50% coverage otherwise)\n      grid.setAnimation(false) // immediate rotate so _getDragOffset() gets the right dom size below\n        .rotate(n.el, { top: -this.dragOffset.offsetTop, left: -this.dragOffset.offsetLeft })\n        .setAnimation();\n      n._moving = true;\n      this.dragOffset = this._getDragOffset(this.lastDrag, n.el, this.helperContainment);\n      this.helper.style.width = this.dragOffset.width + 'px';\n      this.helper.style.height = this.dragOffset.height + 'px';\n      Utils.swap(n._orig, 'w', 'h');\n      delete n._rect;\n      this._mouseMove(this.lastDrag);\n    }\n  }\n\n  /** @internal create a clone copy (or user defined method) of the original drag item if set */\n  protected _createHelper(): HTMLElement {\n    let helper = this.el;\n    if (typeof this.option.helper === 'function') {\n      helper = this.option.helper(this.el);\n    } else if (this.option.helper === 'clone') {\n      helper = Utils.cloneNode(this.el);\n    }\n    if (!helper.parentElement) {\n      Utils.appendTo(helper, this.option.appendTo === 'parent' ? this.el.parentElement : this.option.appendTo);\n    }\n    this.dragElementOriginStyle = DDDraggable.originStyleProp.map(prop => this.el.style[prop]);\n    return helper;\n  }\n\n  /** @internal set the fix position of the dragged item */\n  protected _setupHelperStyle(e: DragEvent): DDDraggable {\n    this.helper.classList.add('ui-draggable-dragging');\n    // TODO: set all at once with style.cssText += ... ? https://stackoverflow.com/questions/3968593\n    const style = this.helper.style;\n    style.pointerEvents = 'none'; // needed for over items to get enter/leave\n    // style.cursor = 'move'; //  TODO: can't set with pointerEvents=none ! (no longer in CSS either as no-op)\n    style.width = this.dragOffset.width + 'px';\n    style.height = this.dragOffset.height + 'px';\n    style.willChange = 'left, top';\n    style.position = 'fixed'; // let us drag between grids by not clipping as parent .grid-stack is position: 'relative'\n    this._dragFollow(e); // now position it\n    style.transition = 'none'; // show up instantly\n    setTimeout(() => {\n      if (this.helper) {\n        style.transition = null; // recover animation\n      }\n    }, 0);\n    return this;\n  }\n\n  /** @internal restore back the original style before dragging */\n  protected _removeHelperStyle(): DDDraggable {\n    this.helper.classList.remove('ui-draggable-dragging');\n    const node = (this.helper as GridItemHTMLElement)?.gridstackNode;\n    // don't bother restoring styles if we're gonna remove anyway...\n    if (!node?._isAboutToRemove && this.dragElementOriginStyle) {\n      const helper = this.helper;\n      // don't animate, otherwise we animate offseted when switching back to 'absolute' from 'fixed'.\n      // TODO: this also removes resizing animation which doesn't have this issue, but others.\n      // Ideally both would animate ('move' would immediately restore 'absolute' and adjust coordinate to match,\n      // then trigger a delay (repaint) to restore to final dest with animate) but then we need to make sure 'resizestop'\n      // is called AFTER 'transitionend' event is received (see https://github.com/gridstack/gridstack.js/issues/2033)\n      const transition = this.dragElementOriginStyle['transition'] || null;\n      helper.style.transition = this.dragElementOriginStyle['transition'] = 'none'; // can't be NULL #1973\n      DDDraggable.originStyleProp.forEach(prop => helper.style[prop] = this.dragElementOriginStyle[prop] || null);\n      setTimeout(() => helper.style.transition = transition, 50); // recover animation from saved vars after a pause (0 isn't enough #1973)\n    }\n    delete this.dragElementOriginStyle;\n    return this;\n  }\n\n  /** @internal updates the top/left position to follow the mouse */\n  protected _dragFollow(e: DragEvent): void {\n    const containmentRect = { left: 0, top: 0 };\n    // if (this.helper.style.position === 'absolute') { // we use 'fixed'\n    //   const { left, top } = this.helperContainment.getBoundingClientRect();\n    //   containmentRect = { left, top };\n    // }\n    const style = this.helper.style;\n    const offset = this.dragOffset;\n    style.left = (e.clientX + offset.offsetLeft - containmentRect.left) * this.dragTransform.xScale + 'px';\n    style.top = (e.clientY + offset.offsetTop - containmentRect.top) * this.dragTransform.yScale + 'px';\n  }\n\n  /** @internal */\n  protected _setupHelperContainmentStyle(): DDDraggable {\n    this.helperContainment = this.helper.parentElement;\n    if (this.helper.style.position !== 'fixed') {\n      this.parentOriginStylePosition = this.helperContainment.style.position;\n      if (getComputedStyle(this.helperContainment).position.match(/static/)) {\n        this.helperContainment.style.position = 'relative';\n      }\n    }\n    return this;\n  }\n\n  /** @internal */\n  protected _getDragOffset(event: DragEvent, el: HTMLElement, parent: HTMLElement): DragOffset {\n\n    // in case ancestor has transform/perspective css properties that change the viewpoint\n    let xformOffsetX = 0;\n    let xformOffsetY = 0;\n    if (parent) {\n      xformOffsetX = this.dragTransform.xOffset;\n      xformOffsetY = this.dragTransform.yOffset;\n    }\n\n    const targetOffset = el.getBoundingClientRect();\n    return {\n      left: targetOffset.left,\n      top: targetOffset.top,\n      offsetLeft: - event.clientX + targetOffset.left - xformOffsetX,\n      offsetTop: - event.clientY + targetOffset.top - xformOffsetY,\n      width: targetOffset.width * this.dragTransform.xScale,\n      height: targetOffset.height * this.dragTransform.yScale\n    };\n  }\n\n  /** @internal TODO: set to public as called by DDDroppable! */\n  public ui(): DDUIData {\n    const containmentEl = this.el.parentElement;\n    const containmentRect = containmentEl.getBoundingClientRect();\n    const offset = this.helper.getBoundingClientRect();\n    return {\n      position: { //Current CSS position of the helper as { top, left } object\n        top: (offset.top - containmentRect.top) * this.dragTransform.yScale,\n        left: (offset.left - containmentRect.left) * this.dragTransform.xScale\n      }\n      /* not used by GridStack for now...\n      helper: [this.helper], //The object arr representing the helper that's being dragged.\n      offset: { top: offset.top, left: offset.left } // Current offset position of the helper as { top, left } object.\n      */\n    };\n  }\n}\n", "/**\n * dd-droppable.ts 12.2.2\n * Copyright (c) 2021-2024  <PERSON> - see GridStack root license\n */\n\nimport { DDDraggable } from './dd-draggable';\nimport { DDManager } from './dd-manager';\nimport { DDBaseImplement, HTMLElementExtendOpt } from './dd-base-impl';\nimport { Utils } from './utils';\nimport { DDElementHost } from './dd-element';\nimport { isTouch, pointerenter, pointerleave } from './dd-touch';\nimport { DDUIData } from './types';\n\nexport interface DDDroppableOpt {\n  accept?: string | ((el: HTMLElement) => boolean);\n  drop?: (event: DragEvent, ui: DDUIData) => void;\n  over?: (event: DragEvent, ui: DDUIData) => void;\n  out?: (event: DragEvent, ui: DDUIData) => void;\n}\n\n// let count = 0; // TEST\n\nexport class DDDroppable extends DDBaseImplement implements HTMLElementExtendOpt<DDDroppableOpt> {\n\n  public accept: (el: HTMLElement) => boolean;\n\n  constructor(public el: HTMLElement, public option: DDDroppableOpt = {}) {\n    super();\n    // create var event binding so we can easily remove and still look like TS methods (unlike anonymous functions)\n    this._mouseEnter = this._mouseEnter.bind(this);\n    this._mouseLeave = this._mouseLeave.bind(this);\n    this.enable();\n    this._setupAccept();\n  }\n\n  public on(event: 'drop' | 'dropover' | 'dropout', callback: (event: DragEvent) => void): void {\n    super.on(event, callback);\n  }\n\n  public off(event: 'drop' | 'dropover' | 'dropout'): void {\n    super.off(event);\n  }\n\n  public enable(): void {\n    if (this.disabled === false) return;\n    super.enable();\n    this.el.classList.add('ui-droppable');\n    this.el.classList.remove('ui-droppable-disabled');\n    this.el.addEventListener('mouseenter', this._mouseEnter);\n    this.el.addEventListener('mouseleave', this._mouseLeave);\n    if (isTouch) {\n      this.el.addEventListener('pointerenter', pointerenter);\n      this.el.addEventListener('pointerleave', pointerleave);\n    }\n  }\n\n  public disable(forDestroy = false): void {\n    if (this.disabled === true) return;\n    super.disable();\n    this.el.classList.remove('ui-droppable');\n    if (!forDestroy) this.el.classList.add('ui-droppable-disabled');\n    this.el.removeEventListener('mouseenter', this._mouseEnter);\n    this.el.removeEventListener('mouseleave', this._mouseLeave);\n    if (isTouch) {\n      this.el.removeEventListener('pointerenter', pointerenter);\n      this.el.removeEventListener('pointerleave', pointerleave);\n    }\n  }\n\n  public destroy(): void {\n    this.disable(true);\n    this.el.classList.remove('ui-droppable');\n    this.el.classList.remove('ui-droppable-disabled');\n    super.destroy();\n  }\n\n  public updateOption(opts: DDDroppableOpt): DDDroppable {\n    Object.keys(opts).forEach(key => this.option[key] = opts[key]);\n    this._setupAccept();\n    return this;\n  }\n\n  /** @internal called when the cursor enters our area - prepare for a possible drop and track leaving */\n  protected _mouseEnter(e: MouseEvent): void {\n    // console.log(`${count++} Enter ${this.el.id || (this.el as GridHTMLElement).gridstack.opts.id}`); // TEST\n    if (!DDManager.dragElement) return;\n    if (!this._canDrop(DDManager.dragElement.el)) return;\n    e.preventDefault();\n    e.stopPropagation();\n\n    // make sure when we enter this, that the last one gets a leave FIRST to correctly cleanup as we don't always do\n    if (DDManager.dropElement && DDManager.dropElement !== this) {\n      DDManager.dropElement._mouseLeave(e as DragEvent, true); // calledByEnter = true\n    }\n    DDManager.dropElement = this;\n\n    const ev = Utils.initEvent<DragEvent>(e, { target: this.el, type: 'dropover' });\n    if (this.option.over) {\n      this.option.over(ev, this._ui(DDManager.dragElement))\n    }\n    this.triggerEvent('dropover', ev);\n    this.el.classList.add('ui-droppable-over');\n    // console.log('tracking'); // TEST\n  }\n\n  /** @internal called when the item is leaving our area, stop tracking if we had moving item */\n  protected _mouseLeave(e: MouseEvent, calledByEnter = false): void {\n    // console.log(`${count++} Leave ${this.el.id || (this.el as GridHTMLElement).gridstack.opts.id}`); // TEST\n    if (!DDManager.dragElement || DDManager.dropElement !== this) return;\n    e.preventDefault();\n    e.stopPropagation();\n\n    const ev = Utils.initEvent<DragEvent>(e, { target: this.el, type: 'dropout' });\n    if (this.option.out) {\n      this.option.out(ev, this._ui(DDManager.dragElement))\n    }\n    this.triggerEvent('dropout', ev);\n\n    if (DDManager.dropElement === this) {\n      delete DDManager.dropElement;\n      // console.log('not tracking'); // TEST\n\n      // if we're still over a parent droppable, send it an enter as we don't get one from leaving nested children\n      if (!calledByEnter) {\n        let parentDrop: DDDroppable;\n        let parent: DDElementHost = this.el.parentElement;\n        while (!parentDrop && parent) {\n          parentDrop = parent.ddElement?.ddDroppable;\n          parent = parent.parentElement;\n        }\n        if (parentDrop) {\n          parentDrop._mouseEnter(e);\n        }\n      }\n    }\n  }\n\n  /** item is being dropped on us - called by the drag mouseup handler - this calls the client drop event */\n  public drop(e: MouseEvent): void {\n    e.preventDefault();\n    const ev = Utils.initEvent<DragEvent>(e, { target: this.el, type: 'drop' });\n    if (this.option.drop) {\n      this.option.drop(ev, this._ui(DDManager.dragElement))\n    }\n    this.triggerEvent('drop', ev);\n  }\n\n  /** @internal true if element matches the string/method accept option */\n  protected _canDrop(el: HTMLElement): boolean {\n    return el && (!this.accept || this.accept(el));\n  }\n\n  /** @internal */\n  protected _setupAccept(): DDDroppable {\n    if (!this.option.accept) return this;\n    if (typeof this.option.accept === 'string') {\n      this.accept = (el: HTMLElement) => el.classList.contains(this.option.accept as string) || el.matches(this.option.accept as string);\n    } else {\n      this.accept = this.option.accept;\n    }\n    return this;\n  }\n\n  /** @internal */\n  protected _ui(drag: DDDraggable): DDUIData {\n    return {\n      draggable: drag.el,\n      ...drag.ui()\n    };\n  }\n}\n\n", "/**\n * dd-elements.ts 12.2.2\n * Copyright (c) 2021-2024 <PERSON> - see GridStack root license\n */\n\nimport { DDResizable, DDResizableOpt } from './dd-resizable';\nimport { DDDragOpt, GridItemHTMLElement } from './types';\nimport { DDDraggable } from './dd-draggable';\nimport { DDDroppable, DDDroppableOpt } from './dd-droppable';\n\nexport interface DDElementHost extends GridItemHTMLElement {\n  ddElement?: DDElement;\n}\n\nexport class DDElement {\n\n  static init(el: DDElementHost): DDElement {\n    if (!el.ddElement) { el.ddElement = new DDElement(el); }\n    return el.ddElement;\n  }\n\n  public ddDraggable?: DDDraggable;\n  public ddDroppable?: DDDroppable;\n  public ddResizable?: DDResizable;\n\n  constructor(public el: DDElementHost) {}\n\n  public on(eventName: string, callback: (event: MouseEvent) => void): DDElement {\n    if (this.ddDraggable && ['drag', 'dragstart', 'dragstop'].indexOf(eventName) > -1) {\n      this.ddDraggable.on(eventName as 'drag' | 'dragstart' | 'dragstop', callback);\n    } else if (this.ddDroppable && ['drop', 'dropover', 'dropout'].indexOf(eventName) > -1) {\n      this.ddDroppable.on(eventName as 'drop' | 'dropover' | 'dropout', callback);\n    } else if (this.ddResizable && ['resizestart', 'resize', 'resizestop'].indexOf(eventName) > -1) {\n      this.ddResizable.on(eventName as 'resizestart' | 'resize' | 'resizestop', callback);\n    }\n    return this;\n  }\n\n  public off(eventName: string): DDElement {\n    if (this.ddDraggable && ['drag', 'dragstart', 'dragstop'].indexOf(eventName) > -1) {\n      this.ddDraggable.off(eventName as 'drag' | 'dragstart' | 'dragstop');\n    } else if (this.ddDroppable && ['drop', 'dropover', 'dropout'].indexOf(eventName) > -1) {\n      this.ddDroppable.off(eventName as 'drop' | 'dropover' | 'dropout');\n    } else if (this.ddResizable && ['resizestart', 'resize', 'resizestop'].indexOf(eventName) > -1) {\n      this.ddResizable.off(eventName as 'resizestart' | 'resize' | 'resizestop');\n    }\n    return this;\n  }\n\n  public setupDraggable(opts: DDDragOpt): DDElement {\n    if (!this.ddDraggable) {\n      this.ddDraggable = new DDDraggable(this.el, opts);\n    } else {\n      this.ddDraggable.updateOption(opts);\n    }\n    return this;\n  }\n\n  public cleanDraggable(): DDElement {\n    if (this.ddDraggable) {\n      this.ddDraggable.destroy();\n      delete this.ddDraggable;\n    }\n    return this;\n  }\n\n  public setupResizable(opts: DDResizableOpt): DDElement {\n    if (!this.ddResizable) {\n      this.ddResizable = new DDResizable(this.el, opts);\n    } else {\n      this.ddResizable.updateOption(opts);\n    }\n    return this;\n  }\n\n  public cleanResizable(): DDElement {\n    if (this.ddResizable) {\n      this.ddResizable.destroy();\n      delete this.ddResizable;\n    }\n    return this;\n  }\n\n  public setupDroppable(opts: DDDroppableOpt): DDElement {\n    if (!this.ddDroppable) {\n      this.ddDroppable = new DDDroppable(this.el, opts);\n    } else {\n      this.ddDroppable.updateOption(opts);\n    }\n    return this;\n  }\n\n  public cleanDroppable(): DDElement {\n    if (this.ddDroppable) {\n      this.ddDroppable.destroy();\n      delete this.ddDroppable;\n    }\n    return this;\n  }\n}\n", "/**\r\n * dd-gridstack.ts 12.2.2\r\n * Copyright (c) 2021-2024 <PERSON> - see GridStack root license\r\n */\r\n\r\n/* eslint-disable @typescript-eslint/no-unused-vars */\r\nimport { GridItemHTMLElement, GridStackElement, DDDragOpt } from './types';\r\nimport { Utils } from './utils';\r\nimport { DDManager } from './dd-manager';\r\nimport { DDElement, DDElementHost } from './dd-element';\r\nimport { GridHTMLElement } from './gridstack';\r\n\r\n/** Drag&Drop drop options */\r\nexport type DDDropOpt = {\r\n  /** function or class type that this grid will accept as dropped items (see GridStackOptions.acceptWidgets) */\r\n  accept?: (el: GridItemHTMLElement) => boolean;\r\n}\r\n\r\n/** drag&drop options currently called from the main code, but others can be passed in grid options */\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nexport type DDOpts = 'enable' | 'disable' | 'destroy' | 'option' | string | any;\r\nexport type DDKey = 'minWidth' | 'minHeight' | 'maxWidth' | 'maxHeight' | 'maxHeightMoveUp' | 'maxWidthMoveLeft';\r\nexport type DDValue = number | string;\r\n\r\n/** drag&drop events callbacks */\r\nexport type DDCallback = (event: Event, arg2: GridItemHTMLElement, helper?: GridItemHTMLElement) => void;\r\n\r\n// let count = 0; // TEST\r\n\r\n/**\r\n * HTML Native Mouse and Touch Events Drag and Drop functionality.\r\n */\r\nexport class DDGridStack {\r\n\r\n  public resizable(el: GridItemHTMLElement, opts: DDOpts, key?: DDKey, value?: DDValue): DDGridStack {\r\n    this._getDDElements(el, opts).forEach(dEl => {\r\n      if (opts === 'disable' || opts === 'enable') {\r\n        dEl.ddResizable && dEl.ddResizable[opts](); // can't create DD as it requires options for setupResizable()\r\n      } else if (opts === 'destroy') {\r\n        dEl.ddResizable && dEl.cleanResizable();\r\n      } else if (opts === 'option') {\r\n        dEl.setupResizable({ [key]: value });\r\n      } else {\r\n        const n = dEl.el.gridstackNode;\r\n        const grid = n.grid;\r\n        let handles = dEl.el.getAttribute('gs-resize-handles') || grid.opts.resizable.handles || 'e,s,se';\r\n        if (handles === 'all') handles = 'n,e,s,w,se,sw,ne,nw';\r\n        // NOTE: keep the resize handles as e,w don't have enough space (10px) to show resize corners anyway. limit during drag instead\r\n        // restrict vertical resize if height is done to match content anyway... odd to have it spring back\r\n        // if (Utils.shouldSizeToContent(n, true)) {\r\n        //   const doE = handles.indexOf('e') !== -1;\r\n        //   const doW = handles.indexOf('w') !== -1;\r\n        //   handles = doE ? (doW ? 'e,w' : 'e') : (doW ? 'w' : '');\r\n        // }\r\n        const autoHide = !grid.opts.alwaysShowResizeHandle;\r\n        dEl.setupResizable({\r\n          ...grid.opts.resizable,\r\n          ...{ handles, autoHide },\r\n          ...{\r\n            start: opts.start,\r\n            stop: opts.stop,\r\n            resize: opts.resize\r\n          }\r\n        });\r\n      }\r\n    });\r\n    return this;\r\n  }\r\n\r\n  public draggable(el: GridItemHTMLElement, opts: DDOpts, key?: DDKey, value?: DDValue): DDGridStack {\r\n    this._getDDElements(el, opts).forEach(dEl => {\r\n      if (opts === 'disable' || opts === 'enable') {\r\n        dEl.ddDraggable && dEl.ddDraggable[opts](); // can't create DD as it requires options for setupDraggable()\r\n      } else if (opts === 'destroy') {\r\n        dEl.ddDraggable && dEl.cleanDraggable();\r\n      } else if (opts === 'option') {\r\n        dEl.setupDraggable({ [key]: value });\r\n      } else {\r\n        const grid = dEl.el.gridstackNode.grid;\r\n        dEl.setupDraggable({\r\n          ...grid.opts.draggable,\r\n          ...{\r\n            // containment: (grid.parentGridNode && grid.opts.dragOut === false) ? grid.el.parentElement : (grid.opts.draggable.containment || null),\r\n            start: opts.start,\r\n            stop: opts.stop,\r\n            drag: opts.drag\r\n          }\r\n        });\r\n      }\r\n    });\r\n    return this;\r\n  }\r\n\r\n  public dragIn(el: GridStackElement, opts: DDDragOpt): DDGridStack {\r\n    this._getDDElements(el).forEach(dEl => dEl.setupDraggable(opts));\r\n    return this;\r\n  }\r\n\r\n  public droppable(el: GridItemHTMLElement, opts: DDOpts | DDDropOpt, key?: DDKey, value?: DDValue): DDGridStack {\r\n    if (typeof opts.accept === 'function' && !opts._accept) {\r\n      opts._accept = opts.accept;\r\n      opts.accept = (el) => opts._accept(el);\r\n    }\r\n    this._getDDElements(el, opts).forEach(dEl => {\r\n      if (opts === 'disable' || opts === 'enable') {\r\n        dEl.ddDroppable && dEl.ddDroppable[opts]();\r\n      } else if (opts === 'destroy') {\r\n        dEl.ddDroppable && dEl.cleanDroppable();\r\n      } else if (opts === 'option') {\r\n        dEl.setupDroppable({ [key]: value });\r\n      } else {\r\n        dEl.setupDroppable(opts);\r\n      }\r\n    });\r\n    return this;\r\n  }\r\n\r\n  /** true if element is droppable */\r\n  public isDroppable(el: DDElementHost): boolean {\r\n    return !!(el?.ddElement?.ddDroppable && !el.ddElement.ddDroppable.disabled);\r\n  }\r\n\r\n  /** true if element is draggable */\r\n  public isDraggable(el: DDElementHost): boolean {\r\n    return !!(el?.ddElement?.ddDraggable && !el.ddElement.ddDraggable.disabled);\r\n  }\r\n\r\n  /** true if element is draggable */\r\n  public isResizable(el: DDElementHost): boolean {\r\n    return !!(el?.ddElement?.ddResizable && !el.ddElement.ddResizable.disabled);\r\n  }\r\n\r\n  public on(el: GridItemHTMLElement, name: string, callback: DDCallback): DDGridStack {\r\n    this._getDDElements(el).forEach(dEl =>\r\n      dEl.on(name, (event: Event) => {\r\n        callback(\r\n          event,\r\n          DDManager.dragElement ? DDManager.dragElement.el : event.target as GridItemHTMLElement,\r\n          DDManager.dragElement ? DDManager.dragElement.helper : null)\r\n      })\r\n    );\r\n    return this;\r\n  }\r\n\r\n  public off(el: GridItemHTMLElement, name: string): DDGridStack {\r\n    this._getDDElements(el).forEach(dEl => dEl.off(name));\r\n    return this;\r\n  }\r\n\r\n  /** @internal returns a list of DD elements, creating them on the fly by default unless option is to destroy or disable */\r\n  protected _getDDElements(els: GridStackElement, opts?: DDOpts): DDElement[] {\r\n    // don't force create if we're going to destroy it, unless it's a grid which is used as drop target for it's children\r\n    const create = (els as GridHTMLElement).gridstack ||  opts !== 'destroy' && opts !== 'disable';\r\n    const hosts = Utils.getElements(els) as DDElementHost[];\r\n    if (!hosts.length) return [];\r\n    const list = hosts.map(e => e.ddElement || (create ? DDElement.init(e) : null)).filter(d => d); // remove nulls\r\n    return list;\r\n  }\r\n}\r\n", "/*!\r\n * GridStack 12.2.2\r\n * https://gridstackjs.com/\r\n *\r\n * Copyright (c) 2021-2024  <PERSON>\r\n * see root license https://github.com/gridstack/gridstack.js/tree/master/LICENSE\r\n */\r\nimport { GridStackEngine } from './gridstack-engine';\r\nimport { Utils, HeightData, obsolete, DragTransform } from './utils';\r\nimport {\r\n  gridDefaults, ColumnOptions, GridItemHTMLElement, GridStackElement, GridStackEventHandlerCallback,\r\n  GridStackNode, GridStackWidget, numberOrString, DDUIData, DDDragOpt, GridStackPosition, GridStackOptions,\r\n  GridStackEventHandler, GridStackNodesHandler, AddRemoveFcn, SaveFcn, CompactOptions, GridStackMoveOpts, ResizeToContentFcn, GridStackDroppedHandler, GridStackElementHandler,\r\n  Position, RenderFcn\r\n} from './types';\r\n\r\n/*\r\n * and include D&D by default\r\n * TODO: while we could generate a gridstack-static.js at smaller size - saves about 31k (41k -> 72k)\r\n * I don't know how to generate the DD only code at the remaining 31k to delay load as code depends on Gridstack.ts\r\n * also it caused loading issues in prod - see https://github.com/gridstack/gridstack.js/issues/2039\r\n */\r\nimport { DDGridStack } from './dd-gridstack';\r\nimport { isTouch } from './dd-touch';\r\nimport { DDManager } from './dd-manager';\r\nimport { DDElementHost } from './dd-element'; /** global instance */\r\nconst dd = new DDGridStack;\r\n\r\n// export all dependent file as well to make it easier for users to just import the main file\r\nexport * from './types';\r\nexport * from './utils';\r\nexport * from './gridstack-engine';\r\nexport * from './dd-gridstack';\r\n\r\nexport interface GridHTMLElement extends HTMLElement {\r\n  gridstack?: GridStack; // grid's parent DOM element points back to grid class\r\n}\r\n/** list of possible events, or space separated list of them */\r\nexport type GridStackEvent = 'added' | 'change' | 'disable' | 'drag' | 'dragstart' | 'dragstop' | 'dropped' |\r\n  'enable' | 'removed' | 'resize' | 'resizestart' | 'resizestop' | 'resizecontent';\r\n\r\n/** Defines the coordinates of an object */\r\nexport interface MousePosition {\r\n  top: number;\r\n  left: number;\r\n}\r\n\r\n/** Defines the position of a cell inside the grid*/\r\nexport interface CellPosition {\r\n  x: number;\r\n  y: number;\r\n}\r\n\r\n// extend with internal fields we need - TODO: move other items in here\r\ninterface InternalGridStackOptions extends GridStackOptions {\r\n  _alwaysShowResizeHandle?: true | false | 'mobile'; // so we can restore for save\r\n}\r\n\r\n/**\r\n * Main gridstack class - you will need to call `GridStack.init()` first to initialize your grid.\r\n * Note: your grid elements MUST have the following classes for the CSS layout to work:\r\n * @example\r\n * <div class=\"grid-stack\">\r\n *   <div class=\"grid-stack-item\">\r\n *     <div class=\"grid-stack-item-content\">Item 1</div>\r\n *   </div>\r\n * </div>\r\n */\r\nexport class GridStack {\r\n\r\n  /**\r\n   * initializing the HTML element, or selector string, into a grid will return the grid. Calling it again will\r\n   * simply return the existing instance (ignore any passed options). There is also an initAll() version that support\r\n   * multiple grids initialization at once. Or you can use addGrid() to create the entire grid from JSON.\r\n   * @param options grid options (optional)\r\n   * @param elOrString element or CSS selector (first one used) to convert to a grid (default to '.grid-stack' class selector)\r\n   *\r\n   * @example\r\n   * const grid = GridStack.init();\r\n   *\r\n   * Note: the HTMLElement (of type GridHTMLElement) will store a `gridstack: GridStack` value that can be retrieve later\r\n   * const grid = document.querySelector('.grid-stack').gridstack;\r\n   */\r\n  public static init(options: GridStackOptions = {}, elOrString: GridStackElement = '.grid-stack'): GridStack {\r\n    if (typeof document === 'undefined') return null; // temp workaround SSR\r\n    const el = GridStack.getGridElement(elOrString);\r\n    if (!el) {\r\n      if (typeof elOrString === 'string') {\r\n        console.error('GridStack.initAll() no grid was found with selector \"' + elOrString + '\" - element missing or wrong selector ?' +\r\n          '\\nNote: \".grid-stack\" is required for proper CSS styling and drag/drop, and is the default selector.');\r\n      } else {\r\n        console.error('GridStack.init() no grid element was passed.');\r\n      }\r\n      return null;\r\n    }\r\n    if (!el.gridstack) {\r\n      el.gridstack = new GridStack(el, Utils.cloneDeep(options));\r\n    }\r\n    return el.gridstack\r\n  }\r\n\r\n  /**\r\n   * Will initialize a list of elements (given a selector) and return an array of grids.\r\n   * @param options grid options (optional)\r\n   * @param selector elements selector to convert to grids (default to '.grid-stack' class selector)\r\n   *\r\n   * @example\r\n   * const grids = GridStack.initAll();\r\n   * grids.forEach(...)\r\n   */\r\n  public static initAll(options: GridStackOptions = {}, selector = '.grid-stack'): GridStack[] {\r\n    const grids: GridStack[] = [];\r\n    if (typeof document === 'undefined') return grids; // temp workaround SSR\r\n    GridStack.getGridElements(selector).forEach(el => {\r\n      if (!el.gridstack) {\r\n        el.gridstack = new GridStack(el, Utils.cloneDeep(options));\r\n      }\r\n      grids.push(el.gridstack);\r\n    });\r\n    if (grids.length === 0) {\r\n      console.error('GridStack.initAll() no grid was found with selector \"' + selector + '\" - element missing or wrong selector ?' +\r\n        '\\nNote: \".grid-stack\" is required for proper CSS styling and drag/drop, and is the default selector.');\r\n    }\r\n    return grids;\r\n  }\r\n\r\n  /**\r\n   * call to create a grid with the given options, including loading any children from JSON structure. This will call GridStack.init(), then\r\n   * grid.load() on any passed children (recursively). Great alternative to calling init() if you want entire grid to come from\r\n   * JSON serialized data, including options.\r\n   * @param parent HTML element parent to the grid\r\n   * @param opt grids options used to initialize the grid, and list of children\r\n   */\r\n  public static addGrid(parent: HTMLElement, opt: GridStackOptions = {}): GridStack {\r\n    if (!parent) return null;\r\n\r\n    let el = parent as GridHTMLElement;\r\n    if (el.gridstack) {\r\n      // already a grid - set option and load data\r\n      const grid = el.gridstack;\r\n      if (opt) grid.opts = { ...grid.opts, ...opt };\r\n      if (opt.children !== undefined) grid.load(opt.children);\r\n      return grid;\r\n    }\r\n\r\n    // create the grid element, but check if the passed 'parent' already has grid styling and should be used instead\r\n    const parentIsGrid = parent.classList.contains('grid-stack');\r\n    if (!parentIsGrid || GridStack.addRemoveCB) {\r\n      if (GridStack.addRemoveCB) {\r\n        el = GridStack.addRemoveCB(parent, opt, true, true);\r\n      } else {\r\n        el = Utils.createDiv(['grid-stack', opt.class], parent);\r\n      }\r\n    }\r\n\r\n    // create grid class and load any children\r\n    const grid = GridStack.init(opt, el);\r\n    return grid;\r\n  }\r\n\r\n  /** call this method to register your engine instead of the default one.\r\n   * See instead `GridStackOptions.engineClass` if you only need to\r\n   * replace just one instance.\r\n   */\r\n  static registerEngine(engineClass: typeof GridStackEngine): void {\r\n    GridStack.engineClass = engineClass;\r\n  }\r\n\r\n  /**\r\n   * callback method use when new items|grids needs to be created or deleted, instead of the default\r\n   * item: <div class=\"grid-stack-item\"><div class=\"grid-stack-item-content\">w.content</div></div>\r\n   * grid: <div class=\"grid-stack\">grid content...</div>\r\n   * add = true: the returned DOM element will then be converted to a GridItemHTMLElement using makeWidget()|GridStack:init().\r\n   * add = false: the item will be removed from DOM (if not already done)\r\n   * grid = true|false for grid vs grid-items\r\n   */\r\n  public static addRemoveCB?: AddRemoveFcn;\r\n\r\n  /**\r\n   * callback during saving to application can inject extra data for each widget, on top of the grid layout properties\r\n   */\r\n  public static saveCB?: SaveFcn;\r\n\r\n  /**\r\n   * callback to create the content of widgets so the app can control how to store and restore it\r\n   * By default this lib will do 'el.textContent = w.content' forcing text only support for avoiding potential XSS issues.\r\n   */\r\n  public static renderCB?: RenderFcn = (el: HTMLElement, w: GridStackNode) => { if (el && w?.content) el.textContent = w.content; };\r\n\r\n  /** called after a widget has been updated (eg: load() into an existing list of children) so application can do extra work */\r\n  public static updateCB?: (w: GridStackNode) => void;\r\n\r\n  /** callback to use for resizeToContent instead of the built in one */\r\n  public static resizeToContentCB?: ResizeToContentFcn;\r\n  /** parent class for sizing content. defaults to '.grid-stack-item-content' */\r\n  public static resizeToContentParent = '.grid-stack-item-content';\r\n\r\n  /** scoping so users can call GridStack.Utils.sort() for example */\r\n  public static Utils = Utils;\r\n\r\n  /** scoping so users can call new GridStack.Engine(12) for example */\r\n  public static Engine = GridStackEngine;\r\n\r\n  /** engine used to implement non DOM grid functionality */\r\n  public engine: GridStackEngine;\r\n\r\n  /** point to a parent grid item if we're nested (inside a grid-item in between 2 Grids) */\r\n  public parentGridNode?: GridStackNode;\r\n\r\n  /** time to wait for animation (if enabled) to be done so content sizing can happen */\r\n  public animationDelay = 300 + 10;\r\n\r\n  protected static engineClass: typeof GridStackEngine;\r\n  protected resizeObserver: ResizeObserver;\r\n\r\n  /** @internal true if we got created by drag over gesture, so we can removed on drag out (temporary) */\r\n  public _isTemp?: boolean;\r\n\r\n  /** @internal create placeholder DIV as needed */\r\n  public get placeholder(): GridItemHTMLElement {\r\n    if (!this._placeholder) {\r\n      this._placeholder = Utils.createDiv([this.opts.placeholderClass, gridDefaults.itemClass, this.opts.itemClass]);\r\n      const placeholderChild = Utils.createDiv(['placeholder-content'], this._placeholder);\r\n      if (this.opts.placeholderText) {\r\n        placeholderChild.textContent = this.opts.placeholderText;\r\n      }\r\n    }\r\n    return this._placeholder;\r\n  }\r\n  /** @internal */\r\n  protected _placeholder: GridItemHTMLElement;\r\n  /** @internal prevent cached layouts from being updated when loading into small column layouts */\r\n  protected _ignoreLayoutsNodeChange: boolean;\r\n  /** @internal */\r\n  public _gsEventHandler = {};\r\n  /** @internal flag to keep cells square during resize */\r\n  protected _isAutoCellHeight: boolean;\r\n  /** @internal limit auto cell resizing method */\r\n  protected _sizeThrottle: () => void;\r\n  /** @internal limit auto cell resizing method */\r\n  protected prevWidth: number;\r\n  /** @internal extra row added when dragging at the bottom of the grid */\r\n  protected _extraDragRow = 0;\r\n  /** @internal true if nested grid should get column count from our width */\r\n  protected _autoColumn?: boolean;\r\n  /** @internal meant to store the scale of the active grid */\r\n  protected dragTransform: DragTransform = { xScale: 1, yScale: 1, xOffset: 0, yOffset: 0 };\r\n  protected responseLayout: ColumnOptions;\r\n  private _skipInitialResize: boolean;\r\n\r\n  /**\r\n   * Construct a grid item from the given element and options\r\n   * @param el the HTML element tied to this grid after it's been initialized\r\n   * @param opts grid options - public for classes to access, but use methods to modify!\r\n   */\r\n  public constructor(public el: GridHTMLElement, public opts: GridStackOptions = {}) {\r\n    el.gridstack = this;\r\n    this.opts = opts = opts || {}; // handles null/undefined/0\r\n\r\n    if (!el.classList.contains('grid-stack')) {\r\n      this.el.classList.add('grid-stack');\r\n    }\r\n\r\n    // if row property exists, replace minRow and maxRow instead\r\n    if (opts.row) {\r\n      opts.minRow = opts.maxRow = opts.row;\r\n      delete opts.row;\r\n    }\r\n    const rowAttr = Utils.toNumber(el.getAttribute('gs-row'));\r\n\r\n    // flag only valid in sub-grids (handled by parent, not here)\r\n    if (opts.column === 'auto') {\r\n      delete opts.column;\r\n    }\r\n    // save original setting so we can restore on save\r\n    if (opts.alwaysShowResizeHandle !== undefined) {\r\n      (opts as InternalGridStackOptions)._alwaysShowResizeHandle = opts.alwaysShowResizeHandle;\r\n    }\r\n\r\n    // cleanup responsive opts (must have columnWidth | breakpoints) then sort breakpoints by size (so we can match during resize)\r\n    const resp = opts.columnOpts;\r\n    if (resp) {\r\n      const bk = resp.breakpoints;\r\n      if (!resp.columnWidth && !bk?.length) {\r\n        delete opts.columnOpts;\r\n      } else {\r\n        resp.columnMax = resp.columnMax || 12;\r\n        if (bk?.length > 1) bk.sort((a, b) => (b.w || 0) - (a.w || 0));\r\n      }\r\n    }\r\n\r\n    // elements DOM attributes override any passed options (like CSS style) - merge the two together\r\n    const defaults: GridStackOptions = {\r\n      ...Utils.cloneDeep(gridDefaults),\r\n      column: Utils.toNumber(el.getAttribute('gs-column')) || gridDefaults.column,\r\n      minRow: rowAttr ? rowAttr : Utils.toNumber(el.getAttribute('gs-min-row')) || gridDefaults.minRow,\r\n      maxRow: rowAttr ? rowAttr : Utils.toNumber(el.getAttribute('gs-max-row')) || gridDefaults.maxRow,\r\n      staticGrid: Utils.toBool(el.getAttribute('gs-static')) || gridDefaults.staticGrid,\r\n      sizeToContent: Utils.toBool(el.getAttribute('gs-size-to-content')) || undefined,\r\n      draggable: {\r\n        handle: (opts.handleClass ? '.' + opts.handleClass : (opts.handle ? opts.handle : '')) || gridDefaults.draggable.handle,\r\n      },\r\n      removableOptions: {\r\n        accept: opts.itemClass || gridDefaults.removableOptions.accept,\r\n        decline: gridDefaults.removableOptions.decline\r\n      },\r\n    };\r\n    if (el.getAttribute('gs-animate')) { // default to true, but if set to false use that instead\r\n      defaults.animate = Utils.toBool(el.getAttribute('gs-animate'))\r\n    }\r\n\r\n    opts = Utils.defaults(opts, defaults);\r\n    this._initMargin(); // part of settings defaults...\r\n\r\n    // Now check if we're loading into !12 column mode FIRST so we don't do un-necessary work (like cellHeight = width / 12 then go 1 column)\r\n    this.checkDynamicColumn();\r\n    this._updateColumnVar(opts);\r\n\r\n    if (opts.rtl === 'auto') {\r\n      opts.rtl = (el.style.direction === 'rtl');\r\n    }\r\n    if (opts.rtl) {\r\n      this.el.classList.add('grid-stack-rtl');\r\n    }\r\n\r\n    // check if we're been nested, and if so update our style and keep pointer around (used during save)\r\n    const parentGridItem: GridItemHTMLElement = this.el.closest('.' + gridDefaults.itemClass);\r\n    const parentNode = parentGridItem?.gridstackNode;\r\n    if (parentNode) {\r\n      parentNode.subGrid = this;\r\n      this.parentGridNode = parentNode;\r\n      this.el.classList.add('grid-stack-nested');\r\n      parentNode.el.classList.add('grid-stack-sub-grid');\r\n    }\r\n\r\n    this._isAutoCellHeight = (opts.cellHeight === 'auto');\r\n    if (this._isAutoCellHeight || opts.cellHeight === 'initial') {\r\n      // make the cell content square initially (will use resize/column event to keep it square)\r\n      this.cellHeight(undefined);\r\n    } else {\r\n      // append unit if any are set\r\n      if (typeof opts.cellHeight == 'number' && opts.cellHeightUnit && opts.cellHeightUnit !== gridDefaults.cellHeightUnit) {\r\n        opts.cellHeight = opts.cellHeight + opts.cellHeightUnit;\r\n        delete opts.cellHeightUnit;\r\n      }\r\n      const val = opts.cellHeight;\r\n      delete opts.cellHeight; // force initial cellHeight() call to set the value\r\n      this.cellHeight(val);\r\n    }\r\n\r\n    // see if we need to adjust auto-hide\r\n    if (opts.alwaysShowResizeHandle === 'mobile') {\r\n      opts.alwaysShowResizeHandle = isTouch;\r\n    }\r\n\r\n    this._setStaticClass();\r\n\r\n    const engineClass = opts.engineClass || GridStack.engineClass || GridStackEngine;\r\n    this.engine = new engineClass({\r\n      column: this.getColumn(),\r\n      float: opts.float,\r\n      maxRow: opts.maxRow,\r\n      onChange: (cbNodes) => {\r\n        cbNodes.forEach(n => {\r\n          const el = n.el;\r\n          if (!el) return;\r\n          if (n._removeDOM) {\r\n            if (el) el.remove();\r\n            delete n._removeDOM;\r\n          } else {\r\n            this._writePosAttr(el, n);\r\n          }\r\n        });\r\n        this._updateContainerHeight();\r\n      }\r\n    });\r\n\r\n    if (opts.auto) {\r\n      this.batchUpdate(); // prevent in between re-layout #1535 TODO: this only set float=true, need to prevent collision check...\r\n      this.engine._loading = true; // loading collision check\r\n      this.getGridItems().forEach(el => this._prepareElement(el));\r\n      delete this.engine._loading;\r\n      this.batchUpdate(false);\r\n    }\r\n\r\n    // load any passed in children as well, which overrides any DOM layout done above\r\n    if (opts.children) {\r\n      const children = opts.children;\r\n      delete opts.children;\r\n      if (children.length) this.load(children); // don't load empty\r\n    }\r\n\r\n    this.setAnimation();\r\n\r\n    // dynamic grids require pausing during drag to detect over to nest vs push\r\n    if (opts.subGridDynamic && !DDManager.pauseDrag) DDManager.pauseDrag = true;\r\n    if (opts.draggable?.pause !== undefined) DDManager.pauseDrag = opts.draggable.pause;\r\n\r\n    this._setupRemoveDrop();\r\n    this._setupAcceptWidget();\r\n    this._updateResizeEvent();\r\n  }\r\n\r\n  private _updateColumnVar(opts: GridStackOptions = this.opts): void {\r\n    this.el.classList.add('gs-' + opts.column);\r\n    if (typeof opts.column === 'number') this.el.style.setProperty('--gs-column-width', `${100/opts.column}%`);\r\n  }\r\n\r\n  /**\r\n   * add a new widget and returns it.\r\n   *\r\n   * Widget will be always placed even if result height is more than actual grid height.\r\n   * You need to use `willItFit()` before calling addWidget for additional check.\r\n   * See also `makeWidget(el)` for DOM element.\r\n   *\r\n   * @example\r\n   * const grid = GridStack.init();\r\n   * grid.addWidget({w: 3, content: 'hello'});\r\n   *\r\n   * @param w GridStackWidget definition. used MakeWidget(el) if you have dom element instead.\r\n   */\r\n  public addWidget(w: GridStackWidget): GridItemHTMLElement {\r\n    if (typeof w === 'string') { console.error('V11: GridStack.addWidget() does not support string anymore. see #2736'); return; }\r\n    if ((w as HTMLElement).ELEMENT_NODE) { console.error('V11: GridStack.addWidget() does not support HTMLElement anymore. use makeWidget()'); return this.makeWidget(w as HTMLElement); }\r\n\r\n    let el: GridItemHTMLElement;\r\n    let node: GridStackNode = w;\r\n    node.grid = this;\r\n    if (node?.el) {\r\n      el = node.el; // re-use element stored in the node\r\n    } else if (GridStack.addRemoveCB) {\r\n      el = GridStack.addRemoveCB(this.el, w, true, false);\r\n    } else {\r\n      el = this.createWidgetDivs(node);\r\n    }\r\n\r\n    if (!el) return;\r\n\r\n    // if the caller ended up initializing the widget in addRemoveCB, or we stared with one already, skip the rest\r\n    node = el.gridstackNode;\r\n    if (node && el.parentElement === this.el && this.engine.nodes.find(n => n._id === node._id)) return el;\r\n\r\n    // Tempting to initialize the passed in opt with default and valid values, but this break knockout demos\r\n    // as the actual value are filled in when _prepareElement() calls el.getAttribute('gs-xyz') before adding the node.\r\n    // So make sure we load any DOM attributes that are not specified in passed in options (which override)\r\n    const domAttr = this._readAttr(el);\r\n    Utils.defaults(w, domAttr);\r\n    this.engine.prepareNode(w);\r\n    // this._writeAttr(el, w); why write possibly incorrect values back when makeWidget() will ?\r\n\r\n    this.el.appendChild(el);\r\n\r\n    this.makeWidget(el, w);\r\n\r\n    return el;\r\n  }\r\n\r\n  /** create the default grid item divs, and content (possibly lazy loaded) by using GridStack.renderCB() */\r\n  public createWidgetDivs(n: GridStackNode): HTMLElement {\r\n    const el = Utils.createDiv(['grid-stack-item', this.opts.itemClass]);\r\n    const cont = Utils.createDiv(['grid-stack-item-content'], el);\r\n\r\n    if (Utils.lazyLoad(n)) {\r\n      if (!n.visibleObservable) {\r\n        n.visibleObservable = new IntersectionObserver(([entry]) => { if (entry.isIntersecting) {\r\n          n.visibleObservable?.disconnect();\r\n          delete n.visibleObservable;\r\n          GridStack.renderCB(cont, n);\r\n          n.grid?.prepareDragDrop(n.el);\r\n        }});\r\n        window.setTimeout(() => n.visibleObservable?.observe(el)); // wait until callee sets position attributes\r\n      }\r\n    } else GridStack.renderCB(cont, n);\r\n\r\n    return el;\r\n  }\r\n\r\n  /**\r\n   * Convert an existing gridItem element into a sub-grid with the given (optional) options, else inherit them\r\n   * from the parent's subGrid options.\r\n   * @param el gridItem element to convert\r\n   * @param ops (optional) sub-grid options, else default to node, then parent settings, else defaults\r\n   * @param nodeToAdd (optional) node to add to the newly created sub grid (used when dragging over existing regular item)\r\n   * @param saveContent if true (default) the html inside .grid-stack-content will be saved to child widget\r\n   * @returns newly created grid\r\n   */\r\n  public makeSubGrid(el: GridItemHTMLElement, ops?: GridStackOptions, nodeToAdd?: GridStackNode, saveContent = true): GridStack {\r\n    let node = el.gridstackNode;\r\n    if (!node) {\r\n      node = this.makeWidget(el).gridstackNode;\r\n    }\r\n    if (node.subGrid?.el) return node.subGrid; // already done\r\n\r\n    // find the template subGrid stored on a parent as fallback...\r\n    let subGridTemplate: GridStackOptions; // eslint-disable-next-line @typescript-eslint/no-this-alias\r\n    let grid: GridStack = this;\r\n    while (grid && !subGridTemplate) {\r\n      subGridTemplate = grid.opts?.subGridOpts;\r\n      grid = grid.parentGridNode?.grid;\r\n    }\r\n    //... and set the create options\r\n    ops = Utils.cloneDeep({\r\n      // by default sub-grid inherit from us | parent, other than id, children, etc...\r\n      ...this.opts, id: undefined, children: undefined, column: 'auto', columnOpts: undefined, layout: 'list', subGridOpts: undefined,\r\n      ...(subGridTemplate || {}),\r\n      ...(ops || node.subGridOpts || {})\r\n    });\r\n    node.subGridOpts = ops;\r\n\r\n    // if column special case it set, remember that flag and set default\r\n    let autoColumn: boolean;\r\n    if (ops.column === 'auto') {\r\n      autoColumn = true;\r\n      ops.column = Math.max(node.w || 1, nodeToAdd?.w || 1);\r\n      delete ops.columnOpts; // driven by parent\r\n    }\r\n\r\n    // if we're converting an existing full item, move over the content to be the first sub item in the new grid\r\n    let content = node.el.querySelector('.grid-stack-item-content') as HTMLElement;\r\n    let newItem: HTMLElement;\r\n    let newItemOpt: GridStackNode;\r\n    if (saveContent) {\r\n      this._removeDD(node.el); // remove D&D since it's set on content div\r\n      newItemOpt = { ...node, x: 0, y: 0 };\r\n      Utils.removeInternalForSave(newItemOpt);\r\n      delete newItemOpt.subGridOpts;\r\n      if (node.content) {\r\n        newItemOpt.content = node.content;\r\n        delete node.content;\r\n      }\r\n      if (GridStack.addRemoveCB) {\r\n        newItem = GridStack.addRemoveCB(this.el, newItemOpt, true, false);\r\n      } else {\r\n        newItem = Utils.createDiv(['grid-stack-item']);\r\n        newItem.appendChild(content);\r\n        content = Utils.createDiv(['grid-stack-item-content'], node.el);\r\n      }\r\n      this.prepareDragDrop(node.el); // ... and restore original D&D\r\n    }\r\n\r\n    // if we're adding an additional item, make the container large enough to have them both\r\n    if (nodeToAdd) {\r\n      const w = autoColumn ? ops.column : node.w;\r\n      const h = node.h + nodeToAdd.h;\r\n      const style = node.el.style;\r\n      style.transition = 'none'; // show up instantly so we don't see scrollbar with nodeToAdd\r\n      this.update(node.el, { w, h });\r\n      setTimeout(() => style.transition = null); // recover animation\r\n    }\r\n\r\n    const subGrid = node.subGrid = GridStack.addGrid(content, ops);\r\n    if (nodeToAdd?._moving) subGrid._isTemp = true; // prevent re-nesting as we add over\r\n    if (autoColumn) subGrid._autoColumn = true;\r\n\r\n    // add the original content back as a child of hte newly created grid\r\n    if (saveContent) {\r\n      subGrid.makeWidget(newItem, newItemOpt);\r\n    }\r\n\r\n    // now add any additional node\r\n    if (nodeToAdd) {\r\n      if (nodeToAdd._moving) {\r\n        // create an artificial event even for the just created grid to receive this item\r\n        window.setTimeout(() => Utils.simulateMouseEvent(nodeToAdd._event, 'mouseenter', subGrid.el), 0);\r\n      } else {\r\n        subGrid.makeWidget(node.el, node);\r\n      }\r\n    }\r\n\r\n    // if sizedToContent, we need to re-calc the size of ourself\r\n    this.resizeToContentCheck(false, node);\r\n\r\n    return subGrid;\r\n  }\r\n\r\n  /**\r\n   * called when an item was converted into a nested grid to accommodate a dragged over item, but then item leaves - return back\r\n   * to the original grid-item. Also called to remove empty sub-grids when last item is dragged out (since re-creating is simple)\r\n   */\r\n  public removeAsSubGrid(nodeThatRemoved?: GridStackNode): void {\r\n    const pGrid = this.parentGridNode?.grid;\r\n    if (!pGrid) return;\r\n\r\n    pGrid.batchUpdate();\r\n    pGrid.removeWidget(this.parentGridNode.el, true, true);\r\n    this.engine.nodes.forEach(n => {\r\n      // migrate any children over and offsetting by our location\r\n      n.x += this.parentGridNode.x;\r\n      n.y += this.parentGridNode.y;\r\n      pGrid.makeWidget(n.el, n);\r\n    });\r\n    pGrid.batchUpdate(false);\r\n    if (this.parentGridNode) delete this.parentGridNode.subGrid;\r\n    delete this.parentGridNode;\r\n\r\n    // create an artificial event for the original grid now that this one is gone (got a leave, but won't get enter)\r\n    if (nodeThatRemoved) {\r\n      window.setTimeout(() => Utils.simulateMouseEvent(nodeThatRemoved._event, 'mouseenter', pGrid.el), 0);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * saves the current layout returning a list of widgets for serialization which might include any nested grids.\r\n   * @param saveContent if true (default) the latest html inside .grid-stack-content will be saved to GridStackWidget.content field, else it will\r\n   * be removed.\r\n   * @param saveGridOpt if true (default false), save the grid options itself, so you can call the new GridStack.addGrid()\r\n   * to recreate everything from scratch. GridStackOptions.children would then contain the widget list instead.\r\n   * @param saveCB callback for each node -> widget, so application can insert additional data to be saved into the widget data structure.\r\n   * @returns list of widgets or full grid option, including .children list of widgets\r\n   */\r\n  public save(saveContent = true, saveGridOpt = false, saveCB = GridStack.saveCB): GridStackWidget[] | GridStackOptions {\r\n    // return copied GridStackWidget (with optionally .el) we can modify at will...\r\n    const list = this.engine.save(saveContent, saveCB);\r\n\r\n    // check for HTML content and nested grids\r\n    list.forEach(n => {\r\n      if (saveContent && n.el && !n.subGrid && !saveCB) { // sub-grid are saved differently, not plain content\r\n        const itemContent = n.el.querySelector('.grid-stack-item-content');\r\n        n.content = itemContent?.innerHTML;\r\n        if (!n.content) delete n.content;\r\n      } else {\r\n        if (!saveContent && !saveCB) { delete n.content; }\r\n        // check for nested grid\r\n        if (n.subGrid?.el) {\r\n          const listOrOpt = n.subGrid.save(saveContent, saveGridOpt, saveCB);\r\n          n.subGridOpts = (saveGridOpt ? listOrOpt : { children: listOrOpt }) as GridStackOptions;\r\n          delete n.subGrid;\r\n        }\r\n      }\r\n      delete n.el;\r\n    });\r\n\r\n    // check if save entire grid options (needed for recursive) + children...\r\n    if (saveGridOpt) {\r\n      const o: InternalGridStackOptions = Utils.cloneDeep(this.opts);\r\n      // delete default values that will be recreated on launch\r\n      if (o.marginBottom === o.marginTop && o.marginRight === o.marginLeft && o.marginTop === o.marginRight) {\r\n        o.margin = o.marginTop;\r\n        delete o.marginTop; delete o.marginRight; delete o.marginBottom; delete o.marginLeft;\r\n      }\r\n      if (o.rtl === (this.el.style.direction === 'rtl')) { o.rtl = 'auto' }\r\n      if (this._isAutoCellHeight) {\r\n        o.cellHeight = 'auto'\r\n      }\r\n      if (this._autoColumn) {\r\n        o.column = 'auto';\r\n      }\r\n      const origShow = o._alwaysShowResizeHandle;\r\n      delete o._alwaysShowResizeHandle;\r\n      if (origShow !== undefined) {\r\n        o.alwaysShowResizeHandle = origShow;\r\n      } else {\r\n        delete o.alwaysShowResizeHandle;\r\n      }\r\n      Utils.removeInternalAndSame(o, gridDefaults);\r\n      o.children = list;\r\n      return o;\r\n    }\r\n\r\n    return list;\r\n  }\r\n\r\n  /**\r\n   * load the widgets from a list. This will call update() on each (matching by id) or add/remove widgets that are not there.\r\n   *\r\n   * @param items list of widgets definition to update/create\r\n   * @param addRemove boolean (default true) or callback method can be passed to control if and how missing widgets can be added/removed, giving\r\n   * the user control of insertion.\r\n   *\r\n   * @example\r\n   * see http://gridstackjs.com/demo/serialization.html\r\n   */\r\n  public load(items: GridStackWidget[], addRemove: boolean | AddRemoveFcn = GridStack.addRemoveCB || true): GridStack {\r\n    items = Utils.cloneDeep(items); // so we can mod\r\n    const column = this.getColumn();\r\n\r\n    // make sure size 1x1 (default) is present as it may need to override current sizes\r\n    items.forEach(n => { n.w = n.w || 1; n.h = n.h || 1 });\r\n\r\n    // sort items. those without coord will be appended last\r\n    items = Utils.sort(items);\r\n\r\n    this.engine.skipCacheUpdate = this._ignoreLayoutsNodeChange = true; // skip layout update\r\n\r\n    // if we're loading a layout into for example 1 column and items don't fit, make sure to save\r\n    // the original wanted layout so we can scale back up correctly #1471\r\n    let maxColumn = 0;\r\n    items.forEach(n => { maxColumn = Math.max(maxColumn, (n.x || 0) + n.w) });\r\n    if (maxColumn > this.engine.defaultColumn) this.engine.defaultColumn = maxColumn;\r\n    if (maxColumn > column) {\r\n      // if we're loading (from empty) into a smaller column, check for special responsive layout\r\n      if (this.engine.nodes.length === 0 && this.responseLayout) {\r\n        this.engine.nodes = items;\r\n        this.engine.columnChanged(maxColumn, column, this.responseLayout);\r\n        items = this.engine.nodes;\r\n        this.engine.nodes = [];\r\n        delete this.responseLayout;\r\n      } else this.engine.cacheLayout(items, maxColumn, true);\r\n    }\r\n\r\n    // if given a different callback, temporally set it as global option so creating will use it\r\n    const prevCB = GridStack.addRemoveCB;\r\n    if (typeof (addRemove) === 'function') GridStack.addRemoveCB = addRemove as AddRemoveFcn;\r\n\r\n    const removed: GridStackNode[] = [];\r\n    this.batchUpdate();\r\n\r\n    // if we are loading from empty temporarily remove animation\r\n    const blank = !this.engine.nodes.length;\r\n    const noAnim = blank && this.opts.animate;\r\n    if (noAnim) this.setAnimation(false);\r\n\r\n    // see if any items are missing from new layout and need to be removed first\r\n    if (!blank && addRemove) {\r\n      const copyNodes = [...this.engine.nodes]; // don't loop through array you modify\r\n      copyNodes.forEach(n => {\r\n        if (!n.id) return;\r\n        const item = Utils.find(items, n.id);\r\n        if (!item) {\r\n          if (GridStack.addRemoveCB) GridStack.addRemoveCB(this.el, n, false, false);\r\n          removed.push(n); // batch keep track\r\n          this.removeWidget(n.el, true, false);\r\n        }\r\n      });\r\n    }\r\n\r\n    // now add/update the widgets - starting with removing items in the new layout we will reposition\r\n    // to reduce collision and add no-coord ones at next available spot\r\n    this.engine._loading = true; // help with collision\r\n    const updateNodes: GridStackWidget[] = [];\r\n    this.engine.nodes = this.engine.nodes.filter(n => {\r\n      if (Utils.find(items, n.id)) { updateNodes.push(n); return false; } // remove if found from list\r\n      return true;\r\n    });\r\n    items.forEach(w => {\r\n      const item = Utils.find(updateNodes, w.id);\r\n      if (item) {\r\n        // if item sizes to content, re-use the exiting height so it's a better guess at the final size (same if width doesn't change)\r\n        if (Utils.shouldSizeToContent(item)) w.h = item.h;\r\n        // check if missing coord, in which case find next empty slot with new (or old if missing) sizes\r\n        this.engine.nodeBoundFix(w);\r\n        if (w.autoPosition || w.x === undefined || w.y === undefined) {\r\n          w.w = w.w || item.w;\r\n          w.h = w.h || item.h;\r\n          this.engine.findEmptyPosition(w);\r\n        }\r\n\r\n        // add back to current list BUT force a collision check if it 'appears' we didn't change to make sure we don't overlap others now\r\n        this.engine.nodes.push(item);\r\n        if (Utils.samePos(item, w) && this.engine.nodes.length > 1) {\r\n          this.moveNode(item, { ...w, forceCollide: true });\r\n          Utils.copyPos(w, item); // use possily updated values before update() is called next (no-op since already moved)\r\n        }\r\n\r\n        this.update(item.el, w);\r\n\r\n        if (w.subGridOpts?.children) { // update any sub grid as well\r\n          const sub = item.el.querySelector('.grid-stack') as GridHTMLElement;\r\n          if (sub && sub.gridstack) {\r\n            sub.gridstack.load(w.subGridOpts.children); // TODO: support updating grid options ?\r\n          }\r\n        }\r\n      } else if (addRemove) {\r\n        this.addWidget(w);\r\n      }\r\n    });\r\n\r\n    delete this.engine._loading; // done loading\r\n    this.engine.removedNodes = removed;\r\n    this.batchUpdate(false);\r\n\r\n    // after commit, clear that flag\r\n    delete this._ignoreLayoutsNodeChange;\r\n    delete this.engine.skipCacheUpdate;\r\n    prevCB ? GridStack.addRemoveCB = prevCB : delete GridStack.addRemoveCB;\r\n    if (noAnim) this.setAnimation(true, true); // delay adding animation back\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * use before calling a bunch of `addWidget()` to prevent un-necessary relayouts in between (more efficient)\r\n   * and get a single event callback. You will see no changes until `batchUpdate(false)` is called.\r\n   */\r\n  public batchUpdate(flag = true): GridStack {\r\n    this.engine.batchUpdate(flag);\r\n    if (!flag) {\r\n      this._updateContainerHeight();\r\n      this._triggerRemoveEvent();\r\n      this._triggerAddEvent();\r\n      this._triggerChangeEvent();\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Gets current cell height.\r\n   */\r\n  public getCellHeight(forcePixel = false): number {\r\n    if (this.opts.cellHeight && this.opts.cellHeight !== 'auto' &&\r\n      (!forcePixel || !this.opts.cellHeightUnit || this.opts.cellHeightUnit === 'px')) {\r\n      return this.opts.cellHeight as number;\r\n    }\r\n    // do rem/em/cm/mm to px conversion\r\n    if (this.opts.cellHeightUnit === 'rem') {\r\n      return (this.opts.cellHeight as number) * parseFloat(getComputedStyle(document.documentElement).fontSize);\r\n    }\r\n    if (this.opts.cellHeightUnit === 'em') {\r\n      return (this.opts.cellHeight as number) * parseFloat(getComputedStyle(this.el).fontSize);\r\n    }\r\n    if (this.opts.cellHeightUnit === 'cm') {\r\n      // 1cm = 96px/2.54. See https://www.w3.org/TR/css-values-3/#absolute-lengths\r\n      return (this.opts.cellHeight as number) * (96 / 2.54);\r\n    }\r\n    if (this.opts.cellHeightUnit === 'mm') {\r\n      return (this.opts.cellHeight as number) * (96 / 2.54) / 10;\r\n    }\r\n    // else get first cell height\r\n    const el = this.el.querySelector('.' + this.opts.itemClass) as HTMLElement;\r\n    if (el) {\r\n      const h = Utils.toNumber(el.getAttribute('gs-h')) || 1; // since we don't write 1 anymore\r\n      return Math.round(el.offsetHeight / h);\r\n    }\r\n    // else do entire grid and # of rows (but doesn't work if min-height is the actual constrain)\r\n    const rows = parseInt(this.el.getAttribute('gs-current-row'));\r\n    return rows ? Math.round(this.el.getBoundingClientRect().height / rows) : this.opts.cellHeight as number;\r\n  }\r\n\r\n  /**\r\n   * Update current cell height - see `GridStackOptions.cellHeight` for format.\r\n   * This method rebuilds an internal CSS style sheet.\r\n   * Note: You can expect performance issues if call this method too often.\r\n   *\r\n   * @param val the cell height. If not passed (undefined), cells content will be made square (match width minus margin),\r\n   * if pass 0 the CSS will be generated by the application instead.\r\n   *\r\n   * @example\r\n   * grid.cellHeight(100); // same as 100px\r\n   * grid.cellHeight('70px');\r\n   * grid.cellHeight(grid.cellWidth() * 1.2);\r\n   */\r\n  public cellHeight(val?: numberOrString): GridStack {\r\n\r\n    // if not called internally, check if we're changing mode\r\n    if (val !== undefined) {\r\n      if (this._isAutoCellHeight !== (val === 'auto')) {\r\n        this._isAutoCellHeight = (val === 'auto');\r\n        this._updateResizeEvent();\r\n      }\r\n    }\r\n    if (val === 'initial' || val === 'auto') { val = undefined; }\r\n\r\n    // make item content be square\r\n    if (val === undefined) {\r\n      const marginDiff = - (this.opts.marginRight as number) - (this.opts.marginLeft as number)\r\n        + (this.opts.marginTop as number) + (this.opts.marginBottom as number);\r\n      val = this.cellWidth() + marginDiff;\r\n    }\r\n\r\n    const data = Utils.parseHeight(val);\r\n    if (this.opts.cellHeightUnit === data.unit && this.opts.cellHeight === data.h) {\r\n      return this;\r\n    }\r\n    this.opts.cellHeightUnit = data.unit;\r\n    this.opts.cellHeight = data.h;\r\n\r\n    // finally update var and container\r\n    this.el.style.setProperty('--gs-cell-height', `${this.opts.cellHeight}${this.opts.cellHeightUnit}`);\r\n    this._updateContainerHeight();\r\n    this.resizeToContentCheck();\r\n\r\n    return this;\r\n  }\r\n\r\n  /** Gets current cell width. */\r\n  public cellWidth(): number {\r\n    return this._widthOrContainer() / this.getColumn();\r\n  }\r\n  /** return our expected width (or parent) , and optionally of window for dynamic column check */\r\n  protected _widthOrContainer(forBreakpoint = false): number {\r\n    // use `offsetWidth` or `clientWidth` (no scrollbar) ?\r\n    // https://stackoverflow.com/questions/21064101/understanding-offsetwidth-clientwidth-scrollwidth-and-height-respectively\r\n    return forBreakpoint && this.opts.columnOpts?.breakpointForWindow ? window.innerWidth : (this.el.clientWidth || this.el.parentElement.clientWidth || window.innerWidth);\r\n  }\r\n  /** checks for dynamic column count for our current size, returning true if changed */\r\n  protected checkDynamicColumn(): boolean {\r\n    const resp = this.opts.columnOpts;\r\n    if (!resp || (!resp.columnWidth && !resp.breakpoints?.length)) return false;\r\n    const column = this.getColumn();\r\n    let newColumn = column;\r\n    const w = this._widthOrContainer(true);\r\n    if (resp.columnWidth) {\r\n      newColumn = Math.min(Math.round(w / resp.columnWidth) || 1, resp.columnMax);\r\n    } else {\r\n      // find the closest breakpoint (already sorted big to small) that matches\r\n      newColumn = resp.columnMax;\r\n      let i = 0;\r\n      while (i < resp.breakpoints.length && w <= resp.breakpoints[i].w) {\r\n        newColumn = resp.breakpoints[i++].c || column;\r\n      }\r\n    }\r\n    if (newColumn !== column) {\r\n      const bk = resp.breakpoints?.find(b => b.c === newColumn);\r\n      this.column(newColumn, bk?.layout || resp.layout);\r\n      return true;\r\n    }\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * re-layout grid items to reclaim any empty space. Options are:\r\n   * 'list' keep the widget left->right order the same, even if that means leaving an empty slot if things don't fit\r\n   * 'compact' might re-order items to fill any empty space\r\n   *\r\n   * doSort - 'false' to let you do your own sorting ahead in case you need to control a different order. (default to sort)\r\n   */\r\n  public compact(layout: CompactOptions = 'compact', doSort = true): GridStack {\r\n    this.engine.compact(layout, doSort);\r\n    this._triggerChangeEvent();\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * set the number of columns in the grid. Will update existing widgets to conform to new number of columns,\r\n   * as well as cache the original layout so you can revert back to previous positions without loss.\r\n   * @param column - Integer > 0 (default 12).\r\n   * @param layout specify the type of re-layout that will happen (position, size, etc...).\r\n   * Note: items will never be outside of the current column boundaries. default ('moveScale'). Ignored for 1 column\r\n   */\r\n  public column(column: number, layout: ColumnOptions = 'moveScale'): GridStack {\r\n    if (!column || column < 1 || this.opts.column === column) return this;\r\n\r\n    const oldColumn = this.getColumn();\r\n    this.opts.column = column;\r\n    if (!this.engine) {\r\n      // called in constructor, noting else to do but remember that breakpoint layout\r\n      this.responseLayout = layout;\r\n      return this;\r\n    }\r\n\r\n    this.engine.column = column;\r\n    this.el.classList.remove('gs-' + oldColumn);\r\n    this._updateColumnVar();\r\n\r\n    // update the items now\r\n    this.engine.columnChanged(oldColumn, column, layout);\r\n    if (this._isAutoCellHeight) this.cellHeight();\r\n\r\n    this.resizeToContentCheck(true); // wait for width resizing\r\n\r\n    // and trigger our event last...\r\n    this._ignoreLayoutsNodeChange = true; // skip layout update\r\n    this._triggerChangeEvent();\r\n    delete this._ignoreLayoutsNodeChange;\r\n\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * get the number of columns in the grid (default 12)\r\n   */\r\n  public getColumn(): number { return this.opts.column as number; }\r\n\r\n  /** returns an array of grid HTML elements (no placeholder) - used to iterate through our children in DOM order */\r\n  public getGridItems(): GridItemHTMLElement[] {\r\n    return Array.from(this.el.children)\r\n      .filter((el: HTMLElement) => el.matches('.' + this.opts.itemClass) && !el.matches('.' + this.opts.placeholderClass)) as GridItemHTMLElement[];\r\n  }\r\n\r\n  /** true if changeCB should be ignored due to column change, sizeToContent, loading, etc... which caller can ignore for dirty flag case */\r\n  public isIgnoreChangeCB(): boolean { return this._ignoreLayoutsNodeChange; }\r\n\r\n  /**\r\n   * Destroys a grid instance. DO NOT CALL any methods or access any vars after this as it will free up members.\r\n   * @param removeDOM if `false` grid and items HTML elements will not be removed from the DOM (Optional. Default `true`).\r\n   */\r\n  public destroy(removeDOM = true): GridStack {\r\n    if (!this.el) return; // prevent multiple calls\r\n    this.offAll();\r\n    this._updateResizeEvent(true);\r\n    this.setStatic(true, false); // permanently removes DD but don't set CSS class (we're going away)\r\n    this.setAnimation(false);\r\n    if (!removeDOM) {\r\n      this.removeAll(removeDOM);\r\n      this.el.removeAttribute('gs-current-row');\r\n    } else {\r\n      this.el.parentNode.removeChild(this.el);\r\n    }\r\n    if (this.parentGridNode) delete this.parentGridNode.subGrid;\r\n    delete this.parentGridNode;\r\n    delete this.opts;\r\n    delete this._placeholder?.gridstackNode;\r\n    delete this._placeholder;\r\n    delete this.engine;\r\n    delete this.el.gridstack; // remove circular dependency that would prevent a freeing\r\n    delete this.el;\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * enable/disable floating widgets (default: `false`) See [example](http://gridstackjs.com/demo/float.html)\r\n   */\r\n  public float(val: boolean): GridStack {\r\n    if (this.opts.float !== val) {\r\n      this.opts.float = this.engine.float = val;\r\n      this._triggerChangeEvent();\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * get the current float mode\r\n   */\r\n  public getFloat(): boolean {\r\n    return this.engine.float;\r\n  }\r\n\r\n  /**\r\n   * Get the position of the cell under a pixel on screen.\r\n   * @param position the position of the pixel to resolve in\r\n   * absolute coordinates, as an object with top and left properties\r\n   * @param useDocRelative if true, value will be based on document position vs parent position (Optional. Default false).\r\n   * Useful when grid is within `position: relative` element\r\n   *\r\n   * Returns an object with properties `x` and `y` i.e. the column and row in the grid.\r\n   */\r\n  public getCellFromPixel(position: MousePosition, useDocRelative = false): CellPosition {\r\n    const box = this.el.getBoundingClientRect();\r\n    // console.log(`getBoundingClientRect left: ${box.left} top: ${box.top} w: ${box.w} h: ${box.h}`)\r\n    let containerPos: { top: number, left: number };\r\n    if (useDocRelative) {\r\n      containerPos = { top: box.top + document.documentElement.scrollTop, left: box.left };\r\n      // console.log(`getCellFromPixel scrollTop: ${document.documentElement.scrollTop}`)\r\n    } else {\r\n      containerPos = { top: this.el.offsetTop, left: this.el.offsetLeft }\r\n      // console.log(`getCellFromPixel offsetTop: ${containerPos.left} offsetLeft: ${containerPos.top}`)\r\n    }\r\n    const relativeLeft = position.left - containerPos.left;\r\n    const relativeTop = position.top - containerPos.top;\r\n\r\n    const columnWidth = (box.width / this.getColumn());\r\n    const rowHeight = (box.height / parseInt(this.el.getAttribute('gs-current-row')));\r\n\r\n    return { x: Math.floor(relativeLeft / columnWidth), y: Math.floor(relativeTop / rowHeight) };\r\n  }\r\n\r\n  /** returns the current number of rows, which will be at least `minRow` if set */\r\n  public getRow(): number {\r\n    return Math.max(this.engine.getRow(), this.opts.minRow || 0);\r\n  }\r\n\r\n  /**\r\n   * Checks if specified area is empty.\r\n   * @param x the position x.\r\n   * @param y the position y.\r\n   * @param w the width of to check\r\n   * @param h the height of to check\r\n   */\r\n  public isAreaEmpty(x: number, y: number, w: number, h: number): boolean {\r\n    return this.engine.isAreaEmpty(x, y, w, h);\r\n  }\r\n\r\n  /**\r\n   * If you add elements to your grid by hand (or have some framework creating DOM), you have to tell gridstack afterwards to make them widgets.\r\n   * If you want gridstack to add the elements for you, use `addWidget()` instead.\r\n   * Makes the given element a widget and returns it.\r\n   * @param els widget or single selector to convert.\r\n   * @param options widget definition to use instead of reading attributes or using default sizing values\r\n   *\r\n   * @example\r\n   * const grid = GridStack.init();\r\n   * grid.el.innerHtml = '<div id=\"1\" gs-w=\"3\"></div><div id=\"2\"></div>';\r\n   * grid.makeWidget('1');\r\n   * grid.makeWidget('2', {w:2, content: 'hello'});\r\n   */\r\n  public makeWidget(els: GridStackElement, options?: GridStackWidget): GridItemHTMLElement {\r\n    const el = GridStack.getElement(els);\r\n    if (!el || el.gridstackNode) return el;\r\n    if (!el.parentElement) this.el.appendChild(el);\r\n    this._prepareElement(el, true, options);\r\n    const node = el.gridstackNode;\r\n\r\n    this._updateContainerHeight();\r\n\r\n    // see if there is a sub-grid to create\r\n    if (node.subGridOpts) {\r\n      this.makeSubGrid(el, node.subGridOpts, undefined, false); // node.subGrid will be used as option in method, no need to pass\r\n    }\r\n\r\n    // if we're adding an item into 1 column make sure\r\n    // we don't override the larger 12 column layout that was already saved. #1985\r\n    let resetIgnoreLayoutsNodeChange: boolean;\r\n    if (this.opts.column === 1 && !this._ignoreLayoutsNodeChange) {\r\n      resetIgnoreLayoutsNodeChange = this._ignoreLayoutsNodeChange = true;\r\n    }\r\n    this._triggerAddEvent();\r\n    this._triggerChangeEvent();\r\n    if (resetIgnoreLayoutsNodeChange) delete this._ignoreLayoutsNodeChange;\r\n\r\n    return el;\r\n  }\r\n\r\n  /**\r\n   * Event handler that extracts our CustomEvent data out automatically for receiving custom\r\n   * notifications (see doc for supported events)\r\n   * @param name of the event (see possible values) or list of names space separated\r\n   * @param callback function called with event and optional second/third param\r\n   * (see README documentation for each signature).\r\n   *\r\n   * @example\r\n   * grid.on('added', function(e, items) { log('added ', items)} );\r\n   * or\r\n   * grid.on('added removed change', function(e, items) { log(e.type, items)} );\r\n   *\r\n   * Note: in some cases it is the same as calling native handler and parsing the event.\r\n   * grid.el.addEventListener('added', function(event) { log('added ', event.detail)} );\r\n   *\r\n   */\r\n  public on(name: 'dropped', callback: GridStackDroppedHandler): GridStack\r\n  public on(name: 'enable' | 'disable', callback: GridStackEventHandler): GridStack\r\n  public on(name: 'change' | 'added' | 'removed' | 'resizecontent', callback: GridStackNodesHandler): GridStack\r\n  public on(name: 'resizestart' | 'resize' | 'resizestop' | 'dragstart' | 'drag' | 'dragstop', callback: GridStackElementHandler): GridStack\r\n  public on(name: string, callback: GridStackEventHandlerCallback): GridStack\r\n  public on(name: GridStackEvent | string, callback: GridStackEventHandlerCallback): GridStack {\r\n    // check for array of names being passed instead\r\n    if (name.indexOf(' ') !== -1) {\r\n      const names = name.split(' ') as GridStackEvent[];\r\n      names.forEach(name => this.on(name, callback));\r\n      return this;\r\n    }\r\n\r\n    // native CustomEvent handlers - cash the generic handlers so we can easily remove\r\n    if (name === 'change' || name === 'added' || name === 'removed' || name === 'enable' || name === 'disable') {\r\n      const noData = (name === 'enable' || name === 'disable');\r\n      if (noData) {\r\n        this._gsEventHandler[name] = (event: Event) => (callback as GridStackEventHandler)(event);\r\n      } else {\r\n        this._gsEventHandler[name] = (event: CustomEvent) => {if (event.detail) (callback as GridStackNodesHandler)(event, event.detail)};\r\n      }\r\n      this.el.addEventListener(name, this._gsEventHandler[name]);\r\n    } else if (name === 'drag' || name === 'dragstart' || name === 'dragstop' || name === 'resizestart' || name === 'resize'\r\n      || name === 'resizestop' || name === 'dropped' || name === 'resizecontent') {\r\n      // drag&drop stop events NEED to be call them AFTER we update node attributes so handle them ourself.\r\n      // do same for start event to make it easier...\r\n      this._gsEventHandler[name] = callback;\r\n    } else {\r\n      console.error('GridStack.on(' + name + ') event not supported');\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * unsubscribe from the 'on' event GridStackEvent\r\n   * @param name of the event (see possible values) or list of names space separated\r\n   */\r\n  public off(name: GridStackEvent | string): GridStack {\r\n    // check for array of names being passed instead\r\n    if (name.indexOf(' ') !== -1) {\r\n      const names = name.split(' ') as GridStackEvent[];\r\n      names.forEach(name => this.off(name));\r\n      return this;\r\n    }\r\n\r\n    if (name === 'change' || name === 'added' || name === 'removed' || name === 'enable' || name === 'disable') {\r\n      // remove native CustomEvent handlers\r\n      if (this._gsEventHandler[name]) {\r\n        this.el.removeEventListener(name, this._gsEventHandler[name]);\r\n      }\r\n    }\r\n    delete this._gsEventHandler[name];\r\n\r\n    return this;\r\n  }\r\n\r\n  /** remove all event handlers */\r\n  public offAll(): GridStack {\r\n    Object.keys(this._gsEventHandler).forEach((key: GridStackEvent) => this.off(key));\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Removes widget from the grid.\r\n   * @param el  widget or selector to modify\r\n   * @param removeDOM if `false` DOM element won't be removed from the tree (Default? true).\r\n   * @param triggerEvent if `false` (quiet mode) element will not be added to removed list and no 'removed' callbacks will be called (Default? true).\r\n   */\r\n  public removeWidget(els: GridStackElement, removeDOM = true, triggerEvent = true): GridStack {\r\n    if (!els) { console.error('Error: GridStack.removeWidget(undefined) called'); return this; }\r\n\r\n    GridStack.getElements(els).forEach(el => {\r\n      if (el.parentElement && el.parentElement !== this.el) return; // not our child!\r\n      let node = el.gridstackNode;\r\n      // For Meteor support: https://github.com/gridstack/gridstack.js/pull/272\r\n      if (!node) {\r\n        node = this.engine.nodes.find(n => el === n.el);\r\n      }\r\n      if (!node) return;\r\n\r\n      if (removeDOM && GridStack.addRemoveCB) {\r\n        GridStack.addRemoveCB(this.el, node, false, false);\r\n      }\r\n\r\n      // remove our DOM data (circular link) and drag&drop permanently\r\n      delete el.gridstackNode;\r\n      this._removeDD(el);\r\n\r\n      this.engine.removeNode(node, removeDOM, triggerEvent);\r\n\r\n      if (removeDOM && el.parentElement) {\r\n        el.remove(); // in batch mode engine.removeNode doesn't call back to remove DOM\r\n      }\r\n    });\r\n    if (triggerEvent) {\r\n      this._triggerRemoveEvent();\r\n      this._triggerChangeEvent();\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Removes all widgets from the grid.\r\n   * @param removeDOM if `false` DOM elements won't be removed from the tree (Default? `true`).\r\n   * @param triggerEvent if `false` (quiet mode) element will not be added to removed list and no 'removed' callbacks will be called (Default? true).\r\n   */\r\n  public removeAll(removeDOM = true, triggerEvent = true): GridStack {\r\n    // always remove our DOM data (circular link) before list gets emptied and drag&drop permanently\r\n    this.engine.nodes.forEach(n => {\r\n      if (removeDOM && GridStack.addRemoveCB) {\r\n        GridStack.addRemoveCB(this.el, n, false, false);\r\n      }\r\n      delete n.el.gridstackNode;\r\n      if (!this.opts.staticGrid) this._removeDD(n.el);\r\n    });\r\n    this.engine.removeAll(removeDOM, triggerEvent);\r\n    if (triggerEvent) this._triggerRemoveEvent();\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Toggle the grid animation state.  Toggles the `grid-stack-animate` class.\r\n   * @param doAnimate if true the grid will animate.\r\n   * @param delay if true setting will be set on next event loop.\r\n   */\r\n  public setAnimation(doAnimate = this.opts.animate, delay?: boolean): GridStack {\r\n    if (delay) {\r\n      // delay, but check to make sure grid (opt) is still around\r\n      setTimeout(() => { if (this.opts) this.setAnimation(doAnimate) });\r\n    } else if (doAnimate) {\r\n      this.el.classList.add('grid-stack-animate');\r\n    } else {\r\n      this.el.classList.remove('grid-stack-animate');\r\n    }\r\n    this.opts.animate = doAnimate;\r\n    return this;\r\n  }\r\n\r\n  /** @internal */\r\n  private hasAnimationCSS(): boolean { return this.el.classList.contains('grid-stack-animate') }\r\n\r\n  /**\r\n   * Toggle the grid static state, which permanently removes/add Drag&Drop support, unlike disable()/enable() that just turns it off/on.\r\n   * Also toggle the grid-stack-static class.\r\n   * @param val if true the grid become static.\r\n   * @param updateClass true (default) if css class gets updated\r\n   * @param recurse true (default) if sub-grids also get updated\r\n   */\r\n  public setStatic(val: boolean, updateClass = true, recurse = true): GridStack {\r\n    if (!!this.opts.staticGrid === val) return this;\r\n    val ? this.opts.staticGrid = true : delete this.opts.staticGrid;\r\n    this._setupRemoveDrop();\r\n    this._setupAcceptWidget();\r\n    this.engine.nodes.forEach(n => {\r\n      this.prepareDragDrop(n.el); // either delete or init Drag&drop\r\n      if (n.subGrid && recurse) n.subGrid.setStatic(val, updateClass, recurse);\r\n    });\r\n    if (updateClass) { this._setStaticClass(); }\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Updates the passed in options on the grid (similar to update(widget) for for the grid options).\r\n   * @param options PARTIAL grid options to update - only items specified will be updated.\r\n   * NOTE: not all options updating are currently supported (lot of code, unlikely to change)\r\n   */\r\n  public updateOptions(o: GridStackOptions): GridStack {\r\n    const opts = this.opts;\r\n    if (o === opts) return this; // nothing to do\r\n    if (o.acceptWidgets !== undefined) { opts.acceptWidgets = o.acceptWidgets; this._setupAcceptWidget(); }\r\n    if (o.animate !== undefined) this.setAnimation(o.animate);\r\n    if (o.cellHeight) this.cellHeight(o.cellHeight);\r\n    if (o.class !== undefined && o.class !== opts.class) { if (opts.class) this.el.classList.remove(opts.class); if (o.class) this.el.classList.add(o.class); }\r\n    // responsive column take over actual count (keep what we have now)\r\n    if (o.columnOpts) {\r\n      this.opts.columnOpts = o.columnOpts;\r\n      this.checkDynamicColumn();\r\n    } else if (o.columnOpts === null && this.opts.columnOpts) {\r\n      delete this.opts.columnOpts;\r\n      this._updateResizeEvent();\r\n    } else if (typeof(o.column) === 'number') this.column(o.column);\r\n    if (o.margin !== undefined) this.margin(o.margin);\r\n    if (o.staticGrid !== undefined) this.setStatic(o.staticGrid);\r\n    if (o.disableDrag !== undefined && !o.staticGrid) this.enableMove(!o.disableDrag);\r\n    if (o.disableResize !== undefined && !o.staticGrid) this.enableResize(!o.disableResize);\r\n    if (o.float !== undefined) this.float(o.float);\r\n    if (o.row !== undefined) {\r\n      opts.minRow = opts.maxRow = opts.row = o.row;\r\n      this._updateContainerHeight();\r\n    } else {\r\n      if (o.minRow !== undefined) { opts.minRow = o.minRow; this._updateContainerHeight(); }\r\n      if (o.maxRow !== undefined) opts.maxRow = o.maxRow;\r\n    }\r\n    if (o.children?.length) this.load(o.children);\r\n    // TBD if we have a real need for these (more complex code)\r\n    // alwaysShowResizeHandle, draggable, handle, handleClass, itemClass, layout, placeholderClass, placeholderText, resizable, removable, row,...\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Updates widget position/size and other info. Note: if you need to call this on all nodes, use load() instead which will update what changed.\r\n   * @param els  widget or selector of objects to modify (note: setting the same x,y for multiple items will be indeterministic and likely unwanted)\r\n   * @param opt new widget options (x,y,w,h, etc..). Only those set will be updated.\r\n   */\r\n  public update(els: GridStackElement, opt: GridStackWidget): GridStack {\r\n\r\n    GridStack.getElements(els).forEach(el => {\r\n      const n = el?.gridstackNode;\r\n      if (!n) return;\r\n      const w = {...Utils.copyPos({}, n), ...Utils.cloneDeep(opt)}; // make a copy we can modify in case they re-use it or multiple items\r\n      this.engine.nodeBoundFix(w);\r\n      delete w.autoPosition;\r\n\r\n      // move/resize widget if anything changed\r\n      const keys = ['x', 'y', 'w', 'h'];\r\n      let m: GridStackWidget;\r\n      if (keys.some(k => w[k] !== undefined && w[k] !== n[k])) {\r\n        m = {};\r\n        keys.forEach(k => {\r\n          m[k] = (w[k] !== undefined) ? w[k] : n[k];\r\n          delete w[k];\r\n        });\r\n      }\r\n      // for a move as well IFF there is any min/max fields set\r\n      if (!m && (w.minW || w.minH || w.maxW || w.maxH)) {\r\n        m = {}; // will use node position but validate values\r\n      }\r\n\r\n      // check for content changing\r\n      if (w.content !== undefined) {\r\n        const itemContent = el.querySelector('.grid-stack-item-content') as HTMLElement;\r\n        if (itemContent && itemContent.textContent !== w.content) {\r\n          n.content = w.content;\r\n          GridStack.renderCB(itemContent, w);\r\n          // restore any sub-grid back\r\n          if (n.subGrid?.el) {\r\n            itemContent.appendChild(n.subGrid.el);\r\n            n.subGrid._updateContainerHeight();\r\n          }\r\n        }\r\n        delete w.content;\r\n      }\r\n\r\n      // any remaining fields are assigned, but check for dragging changes, resize constrain\r\n      let changed = false;\r\n      let ddChanged = false;\r\n      for (const key in w) {\r\n        if (key[0] !== '_' && n[key] !== w[key]) {\r\n          n[key] = w[key];\r\n          changed = true;\r\n          ddChanged = ddChanged || (!this.opts.staticGrid && (key === 'noResize' || key === 'noMove' || key === 'locked'));\r\n        }\r\n      }\r\n      Utils.sanitizeMinMax(n);\r\n\r\n      // finally move the widget and update attr\r\n      if (m) {\r\n        const widthChanged = (m.w !== undefined && m.w !== n.w);\r\n        this.moveNode(n, m);\r\n        if (widthChanged && n.subGrid) {\r\n          // if we're animating the client size hasn't changed yet, so force a change (not exact size)\r\n          n.subGrid.onResize(this.hasAnimationCSS() ? n.w : undefined);\r\n        } else {\r\n          this.resizeToContentCheck(widthChanged, n);\r\n        }\r\n        delete n._orig; // clear out original position now that we moved #2669\r\n      }\r\n      if (m || changed) {\r\n        this._writeAttr(el, n);\r\n      }\r\n      if (ddChanged) {\r\n        this.prepareDragDrop(n.el);\r\n      }\r\n      if (GridStack.updateCB) GridStack.updateCB(n); // call user callback so they know widget got updated\r\n    });\r\n\r\n    return this;\r\n  }\r\n\r\n  private moveNode(n: GridStackNode, m: GridStackMoveOpts) {\r\n    const wasUpdating = n._updating;\r\n    if (!wasUpdating) this.engine.cleanNodes().beginUpdate(n);\r\n    this.engine.moveNode(n, m);\r\n    this._updateContainerHeight();\r\n    if (!wasUpdating) {\r\n      this._triggerChangeEvent();\r\n      this.engine.endUpdate();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Updates widget height to match the content height to avoid v-scrollbar or dead space.\r\n   * Note: this assumes only 1 child under resizeToContentParent='.grid-stack-item-content' (sized to gridItem minus padding) that is at the entire content size wanted.\r\n   * @param el grid item element\r\n   * @param useNodeH set to true if GridStackNode.h should be used instead of actual container height when we don't need to wait for animation to finish to get actual DOM heights\r\n   */\r\n  public resizeToContent(el: GridItemHTMLElement) {\r\n    if (!el) return;\r\n    el.classList.remove('size-to-content-max');\r\n    if (!el.clientHeight) return; // 0 when hidden, skip\r\n    const n = el.gridstackNode;\r\n    if (!n) return;\r\n    const grid = n.grid;\r\n    if (!grid || el.parentElement !== grid.el) return; // skip if we are not inside a grid\r\n    const cell = grid.getCellHeight(true);\r\n    if (!cell) return;\r\n    let height = n.h ? n.h * cell : el.clientHeight; // getBoundingClientRect().height seem to flicker back and forth\r\n    let item: Element;\r\n    if (n.resizeToContentParent) item = el.querySelector(n.resizeToContentParent);\r\n    if (!item) item = el.querySelector(GridStack.resizeToContentParent);\r\n    if (!item) return;\r\n    const padding = el.clientHeight - item.clientHeight; // full - available height to our child (minus border, padding...)\r\n    const itemH = n.h ? n.h * cell - padding : item.clientHeight; // calculated to what cellHeight is or will become (rather than actual to prevent waiting for animation to finish)\r\n    let wantedH: number;\r\n    if (n.subGrid) {\r\n      // sub-grid - use their actual row count * their cell height, BUT append any content outside of the grid (eg: above text)\r\n      wantedH = n.subGrid.getRow() * n.subGrid.getCellHeight(true);\r\n      const subRec = n.subGrid.el.getBoundingClientRect();\r\n      const parentRec = el.getBoundingClientRect();\r\n      wantedH += subRec.top - parentRec.top;\r\n    } else if (n.subGridOpts?.children?.length) {\r\n      // not sub-grid just yet (case above) wait until we do\r\n      return;\r\n    } else {\r\n      // NOTE: clientHeight & getBoundingClientRect() is undefined for text and other leaf nodes. use <div> container!\r\n      const child = item.firstElementChild;\r\n      if (!child) {\r\n        console.error(`Error: GridStack.resizeToContent() widget id:${n.id} '${GridStack.resizeToContentParent}'.firstElementChild is null, make sure to have a div like container. Skipping sizing.`);\r\n        return;\r\n      }\r\n      wantedH = child.getBoundingClientRect().height || itemH;\r\n    }\r\n    if (itemH === wantedH) return;\r\n    height += wantedH - itemH;\r\n    let h = Math.ceil(height / cell);\r\n    // check for min/max and special sizing\r\n    const softMax = Number.isInteger(n.sizeToContent) ? n.sizeToContent as number : 0;\r\n    if (softMax && h > softMax) {\r\n      h = softMax;\r\n      el.classList.add('size-to-content-max');  // get v-scroll back\r\n    }\r\n    if (n.minH && h < n.minH) h = n.minH;\r\n    else if (n.maxH && h > n.maxH) h = n.maxH;\r\n    if (h !== n.h) {\r\n      grid._ignoreLayoutsNodeChange = true;\r\n      grid.moveNode(n, { h });\r\n      delete grid._ignoreLayoutsNodeChange;\r\n    }\r\n  }\r\n\r\n  /** call the user resize (so they can do extra work) else our build in version */\r\n  private resizeToContentCBCheck(el: GridItemHTMLElement) {\r\n    if (GridStack.resizeToContentCB) GridStack.resizeToContentCB(el);\r\n    else this.resizeToContent(el);\r\n  }\r\n\r\n  /** rotate (by swapping w & h) the passed in node - called when user press 'r' during dragging\r\n   * @param els  widget or selector of objects to modify\r\n   * @param relative optional pixel coord relative to upper/left corner to rotate around (will keep that cell under cursor)\r\n   */\r\n  public rotate(els: GridStackElement, relative?: Position): GridStack {\r\n    GridStack.getElements(els).forEach(el => {\r\n      const n = el.gridstackNode;\r\n      if (!Utils.canBeRotated(n)) return;\r\n      const rot: GridStackWidget = { w: n.h, h: n.w, minH: n.minW, minW: n.minH, maxH: n.maxW, maxW: n.maxH };\r\n      // if given an offset, adjust x/y by column/row bounds when user presses 'r' during dragging\r\n      if (relative) {\r\n        const pivotX = relative.left > 0 ? Math.floor(relative.left / this.cellWidth()) : 0;\r\n        const pivotY = relative.top > 0 ? Math.floor(relative.top / (this.opts.cellHeight as number)) : 0;\r\n        rot.x = n.x + pivotX - (n.h - (pivotY+1));\r\n        rot.y = (n.y + pivotY) - pivotX;\r\n      }\r\n      Object.keys(rot).forEach(k => { if (rot[k] === undefined) delete rot[k]; });\r\n      const _orig = n._orig;\r\n      this.update(el, rot);\r\n      n._orig = _orig; // restore as move() will delete it\r\n    });\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Updates the margins which will set all 4 sides at once - see `GridStackOptions.margin` for format options (CSS string format of 1,2,4 values or single number).\r\n   * @param value margin value\r\n   */\r\n  public margin(value: numberOrString): GridStack {\r\n    const isMultiValue = (typeof value === 'string' && value.split(' ').length > 1);\r\n    // check if we can skip... won't check if multi values (too much hassle)\r\n    if (!isMultiValue) {\r\n      const data = Utils.parseHeight(value);\r\n      if (this.opts.marginUnit === data.unit && this.opts.margin === data.h) return;\r\n    }\r\n    // re-use existing margin handling\r\n    this.opts.margin = value;\r\n    this.opts.marginTop = this.opts.marginBottom = this.opts.marginLeft = this.opts.marginRight = undefined;\r\n    this._initMargin();\r\n\r\n    return this;\r\n  }\r\n\r\n  /** returns current margin number value (undefined if 4 sides don't match) */\r\n  public getMargin(): number { return this.opts.margin as number; }\r\n\r\n  /**\r\n   * Returns true if the height of the grid will be less than the vertical\r\n   * constraint. Always returns true if grid doesn't have height constraint.\r\n   * @param node contains x,y,w,h,auto-position options\r\n   *\r\n   * @example\r\n   * if (grid.willItFit(newWidget)) {\r\n   *   grid.addWidget(newWidget);\r\n   * } else {\r\n   *   alert('Not enough free space to place the widget');\r\n   * }\r\n   */\r\n  public willItFit(node: GridStackWidget): boolean {\r\n    // support legacy call for now\r\n    if (arguments.length > 1) {\r\n      console.warn('gridstack.ts: `willItFit(x,y,w,h,autoPosition)` is deprecated. Use `willItFit({x, y,...})`. It will be removed soon');\r\n      // eslint-disable-next-line prefer-rest-params\r\n      const a = arguments; let i = 0,\r\n        w: GridStackWidget = { x: a[i++], y: a[i++], w: a[i++], h: a[i++], autoPosition: a[i++] };\r\n      return this.willItFit(w);\r\n    }\r\n    return this.engine.willItFit(node);\r\n  }\r\n\r\n  /** @internal */\r\n  protected _triggerChangeEvent(): GridStack {\r\n    if (this.engine.batchMode) return this;\r\n    const elements = this.engine.getDirtyNodes(true); // verify they really changed\r\n    if (elements && elements.length) {\r\n      if (!this._ignoreLayoutsNodeChange) {\r\n        this.engine.layoutsNodesChange(elements);\r\n      }\r\n      this._triggerEvent('change', elements);\r\n    }\r\n    this.engine.saveInitial(); // we called, now reset initial values & dirty flags\r\n    return this;\r\n  }\r\n\r\n  /** @internal */\r\n  protected _triggerAddEvent(): GridStack {\r\n    if (this.engine.batchMode) return this;\r\n    if (this.engine.addedNodes?.length) {\r\n      if (!this._ignoreLayoutsNodeChange) {\r\n        this.engine.layoutsNodesChange(this.engine.addedNodes);\r\n      }\r\n      // prevent added nodes from also triggering 'change' event (which is called next)\r\n      this.engine.addedNodes.forEach(n => { delete n._dirty; });\r\n      const addedNodes = [...this.engine.addedNodes];\r\n      this.engine.addedNodes = [];\r\n      this._triggerEvent('added', addedNodes);\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /** @internal */\r\n  public _triggerRemoveEvent(): GridStack {\r\n    if (this.engine.batchMode) return this;\r\n    if (this.engine.removedNodes?.length) {\r\n      const removedNodes = [...this.engine.removedNodes];\r\n      this.engine.removedNodes = [];\r\n      this._triggerEvent('removed', removedNodes);\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /** @internal */\r\n  protected _triggerEvent(type: string, data?: GridStackNode[]): GridStack {\r\n    const event = data ? new CustomEvent(type, { bubbles: false, detail: data }) : new Event(type);\r\n    // check if we're nested, and if so call the outermost grid to trigger the event\r\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\r\n    let grid: GridStack = this;\r\n    while (grid.parentGridNode) grid = grid.parentGridNode.grid;\r\n    grid.el.dispatchEvent(event);\r\n    return this;\r\n  }\r\n\r\n  /** @internal */\r\n  protected _updateContainerHeight(): GridStack {\r\n    if (!this.engine || this.engine.batchMode) return this;\r\n    const parent = this.parentGridNode;\r\n    let row = this.getRow() + this._extraDragRow; // this checks for minRow already\r\n    const cellHeight = this.opts.cellHeight as number;\r\n    const unit = this.opts.cellHeightUnit;\r\n    if (!cellHeight) return this;\r\n\r\n    // check for css min height (non nested grid). TODO: support mismatch, say: min % while unit is px.\r\n    // If `minRow` was applied, don't override it with this check, and avoid performance issues\r\n    // (reflows) using `getComputedStyle`\r\n    if (!parent && !this.opts.minRow) {\r\n      const cssMinHeight = Utils.parseHeight(getComputedStyle(this.el)['minHeight']);\r\n      if (cssMinHeight.h > 0 && cssMinHeight.unit === unit) {\r\n        const minRow = Math.floor(cssMinHeight.h / cellHeight);\r\n        if (row < minRow) {\r\n          row = minRow;\r\n        }\r\n      }\r\n    }\r\n\r\n    this.el.setAttribute('gs-current-row', String(row));\r\n    this.el.style.removeProperty('min-height');\r\n    this.el.style.removeProperty('height');\r\n    if (row) {\r\n      // nested grids have 'insert:0' to fill the space of parent by default, but we may be taller so use min-height for possible scrollbars\r\n      this.el.style[parent ? 'minHeight' : 'height'] = row * cellHeight + unit;\r\n    }\r\n\r\n    // if we're a nested grid inside an sizeToContent item, tell it to resize itself too\r\n    if (parent && Utils.shouldSizeToContent(parent)) {\r\n      parent.grid.resizeToContentCBCheck(parent.el);\r\n    }\r\n\r\n    return this;\r\n  }\r\n\r\n  /** @internal */\r\n  protected _prepareElement(el: GridItemHTMLElement, triggerAddEvent = false, node?: GridStackNode): GridStack {\r\n    node = node || this._readAttr(el);\r\n    el.gridstackNode = node;\r\n    node.el = el;\r\n    node.grid = this;\r\n    node = this.engine.addNode(node, triggerAddEvent);\r\n\r\n    // write the dom sizes and class\r\n    this._writeAttr(el, node);\r\n    el.classList.add(gridDefaults.itemClass, this.opts.itemClass);\r\n    const sizeToContent = Utils.shouldSizeToContent(node);\r\n    sizeToContent ? el.classList.add('size-to-content') : el.classList.remove('size-to-content');\r\n    if (sizeToContent) this.resizeToContentCheck(false, node);\r\n\r\n    if (!Utils.lazyLoad(node)) this.prepareDragDrop(node.el);\r\n\r\n    return this;\r\n  }\r\n\r\n  /** @internal write position CSS vars and x,y,w,h attributes (not used for CSS but by users) back to element */\r\n  protected _writePosAttr(el: HTMLElement, n: GridStackNode): GridStack {\r\n    // Avoid overwriting the inline style of the element during drag/resize, but always update the placeholder\r\n    if ((!n._moving && !n._resizing) || this._placeholder === el) {\r\n      // width/height:1 x/y:0 is set by default in the main CSS, so no need to set inlined vars\r\n      el.style.top = n.y ? (n.y === 1 ? `var(--gs-cell-height)` : `calc(${n.y} * var(--gs-cell-height))`) : null;\r\n      el.style.left = n.x ? (n.x === 1 ? `var(--gs-column-width)` : `calc(${n.x} * var(--gs-column-width))`) : null;\r\n      el.style.width = n.w > 1 ? `calc(${n.w} * var(--gs-column-width))` : null;\r\n      el.style.height = n.h > 1 ? `calc(${n.h} * var(--gs-cell-height))` : null;\r\n    }\r\n    // NOTE: those are technically not needed anymore (v12+) as we have CSS vars for everything, but some users depends on them to render item size using CSS\r\n    n.x > 0 ? el.setAttribute('gs-x', String(n.x)) : el.removeAttribute('gs-x');\r\n    n.y > 0 ? el.setAttribute('gs-y', String(n.y)) : el.removeAttribute('gs-y');\r\n    n.w > 1 ? el.setAttribute('gs-w', String(n.w)) : el.removeAttribute('gs-w');\r\n    n.h > 1 ? el.setAttribute('gs-h', String(n.h)) : el.removeAttribute('gs-h');\r\n    return this;\r\n  }\r\n\r\n  /** @internal call to write any default attributes back to element */\r\n  protected _writeAttr(el: HTMLElement, node: GridStackNode): GridStack {\r\n    if (!node) return this;\r\n    this._writePosAttr(el, node);\r\n\r\n    const attrs /*: GridStackWidget but strings */ = { // remaining attributes\r\n      // autoPosition: 'gs-auto-position', // no need to write out as already in node and doesn't affect CSS\r\n      noResize: 'gs-no-resize',\r\n      noMove: 'gs-no-move',\r\n      locked: 'gs-locked',\r\n      id: 'gs-id',\r\n      sizeToContent: 'gs-size-to-content',\r\n    };\r\n    for (const key in attrs) {\r\n      if (node[key]) { // 0 is valid for x,y only but done above already and not in list anyway\r\n        el.setAttribute(attrs[key], String(node[key]));\r\n      } else {\r\n        el.removeAttribute(attrs[key]);\r\n      }\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /** @internal call to read any default attributes from element */\r\n  protected _readAttr(el: HTMLElement, clearDefaultAttr = true): GridStackWidget {\r\n    const n: GridStackNode = {};\r\n    n.x = Utils.toNumber(el.getAttribute('gs-x'));\r\n    n.y = Utils.toNumber(el.getAttribute('gs-y'));\r\n    n.w = Utils.toNumber(el.getAttribute('gs-w'));\r\n    n.h = Utils.toNumber(el.getAttribute('gs-h'));\r\n    n.autoPosition = Utils.toBool(el.getAttribute('gs-auto-position'));\r\n    n.noResize = Utils.toBool(el.getAttribute('gs-no-resize'));\r\n    n.noMove = Utils.toBool(el.getAttribute('gs-no-move'));\r\n    n.locked = Utils.toBool(el.getAttribute('gs-locked'));\r\n    const attr = el.getAttribute('gs-size-to-content');\r\n    if (attr) {\r\n      if (attr === 'true' || attr === 'false') n.sizeToContent = Utils.toBool(attr);\r\n      else n.sizeToContent = parseInt(attr, 10);\r\n    }\r\n    n.id = el.getAttribute('gs-id');\r\n\r\n    // read but never written out\r\n    n.maxW = Utils.toNumber(el.getAttribute('gs-max-w'));\r\n    n.minW = Utils.toNumber(el.getAttribute('gs-min-w'));\r\n    n.maxH = Utils.toNumber(el.getAttribute('gs-max-h'));\r\n    n.minH = Utils.toNumber(el.getAttribute('gs-min-h'));\r\n\r\n    // v8.x optimization to reduce un-needed attr that don't render or are default CSS\r\n    if (clearDefaultAttr) {\r\n      if (n.w === 1) el.removeAttribute('gs-w');\r\n      if (n.h === 1) el.removeAttribute('gs-h');\r\n      if (n.maxW) el.removeAttribute('gs-max-w');\r\n      if (n.minW) el.removeAttribute('gs-min-w');\r\n      if (n.maxH) el.removeAttribute('gs-max-h');\r\n      if (n.minH) el.removeAttribute('gs-min-h');\r\n    }\r\n\r\n    // remove any key not found (null or false which is default, unless sizeToContent=false override)\r\n    for (const key in n) {\r\n      if (!n.hasOwnProperty(key)) return;\r\n      if (!n[key] && n[key] !== 0 && key !== 'sizeToContent') { // 0 can be valid value (x,y only really)\r\n        delete n[key];\r\n      }\r\n    }\r\n\r\n    return n;\r\n  }\r\n\r\n  /** @internal */\r\n  protected _setStaticClass(): GridStack {\r\n    const classes = ['grid-stack-static'];\r\n\r\n    if (this.opts.staticGrid) {\r\n      this.el.classList.add(...classes);\r\n      this.el.setAttribute('gs-static', 'true');\r\n    } else {\r\n      this.el.classList.remove(...classes);\r\n      this.el.removeAttribute('gs-static');\r\n\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * called when we are being resized - check if the one Column Mode needs to be turned on/off\r\n   * and remember the prev columns we used, or get our count from parent, as well as check for cellHeight==='auto' (square)\r\n   * or `sizeToContent` gridItem options.\r\n   */\r\n  public onResize(clientWidth = this.el?.clientWidth): GridStack {\r\n    if (!clientWidth) return; // return if we're gone or no size yet (will get called again)\r\n    if (this.prevWidth === clientWidth) return; // no-op\r\n    this.prevWidth = clientWidth\r\n    // console.log('onResize ', clientWidth);\r\n\r\n    this.batchUpdate();\r\n\r\n    // see if we're nested and take our column count from our parent....\r\n    let columnChanged = false;\r\n    if (this._autoColumn && this.parentGridNode) {\r\n      if (this.opts.column !== this.parentGridNode.w) {\r\n        this.column(this.parentGridNode.w, this.opts.layout || 'list');\r\n        columnChanged = true;\r\n      }\r\n    } else {\r\n      // else check for dynamic column\r\n      columnChanged = this.checkDynamicColumn();\r\n    }\r\n\r\n    // make the cells content square again\r\n    if (this._isAutoCellHeight) this.cellHeight();\r\n\r\n    // update any nested grids, or items size\r\n    this.engine.nodes.forEach(n => {\r\n      if (n.subGrid) n.subGrid.onResize()\r\n    });\r\n\r\n    if (!this._skipInitialResize) this.resizeToContentCheck(columnChanged); // wait for anim of column changed (DOM reflow before we can size correctly)\r\n    delete this._skipInitialResize;\r\n\r\n    this.batchUpdate(false);\r\n\r\n    return this;\r\n  }\r\n\r\n  /** resizes content for given node (or all) if shouldSizeToContent() is true */\r\n  private resizeToContentCheck(delay = false, n: GridStackNode = undefined) {\r\n    if (!this.engine) return; // we've been deleted in between!\r\n\r\n    // update any gridItem height with sizeToContent, but wait for DOM $animation_speed to settle if we changed column count\r\n    // TODO: is there a way to know what the final (post animation) size of the content will be so we can animate the column width and height together rather than sequentially ?\r\n    if (delay && this.hasAnimationCSS()) return setTimeout(() => this.resizeToContentCheck(false, n), this.animationDelay);\r\n\r\n    if (n) {\r\n      if (Utils.shouldSizeToContent(n)) this.resizeToContentCBCheck(n.el);\r\n    } else if (this.engine.nodes.some(n => Utils.shouldSizeToContent(n))) {\r\n      const nodes = [...this.engine.nodes]; // in case order changes while resizing one\r\n      this.batchUpdate();\r\n      nodes.forEach(n => {\r\n        if (Utils.shouldSizeToContent(n)) this.resizeToContentCBCheck(n.el);\r\n      });\r\n      this._ignoreLayoutsNodeChange = true; // loop through each node will set/reset around each move, so set it here again\r\n      this.batchUpdate(false);\r\n      this._ignoreLayoutsNodeChange = false;\r\n    }\r\n    // call this regardless of shouldSizeToContent because widget might need to stretch to take available space after a resize\r\n    if (this._gsEventHandler['resizecontent']) this._gsEventHandler['resizecontent'](null, n ? [n] : this.engine.nodes);\r\n  }\r\n\r\n  /** add or remove the grid element size event handler */\r\n  protected _updateResizeEvent(forceRemove = false): GridStack {\r\n    // only add event if we're not nested (parent will call us) and we're auto sizing cells or supporting dynamic column (i.e. doing work)\r\n    // or supporting new sizeToContent option.\r\n    const trackSize = !this.parentGridNode && (this._isAutoCellHeight || this.opts.sizeToContent || this.opts.columnOpts\r\n      || this.engine.nodes.find(n => n.sizeToContent));\r\n\r\n    if (!forceRemove && trackSize && !this.resizeObserver) {\r\n      this._sizeThrottle = Utils.throttle(() => this.onResize(), this.opts.cellHeightThrottle);\r\n      this.resizeObserver = new ResizeObserver(() => this._sizeThrottle());\r\n      this.resizeObserver.observe(this.el);\r\n      this._skipInitialResize = true; // makeWidget will originally have called on startup\r\n    } else if ((forceRemove || !trackSize) && this.resizeObserver) {\r\n      this.resizeObserver.disconnect();\r\n      delete this.resizeObserver;\r\n      delete this._sizeThrottle;\r\n    }\r\n\r\n    return this;\r\n  }\r\n\r\n  /** @internal convert a potential selector into actual element */\r\n  public static getElement(els: GridStackElement = '.grid-stack-item'): GridItemHTMLElement { return Utils.getElement(els) }\r\n  /** @internal */\r\n  public static getElements(els: GridStackElement = '.grid-stack-item'): GridItemHTMLElement[] { return Utils.getElements(els) }\r\n  /** @internal */\r\n  public static getGridElement(els: GridStackElement): GridHTMLElement { return GridStack.getElement(els) }\r\n  /** @internal */\r\n  public static getGridElements(els: string): GridHTMLElement[] { return Utils.getElements(els) }\r\n\r\n  /** @internal initialize margin top/bottom/left/right and units */\r\n  protected _initMargin(): GridStack {\r\n    let data: HeightData;\r\n    let margin = 0;\r\n\r\n    // support passing multiple values like CSS (ex: '5px 10px 0 20px')\r\n    let margins: string[] = [];\r\n    if (typeof this.opts.margin === 'string') {\r\n      margins = this.opts.margin.split(' ')\r\n    }\r\n    if (margins.length === 2) { // top/bot, left/right like CSS\r\n      this.opts.marginTop = this.opts.marginBottom = margins[0];\r\n      this.opts.marginLeft = this.opts.marginRight = margins[1];\r\n    } else if (margins.length === 4) { // Clockwise like CSS\r\n      this.opts.marginTop = margins[0];\r\n      this.opts.marginRight = margins[1];\r\n      this.opts.marginBottom = margins[2];\r\n      this.opts.marginLeft = margins[3];\r\n    } else {\r\n      data = Utils.parseHeight(this.opts.margin);\r\n      this.opts.marginUnit = data.unit;\r\n      margin = this.opts.margin = data.h;\r\n    }\r\n\r\n    // see if top/bottom/left/right need to be set as well\r\n    const keys = ['marginTop', 'marginRight', 'marginBottom', 'marginLeft'];\r\n    keys.forEach(k => {\r\n      if (this.opts[k] === undefined) {\r\n        this.opts[k] = margin;\r\n      } else {\r\n        data = Utils.parseHeight(this.opts[k]);\r\n        this.opts[k] = data.h;\r\n        delete this.opts.margin;\r\n      }\r\n    });\r\n    this.opts.marginUnit = data.unit; // in case side were spelled out, use those units instead...\r\n    if (this.opts.marginTop === this.opts.marginBottom && this.opts.marginLeft === this.opts.marginRight && this.opts.marginTop === this.opts.marginRight) {\r\n      this.opts.margin = this.opts.marginTop; // makes it easier to check for no-ops in setMargin()\r\n    }\r\n\r\n    // finally Update the CSS margin variables (inside the cell height) */\r\n    const style = this.el.style;\r\n    style.setProperty('--gs-item-margin-top', `${this.opts.marginTop}${this.opts.marginUnit}`);\r\n    style.setProperty('--gs-item-margin-bottom', `${this.opts.marginBottom}${this.opts.marginUnit}`);\r\n    style.setProperty('--gs-item-margin-right', `${this.opts.marginRight}${this.opts.marginUnit}`);\r\n    style.setProperty('--gs-item-margin-left', `${this.opts.marginLeft}${this.opts.marginUnit}`);\r\n\r\n    return this;\r\n  }\r\n\r\n  static GDRev = '12.2.2';\r\n\r\n  /* ===========================================================================================\r\n   * drag&drop methods that used to be stubbed out and implemented in dd-gridstack.ts\r\n   * but caused loading issues in prod - see https://github.com/gridstack/gridstack.js/issues/2039\r\n   * ===========================================================================================\r\n   */\r\n\r\n  /** get the global (but static to this code) DD implementation */\r\n  public static getDD(): DDGridStack {\r\n    return dd;\r\n  }\r\n\r\n  /**\r\n   * call to setup dragging in from the outside (say toolbar), by specifying the class selection and options.\r\n   * Called during GridStack.init() as options, but can also be called directly (last param are used) in case the toolbar\r\n   * is dynamically create and needs to be set later.\r\n   * @param dragIn string selector (ex: '.sidebar-item') or list of dom elements\r\n   * @param dragInOptions options - see DDDragOpt. (default: {handle: '.grid-stack-item-content', appendTo: 'body'}\r\n   * @param widgets GridStackWidget def to assign to each element which defines what to create on drop\r\n   * @param root optional root which defaults to document (for shadow dom pass the parent HTMLDocument)\r\n   */\r\n  public static setupDragIn(dragIn?: string | HTMLElement[], dragInOptions?: DDDragOpt, widgets?: GridStackWidget[], root: HTMLElement | Document = document): void {\r\n    if (dragInOptions?.pause !== undefined) {\r\n      DDManager.pauseDrag = dragInOptions.pause;\r\n    }\r\n\r\n    dragInOptions = { appendTo: 'body', helper: 'clone', ...(dragInOptions || {}) }; // default to handle:undefined = drag by the whole item\r\n    const els = (typeof dragIn === 'string') ? Utils.getElements(dragIn, root) : dragIn;\r\n    els.forEach((el, i) => {\r\n      if (!dd.isDraggable(el)) dd.dragIn(el, dragInOptions);\r\n      if (widgets?.[i]) (el as GridItemHTMLElement).gridstackNode = widgets[i];\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Enables/Disables dragging by the user of specific grid element. If you want all items, and have it affect future items, use enableMove() instead. No-op for static grids.\r\n   * IF you are looking to prevent an item from moving (due to being pushed around by another during collision) use locked property instead.\r\n   * @param els widget or selector to modify.\r\n   * @param val if true widget will be draggable, assuming the parent grid isn't noMove or static.\r\n   */\r\n  public movable(els: GridStackElement, val: boolean): GridStack {\r\n    if (this.opts.staticGrid) return this; // can't move a static grid!\r\n    GridStack.getElements(els).forEach(el => {\r\n      const n = el.gridstackNode;\r\n      if (!n) return;\r\n      val ? delete n.noMove : n.noMove = true;\r\n      this.prepareDragDrop(n.el); // init DD if need be, and adjust\r\n    });\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Enables/Disables user resizing of specific grid element. If you want all items, and have it affect future items, use enableResize() instead. No-op for static grids.\r\n   * @param els  widget or selector to modify\r\n   * @param val  if true widget will be resizable, assuming the parent grid isn't noResize or static.\r\n   */\r\n  public resizable(els: GridStackElement, val: boolean): GridStack {\r\n    if (this.opts.staticGrid) return this; // can't resize a static grid!\r\n    GridStack.getElements(els).forEach(el => {\r\n      const n = el.gridstackNode;\r\n      if (!n) return;\r\n      val ? delete n.noResize : n.noResize = true;\r\n      this.prepareDragDrop(n.el); // init DD if need be, and adjust\r\n    });\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Temporarily disables widgets moving/resizing.\r\n   * If you want a more permanent way (which freezes up resources) use `setStatic(true)` instead.\r\n   * Note: no-op for static grid\r\n   * This is a shortcut for:\r\n   * @example\r\n   *  grid.enableMove(false);\r\n   *  grid.enableResize(false);\r\n   * @param recurse true (default) if sub-grids also get updated\r\n   */\r\n  public disable(recurse = true): GridStack {\r\n    if (this.opts.staticGrid) return;\r\n    this.enableMove(false, recurse);\r\n    this.enableResize(false, recurse);\r\n    this._triggerEvent('disable');\r\n    return this;\r\n  }\r\n  /**\r\n   * Re-enables widgets moving/resizing - see disable().\r\n   * Note: no-op for static grid.\r\n   * This is a shortcut for:\r\n   * @example\r\n   *  grid.enableMove(true);\r\n   *  grid.enableResize(true);\r\n   * @param recurse true (default) if sub-grids also get updated\r\n   */\r\n  public enable(recurse = true): GridStack {\r\n    if (this.opts.staticGrid) return;\r\n    this.enableMove(true, recurse);\r\n    this.enableResize(true, recurse);\r\n    this._triggerEvent('enable');\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Enables/disables widget moving. No-op for static grids, and locally defined items still overrule\r\n   * @param recurse true (default) if sub-grids also get updated\r\n   */\r\n  public enableMove(doEnable: boolean, recurse = true): GridStack {\r\n    if (this.opts.staticGrid) return this; // can't move a static grid!\r\n    doEnable ? delete this.opts.disableDrag : this.opts.disableDrag = true; // FIRST before we update children as grid overrides #1658\r\n    this.engine.nodes.forEach(n => {\r\n      this.prepareDragDrop(n.el);\r\n      if (n.subGrid && recurse) n.subGrid.enableMove(doEnable, recurse);\r\n    });\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * Enables/disables widget resizing. No-op for static grids.\r\n   * @param recurse true (default) if sub-grids also get updated\r\n   */\r\n  public enableResize(doEnable: boolean, recurse = true): GridStack {\r\n    if (this.opts.staticGrid) return this; // can't size a static grid!\r\n    doEnable ? delete this.opts.disableResize : this.opts.disableResize = true; // FIRST before we update children as grid overrides #1658\r\n    this.engine.nodes.forEach(n => {\r\n      this.prepareDragDrop(n.el);\r\n      if (n.subGrid && recurse) n.subGrid.enableResize(doEnable, recurse);\r\n    });\r\n    return this;\r\n  }\r\n\r\n  /** @internal call when drag (and drop) needs to be cancelled (Esc key) */\r\n  public cancelDrag() {\r\n    const n = this._placeholder?.gridstackNode;\r\n    if (!n) return;\r\n    if (n._isExternal) {\r\n      // remove any newly inserted nodes (from outside)\r\n      n._isAboutToRemove = true;\r\n      this.engine.removeNode(n);\r\n    } else if (n._isAboutToRemove) {\r\n      // restore any temp removed (dragged over trash)\r\n      GridStack._itemRemoving(n.el, false);\r\n    }\r\n\r\n    this.engine.restoreInitial();\r\n  }\r\n\r\n  /** @internal removes any drag&drop present (called during destroy) */\r\n  protected _removeDD(el: DDElementHost): GridStack {\r\n    dd.draggable(el, 'destroy').resizable(el, 'destroy');\r\n    if (el.gridstackNode) {\r\n      delete el.gridstackNode._initDD; // reset our DD init flag\r\n    }\r\n    delete el.ddElement;\r\n    return this;\r\n  }\r\n\r\n  /** @internal called to add drag over to support widgets being added externally */\r\n  protected _setupAcceptWidget(): GridStack {\r\n\r\n    // check if we need to disable things\r\n    if (this.opts.staticGrid || (!this.opts.acceptWidgets && !this.opts.removable)) {\r\n      dd.droppable(this.el, 'destroy');\r\n      return this;\r\n    }\r\n\r\n    // vars shared across all methods\r\n    let cellHeight: number, cellWidth: number;\r\n\r\n    const onDrag = (event: DragEvent, el: GridItemHTMLElement, helper: GridItemHTMLElement) => {\r\n      helper = helper || el;\r\n      const node = helper.gridstackNode;\r\n      if (!node) return;\r\n\r\n      // if the element is being dragged from outside, scale it down to match the grid's scale\r\n      // and slightly adjust its position relative to the mouse\r\n      if (!node.grid?.el) {\r\n        // this scales the helper down\r\n        helper.style.transform = `scale(${1 / this.dragTransform.xScale},${1 / this.dragTransform.yScale})`;\r\n        // this makes it so that the helper is well positioned relative to the mouse after scaling\r\n        const helperRect = helper.getBoundingClientRect();\r\n        helper.style.left = helperRect.x + (this.dragTransform.xScale - 1) * (event.clientX - helperRect.x) / this.dragTransform.xScale + 'px';\r\n        helper.style.top = helperRect.y + (this.dragTransform.yScale - 1) * (event.clientY - helperRect.y) / this.dragTransform.yScale + 'px';\r\n        helper.style.transformOrigin = `0px 0px`\r\n      }\r\n\r\n      let { top, left } = helper.getBoundingClientRect();\r\n      const rect = this.el.getBoundingClientRect();\r\n      left -= rect.left;\r\n      top -= rect.top;\r\n      const ui: DDUIData = {\r\n        position: {\r\n          top: top * this.dragTransform.xScale,\r\n          left: left * this.dragTransform.yScale\r\n        }\r\n      };\r\n\r\n      if (node._temporaryRemoved) {\r\n        node.x = Math.max(0, Math.round(left / cellWidth));\r\n        node.y = Math.max(0, Math.round(top / cellHeight));\r\n        delete node.autoPosition;\r\n        this.engine.nodeBoundFix(node);\r\n\r\n        // don't accept *initial* location if doesn't fit #1419 (locked drop region, or can't grow), but maybe try if it will go somewhere\r\n        if (!this.engine.willItFit(node)) {\r\n          node.autoPosition = true; // ignore x,y and try for any slot...\r\n          if (!this.engine.willItFit(node)) {\r\n            dd.off(el, 'drag'); // stop calling us\r\n            return; // full grid or can't grow\r\n          }\r\n          if (node._willFitPos) {\r\n            // use the auto position instead #1687\r\n            Utils.copyPos(node, node._willFitPos);\r\n            delete node._willFitPos;\r\n          }\r\n        }\r\n\r\n        // re-use the existing node dragging method\r\n        this._onStartMoving(helper, event, ui, node, cellWidth, cellHeight);\r\n      } else {\r\n        // re-use the existing node dragging that does so much of the collision detection\r\n        this._dragOrResize(helper, event, ui, node, cellWidth, cellHeight);\r\n      }\r\n    }\r\n\r\n    dd.droppable(this.el, {\r\n      accept: (el: GridItemHTMLElement) => {\r\n        const node: GridStackNode = el.gridstackNode || this._readAttr(el, false);\r\n        // set accept drop to true on ourself (which we ignore) so we don't get \"can't drop\" icon in HTML5 mode while moving\r\n        if (node?.grid === this) return true;\r\n        if (!this.opts.acceptWidgets) return false;\r\n        // check for accept method or class matching\r\n        let canAccept = true;\r\n        if (typeof this.opts.acceptWidgets === 'function') {\r\n          canAccept = this.opts.acceptWidgets(el);\r\n        } else {\r\n          const selector = (this.opts.acceptWidgets === true ? '.grid-stack-item' : this.opts.acceptWidgets as string);\r\n          canAccept = el.matches(selector);\r\n        }\r\n        // finally check to make sure we actually have space left #1571 #2633\r\n        if (canAccept && node && this.opts.maxRow) {\r\n          const n = { w: node.w, h: node.h, minW: node.minW, minH: node.minH }; // only width/height matters and autoPosition\r\n          canAccept = this.engine.willItFit(n);\r\n        }\r\n        return canAccept;\r\n      }\r\n    })\r\n      /**\r\n       * entering our grid area\r\n       */\r\n      .on(this.el, 'dropover', (event: Event, el: GridItemHTMLElement, helper: GridItemHTMLElement) => {\r\n        // console.log(`over ${this.el.gridstack.opts.id} ${count++}`); // TEST\r\n        let node = helper?.gridstackNode || el.gridstackNode;\r\n        // ignore drop enter on ourself (unless we temporarily removed) which happens on a simple drag of our item\r\n        if (node?.grid === this && !node._temporaryRemoved) {\r\n          // delete node._added; // reset this to track placeholder again in case we were over other grid #1484 (dropout doesn't always clear)\r\n          return false; // prevent parent from receiving msg (which may be a grid as well)\r\n        }\r\n\r\n        // If sidebar item, restore the sidebar node size to ensure consistent behavior when dragging between grids\r\n        if (node?._sidebarOrig) {\r\n          node.w = node._sidebarOrig.w;\r\n          node.h = node._sidebarOrig.h;\r\n        }\r\n\r\n        // fix #1578 when dragging fast, we may not get a leave on the previous grid so force one now\r\n        if (node?.grid && node.grid !== this && !node._temporaryRemoved) {\r\n          // console.log('dropover without leave'); // TEST\r\n          const otherGrid = node.grid;\r\n          otherGrid._leave(el, helper);\r\n        }\r\n        helper = helper || el;\r\n\r\n        // cache cell dimensions (which don't change), position can animate if we removed an item in otherGrid that affects us...\r\n        cellWidth = this.cellWidth();\r\n        cellHeight = this.getCellHeight(true);\r\n\r\n        // sidebar items: load any element attributes if we don't have a node on first enter from the sidebar\r\n        if (!node) {\r\n          const attr = helper.getAttribute('data-gs-widget') || helper.getAttribute('gridstacknode'); // TBD: temp support for old V11.0.0 attribute\r\n          if (attr) {\r\n            try {\r\n              node = JSON.parse(attr);\r\n            } catch (error) {\r\n              console.error(\"Gridstack dropover: Bad JSON format: \", attr);\r\n            }\r\n            helper.removeAttribute('data-gs-widget');\r\n            helper.removeAttribute('gridstacknode');\r\n          }\r\n          if (!node) node = this._readAttr(helper); // used to pass false for #2354, but now we clone top level node\r\n          // On first grid enter from sidebar, set the initial sidebar item size properties for the node\r\n          node._sidebarOrig = { w: node.w, h: node.h }\r\n        }\r\n        if (!node.grid) { // sidebar item\r\n          if (!node.el) node = {...node}; // clone first time we're coming from sidebar (since 'clone' doesn't copy vars)\r\n          node._isExternal = true;\r\n          helper.gridstackNode = node;\r\n        }\r\n\r\n        // calculate the grid size based on element outer size\r\n        const w = node.w || Math.round(helper.offsetWidth / cellWidth) || 1;\r\n        const h = node.h || Math.round(helper.offsetHeight / cellHeight) || 1;\r\n\r\n        // if the item came from another grid, make a copy and save the original info in case we go back there\r\n        if (node.grid && node.grid !== this) {\r\n          // copy the node original values (min/max/id/etc...) but override width/height/other flags which are this grid specific\r\n          // console.log('dropover cloning node'); // TEST\r\n          if (!el._gridstackNodeOrig) el._gridstackNodeOrig = node; // shouldn't have multiple nested!\r\n          el.gridstackNode = node = { ...node, w, h, grid: this };\r\n          delete node.x;\r\n          delete node.y;\r\n          this.engine.cleanupNode(node)\r\n            .nodeBoundFix(node);\r\n          // restore some internal fields we need after clearing them all\r\n          node._initDD =\r\n            node._isExternal =  // DOM needs to be re-parented on a drop\r\n            node._temporaryRemoved = true; // so it can be inserted onDrag below\r\n        } else {\r\n          node.w = w;\r\n          node.h = h;\r\n          node._temporaryRemoved = true; // so we can insert it\r\n        }\r\n\r\n        // clear any marked for complete removal (Note: don't check _isAboutToRemove as that is cleared above - just do it)\r\n        GridStack._itemRemoving(node.el, false);\r\n\r\n        dd.on(el, 'drag', onDrag);\r\n        // make sure this is called at least once when going fast #1578\r\n        onDrag(event as DragEvent, el, helper);\r\n        return false; // prevent parent from receiving msg (which may be a grid as well)\r\n      })\r\n      /**\r\n       * Leaving our grid area...\r\n       */\r\n      .on(this.el, 'dropout', (event, el: GridItemHTMLElement, helper: GridItemHTMLElement) => {\r\n        // console.log(`out ${this.el.gridstack.opts.id} ${count++}`); // TEST\r\n        const node = helper?.gridstackNode || el.gridstackNode;\r\n        if (!node) return false;\r\n        // fix #1578 when dragging fast, we might get leave after other grid gets enter (which calls us to clean)\r\n        // so skip this one if we're not the active grid really..\r\n        if (!node.grid || node.grid === this) {\r\n          this._leave(el, helper);\r\n          // if we were created as temporary nested grid, go back to before state\r\n          if (this._isTemp) {\r\n            this.removeAsSubGrid(node);\r\n          }\r\n        }\r\n        return false; // prevent parent from receiving msg (which may be grid as well)\r\n      })\r\n      /**\r\n       * end - releasing the mouse\r\n       */\r\n      .on(this.el, 'drop', (event, el: GridItemHTMLElement, helper: GridItemHTMLElement) => {\r\n        const node = helper?.gridstackNode || el.gridstackNode;\r\n        // ignore drop on ourself from ourself that didn't come from the outside - dragend will handle the simple move instead\r\n        if (node?.grid === this && !node._isExternal) return false;\r\n\r\n        const wasAdded = !!this.placeholder.parentElement; // skip items not actually added to us because of constrains, but do cleanup #1419\r\n        const wasSidebar = el !== helper;\r\n        this.placeholder.remove();\r\n        delete this.placeholder.gridstackNode;\r\n\r\n        // disable animation when replacing a placeholder (already positioned) with actual content\r\n        if (wasAdded && this.opts.animate) {\r\n          this.setAnimation(false);\r\n          this.setAnimation(true, true); // delay adding back\r\n        }\r\n\r\n        // notify previous grid of removal\r\n        // console.log('drop delete _gridstackNodeOrig') // TEST\r\n        const origNode = el._gridstackNodeOrig;\r\n        delete el._gridstackNodeOrig;\r\n        if (wasAdded && origNode?.grid && origNode.grid !== this) {\r\n          const oGrid = origNode.grid;\r\n          oGrid.engine.removeNodeFromLayoutCache(origNode);\r\n          oGrid.engine.removedNodes.push(origNode);\r\n          oGrid._triggerRemoveEvent()._triggerChangeEvent();\r\n          // if it's an empty sub-grid that got auto-created, nuke it\r\n          if (oGrid.parentGridNode && !oGrid.engine.nodes.length && oGrid.opts.subGridDynamic) {\r\n            oGrid.removeAsSubGrid();\r\n          }\r\n        }\r\n\r\n        if (!node) return false;\r\n\r\n        // use existing placeholder node as it's already in our list with drop location\r\n        if (wasAdded) {\r\n          this.engine.cleanupNode(node); // removes all internal _xyz values\r\n          node.grid = this;\r\n        }\r\n        delete node.grid?._isTemp;\r\n        dd.off(el, 'drag');\r\n        // if we made a copy insert that instead of the original (sidebar item)\r\n        if (helper !== el) {\r\n          helper.remove();\r\n          el = helper;\r\n        } else {\r\n          el.remove(); // reduce flicker as we change depth here, and size further down\r\n        }\r\n        this._removeDD(el);\r\n        if (!wasAdded) return false;\r\n        const subGrid = node.subGrid?.el?.gridstack; // set when actual sub-grid present\r\n        Utils.copyPos(node, this._readAttr(this.placeholder)); // placeholder values as moving VERY fast can throw things off #1578\r\n        Utils.removePositioningStyles(el);\r\n\r\n        // give the user a chance to alter the widget that will get inserted if new sidebar item\r\n        if (wasSidebar && (node.content || node.subGridOpts || GridStack.addRemoveCB)) {\r\n          delete node.el;\r\n          el = this.addWidget(node);\r\n        } else {\r\n          this._prepareElement(el, true, node);\r\n          this.el.appendChild(el);\r\n          // resizeToContent is skipped in _prepareElement() until node is visible (clientHeight=0) so call it now\r\n          this.resizeToContentCheck(false, node);\r\n          if (subGrid) {\r\n            subGrid.parentGridNode = node;\r\n          }\r\n          this._updateContainerHeight();\r\n        }\r\n        this.engine.addedNodes.push(node);\r\n        this._triggerAddEvent();\r\n        this._triggerChangeEvent();\r\n\r\n        this.engine.endUpdate();\r\n        if (this._gsEventHandler['dropped']) {\r\n          this._gsEventHandler['dropped']({ ...event, type: 'dropped' }, origNode && origNode.grid ? origNode : undefined, node);\r\n        }\r\n\r\n        return false; // prevent parent from receiving msg (which may be grid as well)\r\n      });\r\n    return this;\r\n  }\r\n\r\n  /** @internal mark item for removal */\r\n  private static _itemRemoving(el: GridItemHTMLElement, remove: boolean) {\r\n    if (!el) return;\r\n    const node = el ? el.gridstackNode : undefined;\r\n    if (!node?.grid || el.classList.contains(node.grid.opts.removableOptions.decline)) return;\r\n    remove ? node._isAboutToRemove = true : delete node._isAboutToRemove;\r\n    remove ? el.classList.add('grid-stack-item-removing') : el.classList.remove('grid-stack-item-removing');\r\n  }\r\n\r\n  /** @internal called to setup a trash drop zone if the user specifies it */\r\n  protected _setupRemoveDrop(): GridStack {\r\n    if (typeof this.opts.removable !== 'string') return this;\r\n    const trashEl = document.querySelector(this.opts.removable) as HTMLElement;\r\n    if (!trashEl) return this;\r\n\r\n    // only register ONE static drop-over/dropout callback for the 'trash', and it will\r\n    // update the passed in item and parent grid because the '.trash' is a shared resource anyway,\r\n    // and Native DD only has 1 event CB (having a list and technically a per grid removableOptions complicates things greatly)\r\n    if (!this.opts.staticGrid && !dd.isDroppable(trashEl)) {\r\n      dd.droppable(trashEl, this.opts.removableOptions)\r\n        .on(trashEl, 'dropover', (event, el) => GridStack._itemRemoving(el, true))\r\n        .on(trashEl, 'dropout', (event, el) => GridStack._itemRemoving(el, false));\r\n    }\r\n    return this;\r\n  }\r\n\r\n  /**\r\n   * prepares the element for drag&drop - this is normally called by makeWidget() unless are are delay loading\r\n   * @param el GridItemHTMLElement of the widget\r\n   * @param [force=false]\r\n   * */\r\n  public prepareDragDrop(el: GridItemHTMLElement, force = false): GridStack {\r\n    const node = el?.gridstackNode;\r\n    if (!node) return;\r\n    const noMove = node.noMove || this.opts.disableDrag;\r\n    const noResize = node.noResize || this.opts.disableResize;\r\n\r\n    // check for disabled grid first\r\n    const disable = this.opts.staticGrid || (noMove && noResize);\r\n    if (force || disable) {\r\n      if (node._initDD) {\r\n        this._removeDD(el); // nukes everything instead of just disable, will add some styles back next\r\n        delete node._initDD;\r\n      }\r\n      if (disable) el.classList.add('ui-draggable-disabled', 'ui-resizable-disabled'); // add styles one might depend on #1435\r\n      if (!force) return this;\r\n    }\r\n\r\n    if (!node._initDD) {\r\n      // variables used/cashed between the 3 start/move/end methods, in addition to node passed above\r\n      let cellWidth: number;\r\n      let cellHeight: number;\r\n\r\n      /** called when item starts moving/resizing */\r\n      const onStartMoving = (event: Event, ui: DDUIData) => {\r\n        // trigger any 'dragstart' / 'resizestart' manually\r\n        this.triggerEvent(event, event.target as GridItemHTMLElement);\r\n        cellWidth = this.cellWidth();\r\n        cellHeight = this.getCellHeight(true); // force pixels for calculations\r\n\r\n        this._onStartMoving(el, event, ui, node, cellWidth, cellHeight);\r\n      }\r\n\r\n      /** called when item is being dragged/resized */\r\n      const dragOrResize = (event: MouseEvent, ui: DDUIData) => {\r\n        this._dragOrResize(el, event, ui, node, cellWidth, cellHeight);\r\n      }\r\n\r\n      /** called when the item stops moving/resizing */\r\n      const onEndMoving = (event: Event) => {\r\n        this.placeholder.remove();\r\n        delete this.placeholder.gridstackNode;\r\n        delete node._moving;\r\n        delete node._resizing;\r\n        delete node._event;\r\n        delete node._lastTried;\r\n        const widthChanged = node.w !== node._orig.w;\r\n\r\n        // if the item has moved to another grid, we're done here\r\n        const target: GridItemHTMLElement = event.target as GridItemHTMLElement;\r\n        if (!target.gridstackNode || target.gridstackNode.grid !== this) return;\r\n\r\n        node.el = target;\r\n\r\n        if (node._isAboutToRemove) {\r\n          const grid = el.gridstackNode.grid;\r\n          if (grid._gsEventHandler[event.type]) {\r\n            grid._gsEventHandler[event.type](event, target);\r\n          }\r\n          grid.engine.nodes.push(node); // temp add it back so we can proper remove it next\r\n          grid.removeWidget(el, true, true);\r\n        } else {\r\n          Utils.removePositioningStyles(target);\r\n          if (node._temporaryRemoved) {\r\n            // got removed - restore item back to before dragging position\r\n            Utils.copyPos(node, node._orig);// @ts-ignore\r\n            this._writePosAttr(target, node);\r\n            this.engine.addNode(node);\r\n          } else {\r\n            // move to new placeholder location\r\n            this._writePosAttr(target, node);\r\n          }\r\n          this.triggerEvent(event, target);\r\n        }\r\n        // @ts-ignore\r\n        this._extraDragRow = 0;// @ts-ignore\r\n        this._updateContainerHeight();// @ts-ignore\r\n        this._triggerChangeEvent();\r\n\r\n        this.engine.endUpdate();\r\n\r\n        if (event.type === 'resizestop') {\r\n          if (Number.isInteger(node.sizeToContent)) node.sizeToContent = node.h; // new soft limit\r\n          this.resizeToContentCheck(widthChanged, node); // wait for width animation if changed\r\n        }\r\n      }\r\n\r\n      dd.draggable(el, {\r\n        start: onStartMoving,\r\n        stop: onEndMoving,\r\n        drag: dragOrResize\r\n      }).resizable(el, {\r\n        start: onStartMoving,\r\n        stop: onEndMoving,\r\n        resize: dragOrResize\r\n      });\r\n      node._initDD = true; // we've set DD support now\r\n    }\r\n\r\n    // finally fine tune move vs resize by disabling any part...\r\n    dd.draggable(el, noMove ? 'disable' : 'enable')\r\n      .resizable(el, noResize ? 'disable' : 'enable');\r\n\r\n    return this;\r\n  }\r\n\r\n  /** @internal handles actual drag/resize start */\r\n  protected _onStartMoving(el: GridItemHTMLElement, event: Event, ui: DDUIData, node: GridStackNode, cellWidth: number, cellHeight: number): void {\r\n    this.engine.cleanNodes()\r\n      .beginUpdate(node);\r\n    // @ts-ignore\r\n    this._writePosAttr(this.placeholder, node)\r\n    this.el.appendChild(this.placeholder);\r\n    this.placeholder.gridstackNode = node;\r\n    // console.log('_onStartMoving placeholder') // TEST\r\n\r\n    // if the element is inside a grid, it has already been scaled\r\n    // we can use that as a scale reference\r\n    if (node.grid?.el) {\r\n      this.dragTransform = Utils.getValuesFromTransformedElement(el);\r\n    }\r\n    // if the element is being dragged from outside (not from any grid)\r\n    // we use the grid as the transformation reference, since the helper is not subject to transformation\r\n    else if (this.placeholder && this.placeholder.closest('.grid-stack')) {\r\n      const gridEl = this.placeholder.closest('.grid-stack') as HTMLElement;\r\n      this.dragTransform = Utils.getValuesFromTransformedElement(gridEl);\r\n    }\r\n    // Fallback\r\n    else {\r\n      this.dragTransform = {\r\n        xScale: 1,\r\n        xOffset: 0,\r\n        yScale: 1,\r\n        yOffset: 0,\r\n      }\r\n    }\r\n\r\n    node.el = this.placeholder;\r\n    node._lastUiPosition = ui.position;\r\n    node._prevYPix = ui.position.top;\r\n    node._moving = (event.type === 'dragstart'); // 'dropover' are not initially moving so they can go exactly where they enter (will push stuff out of the way)\r\n    node._resizing = (event.type === 'resizestart');\r\n    delete node._lastTried;\r\n\r\n    if (event.type === 'dropover' && node._temporaryRemoved) {\r\n      // console.log('engine.addNode x=' + node.x); // TEST\r\n      this.engine.addNode(node); // will add, fix collisions, update attr and clear _temporaryRemoved\r\n      node._moving = true; // AFTER, mark as moving object (wanted fix location before)\r\n    }\r\n\r\n    // set the min/max resize info taking into account the column count and position (so we don't resize outside the grid)\r\n    this.engine.cacheRects(cellWidth, cellHeight, this.opts.marginTop as number, this.opts.marginRight as number, this.opts.marginBottom as number, this.opts.marginLeft as number);\r\n    if (event.type === 'resizestart') {\r\n      const colLeft = this.getColumn() - node.x;\r\n      const rowLeft = (this.opts.maxRow || Number.MAX_SAFE_INTEGER) - node.y;\r\n      dd.resizable(el, 'option', 'minWidth', cellWidth * Math.min(node.minW || 1, colLeft))\r\n        .resizable(el, 'option', 'minHeight', cellHeight * Math.min(node.minH || 1, rowLeft))\r\n        .resizable(el, 'option', 'maxWidth', cellWidth * Math.min(node.maxW || Number.MAX_SAFE_INTEGER, colLeft))\r\n        .resizable(el, 'option', 'maxWidthMoveLeft', cellWidth * Math.min(node.maxW || Number.MAX_SAFE_INTEGER, node.x+node.w))\r\n        .resizable(el, 'option', 'maxHeight', cellHeight * Math.min(node.maxH || Number.MAX_SAFE_INTEGER, rowLeft))\r\n        .resizable(el, 'option', 'maxHeightMoveUp', cellHeight * Math.min(node.maxH || Number.MAX_SAFE_INTEGER, node.y+node.h));\r\n    }\r\n  }\r\n\r\n  /** @internal handles actual drag/resize */\r\n  protected _dragOrResize(el: GridItemHTMLElement, event: MouseEvent, ui: DDUIData, node: GridStackNode, cellWidth: number, cellHeight: number): void {\r\n    const p = { ...node._orig }; // could be undefined (_isExternal) which is ok (drag only set x,y and w,h will default to node value)\r\n    let resizing: boolean;\r\n    let mLeft = this.opts.marginLeft as number,\r\n      mRight = this.opts.marginRight as number,\r\n      mTop = this.opts.marginTop as number,\r\n      mBottom = this.opts.marginBottom as number;\r\n\r\n    // if margins (which are used to pass mid point by) are large relative to cell height/width, reduce them down #1855\r\n    const mHeight = Math.round(cellHeight * 0.1),\r\n      mWidth = Math.round(cellWidth * 0.1);\r\n    mLeft = Math.min(mLeft, mWidth);\r\n    mRight = Math.min(mRight, mWidth);\r\n    mTop = Math.min(mTop, mHeight);\r\n    mBottom = Math.min(mBottom, mHeight);\r\n\r\n    if (event.type === 'drag') {\r\n      if (node._temporaryRemoved) return; // handled by dropover\r\n      const distance = ui.position.top - node._prevYPix;\r\n      node._prevYPix = ui.position.top;\r\n      if (this.opts.draggable.scroll !== false) {\r\n        Utils.updateScrollPosition(el, ui.position, distance);\r\n      }\r\n\r\n      // get new position taking into account the margin in the direction we are moving! (need to pass mid point by margin)\r\n      const left = ui.position.left + (ui.position.left > node._lastUiPosition.left ? -mRight : mLeft);\r\n      const top = ui.position.top + (ui.position.top > node._lastUiPosition.top ? -mBottom : mTop);\r\n      p.x = Math.round(left / cellWidth);\r\n      p.y = Math.round(top / cellHeight);\r\n\r\n      // @ts-ignore// if we're at the bottom hitting something else, grow the grid so cursor doesn't leave when trying to place below others\r\n      const prev = this._extraDragRow;\r\n      if (this.engine.collide(node, p)) {\r\n        const row = this.getRow();\r\n        let extra = Math.max(0, (p.y + node.h) - row);\r\n        if (this.opts.maxRow && row + extra > this.opts.maxRow) {\r\n          extra = Math.max(0, this.opts.maxRow - row);\r\n        }// @ts-ignore\r\n        this._extraDragRow = extra;// @ts-ignore\r\n      } else this._extraDragRow = 0;// @ts-ignore\r\n      if (this._extraDragRow !== prev) this._updateContainerHeight();\r\n\r\n      if (node.x === p.x && node.y === p.y) return; // skip same\r\n      // DON'T skip one we tried as we might have failed because of coverage <50% before\r\n      // if (node._lastTried && node._lastTried.x === x && node._lastTried.y === y) return;\r\n    } else if (event.type === 'resize') {\r\n      if (p.x < 0) return;\r\n      // Scrolling page if needed\r\n      Utils.updateScrollResize(event, el, cellHeight);\r\n\r\n      // get new size\r\n      p.w = Math.round((ui.size.width - mLeft) / cellWidth);\r\n      p.h = Math.round((ui.size.height - mTop) / cellHeight);\r\n      if (node.w === p.w && node.h === p.h) return;\r\n      if (node._lastTried && node._lastTried.w === p.w && node._lastTried.h === p.h) return; // skip one we tried (but failed)\r\n\r\n      // if we size on left/top side this might move us, so get possible new position as well\r\n      const left = ui.position.left + mLeft;\r\n      const top = ui.position.top + mTop;\r\n      p.x = Math.round(left / cellWidth);\r\n      p.y = Math.round(top / cellHeight);\r\n\r\n      resizing = true;\r\n    }\r\n\r\n    node._event = event;\r\n    node._lastTried = p; // set as last tried (will nuke if we go there)\r\n    const rect: GridStackPosition = { // screen pix of the dragged box\r\n      x: ui.position.left + mLeft,\r\n      y: ui.position.top + mTop,\r\n      w: (ui.size ? ui.size.width : node.w * cellWidth) - mLeft - mRight,\r\n      h: (ui.size ? ui.size.height : node.h * cellHeight) - mTop - mBottom\r\n    };\r\n    if (this.engine.moveNodeCheck(node, { ...p, cellWidth, cellHeight, rect, resizing })) {\r\n      node._lastUiPosition = ui.position;\r\n      this.engine.cacheRects(cellWidth, cellHeight, mTop, mRight, mBottom, mLeft);\r\n      delete node._skipDown;\r\n      if (resizing && node.subGrid) node.subGrid.onResize();\r\n      this._extraDragRow = 0;// @ts-ignore\r\n      this._updateContainerHeight();\r\n\r\n      const target = event.target as GridItemHTMLElement;// @ts-ignore\r\n      // Do not write sidebar item attributes back to the original sidebar el\r\n      if (!node._sidebarOrig) {\r\n        this._writePosAttr(target, node);\r\n      }\r\n      this.triggerEvent(event, target);\r\n    }\r\n  }\r\n\r\n  /** call given event callback on our main top-most grid (if we're nested) */\r\n  protected triggerEvent(event: Event, target: GridItemHTMLElement) {\r\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\r\n    let grid: GridStack = this;\r\n    while (grid.parentGridNode) grid = grid.parentGridNode.grid;\r\n    if (grid._gsEventHandler[event.type]) {\r\n      grid._gsEventHandler[event.type](event, target);\r\n    }\r\n  }\r\n\r\n  /** @internal called when item leaving our area by either cursor dropout event\r\n   * or shape is outside our boundaries. remove it from us, and mark temporary if this was\r\n   * our item to start with else restore prev node values from prev grid it came from.\r\n   */\r\n  protected _leave(el: GridItemHTMLElement, helper?: GridItemHTMLElement): void {\r\n    helper = helper || el;\r\n    const node = helper.gridstackNode;\r\n    if (!node) return;\r\n\r\n    // remove the scale of the helper on leave\r\n    helper.style.transform = helper.style.transformOrigin = null;\r\n    dd.off(el, 'drag'); // no need to track while being outside\r\n\r\n    // this gets called when cursor leaves and shape is outside, so only do this once\r\n    if (node._temporaryRemoved) return;\r\n    node._temporaryRemoved = true;\r\n\r\n    this.engine.removeNode(node); // remove placeholder as well, otherwise it's a sign node is not in our list, which is a bigger issue\r\n    node.el = node._isExternal && helper ? helper : el; // point back to real item being dragged\r\n    const sidebarOrig = node._sidebarOrig;\r\n    if (node._isExternal) this.engine.cleanupNode(node);\r\n    // Restore sidebar item initial size info to stay consistent when dragging between multiple grids\r\n    node._sidebarOrig = sidebarOrig;\r\n\r\n    if (this.opts.removable === true) { // boolean vs a class string\r\n      // item leaving us and we are supposed to remove on leave (no need to drag onto trash) mark it so\r\n      GridStack._itemRemoving(el, true);\r\n    }\r\n\r\n    // finally if item originally came from another grid, but left us, restore things back to prev info\r\n    if (el._gridstackNodeOrig) {\r\n      // console.log('leave delete _gridstackNodeOrig') // TEST\r\n      el.gridstackNode = el._gridstackNodeOrig;\r\n      delete el._gridstackNodeOrig;\r\n    } else if (node._isExternal) {\r\n      // item came from outside restore all nodes back to original\r\n      this.engine.restoreInitial();\r\n    }\r\n  }\r\n\r\n  // legacy method removed\r\n  public commit(): GridStack { obsolete(this, this.batchUpdate(false), 'commit', 'batchUpdate', '5.2'); return this; }\r\n}\r\n"], "mappings": ";;;AAqBM,SAAU,SAAS,MAAM,GAAG,SAAiB,SAAiB,KAAW;AAC7E,QAAM,UAAU,IAAI,SAAQ;AAC1B,YAAQ,KAAK,6BAA6B,UAAU,wBAAwB,MAAM,kCACvE,UAAU,+CAA+C;AACpE,WAAO,EAAE,MAAM,MAAM,IAAI;EAC3B;AACA,UAAQ,YAAY,EAAE;AACtB,SAAO;AACT;AAGM,SAAU,aAAa,MAAwB,SAAiB,SAAiB,KAAW;AAChG,MAAI,KAAK,OAAO,MAAM,QAAW;AAC/B,SAAK,OAAO,IAAI,KAAK,OAAO;AAC5B,YAAQ,KAAK,2BAA2B,UAAU,wBAAwB,MAAM,kCAC9E,UAAU,+CAA+C;;AAE/D;AAGM,SAAU,gBAAgB,MAAwB,SAAiB,KAAa,MAAY;AAChG,MAAI,KAAK,OAAO,MAAM,QAAW;AAC/B,YAAQ,KAAK,2BAA2B,UAAU,wBAAwB,MAAM,IAAI;;AAExF;AAGM,SAAU,aAAa,IAAiB,SAAiB,SAAiB,KAAW;AACzF,QAAM,UAAU,GAAG,aAAa,OAAO;AACvC,MAAI,YAAY,MAAM;AACpB,OAAG,aAAa,SAAS,OAAO;AAChC,YAAQ,KAAK,8BAA8B,UAAU,OAAO,UAAU,sCAAsC,MAAM,kCAChH,UAAU,+CAA+C;;AAE/D;AAKM,IAAO,QAAP,MAAO,OAAK;;EAGhB,OAAO,YAAY,KAAuB,OAA+B,UAAQ;AAC/E,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,MAAO,oBAAoB,OAAQ,OAAmB;AAK5D,UAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG;AAC1B,cAAM,KAAK,IAAI,eAAe,GAAG;AACjC,eAAO,KAAK,CAAC,EAAE,IAAI,CAAA;;AAGrB,UAAI,OAAO,KAAK,iBAAiB,GAAG;AACpC,UAAI,CAAC,KAAK,UAAU,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK;AACpD,eAAO,KAAK,iBAAiB,MAAM,GAAG;AACtC,YAAI,CAAC,KAAK,QAAQ;AAAE,iBAAO,KAAK,iBAAiB,MAAM,GAAG;;;AAE5D,aAAO,MAAM,KAAK,IAAI;;AAExB,WAAO,CAAC,GAAG;EACb;;EAGA,OAAO,WAAW,KAAuB,OAA+B,UAAQ;AAC9E,QAAI,OAAO,QAAQ,UAAU;AAC3B,YAAM,MAAO,oBAAoB,OAAQ,OAAmB;AAC5D,UAAI,CAAC,IAAI;AAAQ,eAAO;AACxB,UAAI,OAAO,IAAI,CAAC,MAAM,KAAK;AACzB,eAAO,IAAI,eAAe,IAAI,UAAU,CAAC,CAAC;;AAE5C,UAAI,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,IAAI,CAAC,MAAM,KAAK;AACtD,eAAO,KAAK,cAAc,GAAG;;AAI/B,UAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG;AAC1B,eAAO,IAAI,eAAe,GAAG;;AAI/B,UAAI,KAAK,KAAK,cAAc,GAAG;AAC/B,UAAI,OAAO,CAAC,IAAI;AAAE,aAAK,IAAI,eAAe,GAAG;;AAC7C,UAAI,CAAC,IAAI;AAAE,aAAK,KAAK,cAAc,MAAM,GAAG;;AAC5C,aAAO;;AAET,WAAO;EACT;;EAGA,OAAO,SAAS,GAAgB;AAC9B,WAAO,EAAE,YAAY,EAAE,MAAM,MAAM,YAAY,EAAE,aAAa;EAChE;;EAGA,OAAO,UAAU,SAAmB,QAAoB;AACtD,UAAM,KAAK,SAAS,cAAc,KAAK;AACvC,YAAQ,QAAQ,OAAI;AAAE,UAAI;AAAG,WAAG,UAAU,IAAI,CAAC;IAAC,CAAC;AACjD,YAAQ,YAAY,EAAE;AACtB,WAAO;EACT;;EAGA,OAAO,oBAAoB,GAA8B,SAAS,OAAK;AACrE,WAAO,GAAG,SAAS,SAChB,EAAE,kBAAkB,QAAS,EAAE,KAAK,KAAK,kBAAkB,QAAQ,EAAE,kBAAkB,SACvF,CAAC,CAAC,EAAE,iBAAkB,EAAE,KAAK,KAAK,iBAAiB,EAAE,kBAAkB;EAC5E;;EAGA,OAAO,cAAc,GAAsB,GAAoB;AAC7D,WAAO,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;EACtF;;EAGA,OAAO,WAAW,GAAsB,GAAoB;AAC1D,WAAO,OAAM,cAAc,GAAG,EAAC,GAAG,EAAE,IAAE,KAAK,GAAG,EAAE,IAAE,KAAK,GAAG,EAAE,IAAE,GAAG,GAAG,EAAE,IAAE,EAAC,CAAC;EAC5E;;EAGA,OAAO,cAAc,GAAsB,GAAoB;AAC7D,UAAM,KAAM,EAAE,IAAI,EAAE,IAAK,EAAE,IAAI,EAAE;AACjC,UAAM,KAAM,EAAE,IAAE,EAAE,IAAI,EAAE,IAAE,EAAE,IAAK,EAAE,IAAE,EAAE,IAAI,EAAE,IAAE,EAAE;AACjD,QAAI,MAAM;AAAI,aAAO;AACrB,UAAM,KAAM,EAAE,IAAI,EAAE,IAAK,EAAE,IAAI,EAAE;AACjC,UAAM,KAAM,EAAE,IAAE,EAAE,IAAI,EAAE,IAAE,EAAE,IAAK,EAAE,IAAE,EAAE,IAAI,EAAE,IAAE,EAAE;AACjD,QAAI,MAAM;AAAI,aAAO;AACrB,YAAQ,KAAG,OAAO,KAAG;EACvB;;EAGA,OAAO,KAAK,GAAoB;AAC9B,WAAO,EAAE,IAAI,EAAE;EACjB;;;;;;EAOA,OAAO,KAAK,OAAwB,MAAc,GAAC;AACjD,UAAM,MAAM;AACZ,WAAO,MAAM,KAAK,CAAC,GAAG,MAAK;AACzB,YAAM,QAAQ,QAAQ,EAAE,KAAK,QAAQ,EAAE,KAAK;AAC5C,UAAI,UAAU;AAAG,eAAO,QAAQ,EAAE,KAAK,QAAQ,EAAE,KAAK;AACtD,aAAO;IACT,CAAC;EACH;;EAGA,OAAO,KAAK,OAAwB,IAAU;AAC5C,WAAO,KAAK,MAAM,KAAK,OAAK,EAAE,OAAO,EAAE,IAAI;EAC7C;;EAGA,OAAO,OAAO,GAAU;AACtB,QAAI,OAAO,MAAM,WAAW;AAC1B,aAAO;;AAET,QAAI,OAAO,MAAM,UAAU;AACzB,UAAI,EAAE,YAAW;AACjB,aAAO,EAAE,MAAM,MAAM,MAAM,QAAQ,MAAM,WAAW,MAAM;;AAE5D,WAAO,QAAQ,CAAC;EAClB;EAEA,OAAO,SAAS,OAAoB;AAClC,WAAQ,UAAU,QAAQ,MAAM,WAAW,IAAK,SAAY,OAAO,KAAK;EAC1E;EAEA,OAAO,YAAY,KAAmB;AACpC,QAAI;AACJ,QAAI,OAAO;AACX,QAAI,OAAO,QAAQ,UAAU;AAC3B,UAAI,QAAQ,UAAU,QAAQ;AAAI,YAAI;WACjC;AACH,cAAM,QAAQ,IAAI,MAAM,6EAA6E;AACrG,YAAI,CAAC,OAAO;AACV,gBAAM,IAAI,MAAM,wBAAwB,GAAG,EAAE;;AAE/C,eAAO,MAAM,CAAC,KAAK;AACnB,YAAI,WAAW,MAAM,CAAC,CAAC;;WAEpB;AACL,UAAI;;AAEN,WAAO,EAAE,GAAG,KAAI;EAClB;;;EAIA,OAAO,SAAS,WAAW,SAAO;AAEhC,YAAQ,QAAQ,YAAS;AACvB,iBAAW,OAAO,QAAQ;AACxB,YAAI,CAAC,OAAO,eAAe,GAAG;AAAG;AACjC,YAAI,OAAO,GAAG,MAAM,QAAQ,OAAO,GAAG,MAAM,QAAW;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;mBACf,OAAO,OAAO,GAAG,MAAM,YAAY,OAAO,OAAO,GAAG,MAAM,UAAU;AAE7E,eAAK,SAAS,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;;;IAG5C,CAAC;AAED,WAAO;EACT;;EAGA,OAAO,KAAK,GAAY,GAAU;AAChC,QAAI,OAAO,MAAM;AAAW,aAAO,KAAK;AACxC,QAAI,OAAO,MAAM,OAAO;AAAG,aAAO;AAElC,QAAI,OAAO,KAAK,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE;AAAQ,aAAO;AAC5D,eAAW,OAAO,GAAG;AACnB,UAAI,EAAE,GAAG,MAAM,EAAE,GAAG;AAAG,eAAO;;AAEhC,WAAO;EACT;;EAGA,OAAO,QAAQ,GAAoB,GAAoB,WAAW,OAAK;AACrE,QAAI,EAAE,MAAM;AAAW,QAAE,IAAI,EAAE;AAC/B,QAAI,EAAE,MAAM;AAAW,QAAE,IAAI,EAAE;AAC/B,QAAI,EAAE,MAAM;AAAW,QAAE,IAAI,EAAE;AAC/B,QAAI,EAAE,MAAM;AAAW,QAAE,IAAI,EAAE;AAC/B,QAAI,UAAU;AACZ,UAAI,EAAE;AAAM,UAAE,OAAO,EAAE;AACvB,UAAI,EAAE;AAAM,UAAE,OAAO,EAAE;AACvB,UAAI,EAAE;AAAM,UAAE,OAAO,EAAE;AACvB,UAAI,EAAE;AAAM,UAAE,OAAO,EAAE;;AAEzB,WAAO;EACT;;EAGA,OAAO,QAAQ,GAAsB,GAAoB;AACvD,WAAO,KAAK,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,QAAQ,EAAE,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,KAAK;EACrG;;EAGA,OAAO,eAAe,MAAmB;AAEvC,QAAI,CAAC,KAAK,MAAM;AAAE,aAAO,KAAK;;AAC9B,QAAI,CAAC,KAAK,MAAM;AAAE,aAAO,KAAK;;AAC9B,QAAI,CAAC,KAAK,MAAM;AAAE,aAAO,KAAK;;AAC9B,QAAI,CAAC,KAAK,MAAM;AAAE,aAAO,KAAK;;EAChC;;EAGA,OAAO,sBAAsB,GAAY,GAAU;AACjD,QAAI,OAAO,MAAM,YAAY,OAAO,MAAM;AAAU;AACpD,aAAS,OAAO,GAAG;AACjB,YAAM,OAAO,EAAE,GAAG;AAClB,YAAM,OAAO,EAAE,GAAG;AAClB,UAAI,IAAI,CAAC,MAAM,OAAO,SAAS,MAAM;AACnC,eAAO,EAAE,GAAG;iBACH,QAAQ,OAAO,SAAS,YAAY,SAAS,QAAW;AACjE,eAAM,sBAAsB,MAAM,IAAI;AACtC,YAAI,CAAC,OAAO,KAAK,IAAI,EAAE,QAAQ;AAAE,iBAAO,EAAE,GAAG;;;;EAGnD;;EAGA,OAAO,sBAAsB,GAAkB,WAAW,MAAI;AAC5D,aAAS,OAAO,GAAG;AAAE,UAAI,IAAI,CAAC,MAAM,OAAO,EAAE,GAAG,MAAM,QAAQ,EAAE,GAAG,MAAM;AAAY,eAAO,EAAE,GAAG;;AACjG,WAAO,EAAE;AACT,QAAI;AAAU,aAAO,EAAE;AAEvB,QAAI,CAAC,EAAE;AAAc,aAAO,EAAE;AAC9B,QAAI,CAAC,EAAE;AAAU,aAAO,EAAE;AAC1B,QAAI,CAAC,EAAE;AAAQ,aAAO,EAAE;AACxB,QAAI,CAAC,EAAE;AAAQ,aAAO,EAAE;AACxB,QAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;AAAM,aAAO,EAAE;AAC1C,QAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;AAAM,aAAO,EAAE;EAC5C;;;;;;;;;;EAYA,OAAO,SAAS,MAAkB,OAAa;AAC7C,QAAI,YAAY;AAChB,WAAO,IAAI,SAAQ;AACjB,UAAI,CAAC,WAAW;AACd,oBAAY;AACZ,mBAAW,MAAK;AAAG,eAAK,GAAG,IAAI;AAAG,sBAAY;QAAO,GAAG,KAAK;;IAEjE;EACF;EAEA,OAAO,wBAAwB,IAAe;AAC5C,UAAM,QAAQ,GAAG;AACjB,QAAI,MAAM,UAAU;AAClB,YAAM,eAAe,UAAU;;AAEjC,QAAI,MAAM,MAAM;AACd,YAAM,eAAe,MAAM;;AAE7B,QAAI,MAAM,KAAK;AACb,YAAM,eAAe,KAAK;;AAE5B,QAAI,MAAM,OAAO;AACf,YAAM,eAAe,OAAO;;AAE9B,QAAI,MAAM,QAAQ;AAChB,YAAM,eAAe,QAAQ;;EAEjC;;EAGA,OAAO,iBAAiB,IAAgB;AACtC,QAAI,CAAC;AAAI,aAAO,SAAS,oBAAmC,SAAS;AACrE,UAAM,QAAQ,iBAAiB,EAAE;AACjC,UAAM,gBAAgB;AAEtB,QAAI,cAAc,KAAK,MAAM,WAAW,MAAM,SAAS,GAAG;AACxD,aAAO;WACF;AACL,aAAO,KAAK,iBAAiB,GAAG,aAAa;;EAEjD;;EAGA,OAAO,qBAAqB,IAAiB,UAAyB,UAAgB;AAEpF,UAAM,OAAO,GAAG,sBAAqB;AACrC,UAAM,4BAA6B,OAAO,eAAe,SAAS,gBAAgB;AAClF,QAAI,KAAK,MAAM,KACb,KAAK,SAAS,2BACd;AAIA,YAAM,iBAAiB,KAAK,SAAS;AACrC,YAAM,eAAe,KAAK;AAC1B,YAAM,WAAW,KAAK,iBAAiB,EAAE;AACzC,UAAI,aAAa,MAAM;AACrB,cAAM,aAAa,SAAS;AAC5B,YAAI,KAAK,MAAM,KAAK,WAAW,GAAG;AAEhC,cAAI,GAAG,eAAe,2BAA2B;AAC/C,qBAAS,aAAa;iBACjB;AACL,qBAAS,aAAa,KAAK,IAAI,YAAY,IAAI,KAAK,IAAI,QAAQ,IAAI,WAAW;;mBAExE,WAAW,GAAG;AAEvB,cAAI,GAAG,eAAe,2BAA2B;AAC/C,qBAAS,aAAa;iBACjB;AACL,qBAAS,aAAa,iBAAiB,WAAW,WAAW;;;AAIjE,iBAAS,OAAO,SAAS,YAAY;;;EAG3C;;;;;;;;EASA,OAAO,mBAAmB,OAAmB,IAAiB,UAAgB;AAC5E,UAAM,WAAW,KAAK,iBAAiB,EAAE;AACzC,UAAM,SAAS,SAAS;AAKxB,UAAM,YAAa,aAAa,KAAK,iBAAgB,IAAM,IAAI,SAAS,sBAAqB,EAAG;AAChG,UAAM,cAAc,MAAM,UAAU;AACpC,UAAM,MAAM,cAAc;AAC1B,UAAM,SAAS,cAAc,SAAS;AAEtC,QAAI,KAAK;AAGP,eAAS,SAAS,EAAE,UAAU,UAAU,KAAK,cAAc,SAAQ,CAAC;eAC3D,QAAQ;AACjB,eAAS,SAAS,EAAE,UAAU,UAAU,KAAK,YAAY,SAAS,aAAY,CAAC;;EAEnF;;EAGA,OAAO,MAAS,KAAM;AACpB,QAAI,QAAQ,QAAQ,QAAQ,UAAa,OAAO,QAAS,UAAU;AACjE,aAAO;;AAGT,QAAI,eAAe,OAAO;AAExB,aAAO,CAAC,GAAG,GAAG;;AAEhB,WAAO,EAAC,GAAG,IAAG;EAChB;;;;;EAMA,OAAO,UAAa,KAAM;AAExB,UAAM,aAAa,CAAC,cAAc,MAAM,QAAQ,WAAW,QAAQ;AAEnE,UAAM,MAAM,OAAM,MAAM,GAAG;AAC3B,eAAW,OAAO,KAAK;AAErB,UAAI,IAAI,eAAe,GAAG,KAAK,OAAO,IAAI,GAAG,MAAO,YAAY,IAAI,UAAU,GAAG,CAAC,MAAM,QAAQ,CAAC,WAAW,KAAK,OAAK,MAAM,GAAG,GAAG;AAChI,YAAI,GAAG,IAAI,OAAM,UAAU,IAAI,GAAG,CAAC;;;AAGvC,WAAO;EACT;;EAGO,OAAO,UAAU,IAAe;AACrC,UAAM,OAAO,GAAG,UAAU,IAAI;AAC9B,SAAK,gBAAgB,IAAI;AACzB,WAAO;EACT;EAEO,OAAO,SAAS,IAAiB,QAA4B;AAClE,QAAI;AACJ,QAAI,OAAO,WAAW,UAAU;AAC9B,mBAAa,OAAM,WAAW,MAAM;WAC/B;AACL,mBAAa;;AAEf,QAAI,YAAY;AACd,iBAAW,YAAY,EAAE;;EAE7B;;;;;;EAQO,OAAO,YAAY,IAAiB,QAA6C;AACtF,QAAI,kBAAkB,QAAQ;AAC5B,iBAAW,KAAK,QAAQ;AACtB,YAAI,OAAO,eAAe,CAAC,GAAG;AAC5B,cAAI,MAAM,QAAQ,OAAO,CAAC,CAAC,GAAG;AAE3B,mBAAO,CAAC,EAAe,QAAQ,SAAM;AACpC,iBAAG,MAAM,CAAC,IAAI;YAChB,CAAC;iBACI;AACL,eAAG,MAAM,CAAC,IAAI,OAAO,CAAC;;;;;EAKhC;EAEO,OAAO,UAAa,GAA2B,MAA4C;AAChG,UAAM,MAAM,EAAE,MAAM,KAAK,KAAI;AAC7B,UAAM,MAAM;MACV,QAAQ;MACR,OAAO;MACP,SAAS;MACT,SAAS;MACT,YAAY;MACZ,QAAQ,KAAK,SAAS,KAAK,SAAS,EAAE;;AAExC,KAAC,UAAS,WAAU,WAAU,UAAU,EAAE,QAAQ,OAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACpE,KAAC,SAAQ,SAAQ,WAAU,WAAU,WAAU,SAAS,EAAE,QAAQ,OAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AACpF,WAAO,EAAC,GAAG,KAAK,GAAG,IAAG;EACxB;;EAGO,OAAO,mBAAmB,GAAuB,eAAuB,QAAoB;AACjG,UAAM,KAAK;AACX,UAAM,iBAAiB,IAAI,WAAW,eAAe;MACnD,SAAS;MACT,UAAU;MACV,YAAY;MACZ,MAAM;MACN,QAAQ;MACR,SAAS,EAAE;MACX,SAAS,EAAE;MACX,SAAS,EAAE;MACX,SAAS,EAAE;MACX,SAAS,GAAG,WAAS;MACrB,QAAQ,GAAG,UAAQ;MACnB,UAAU,GAAG,YAAU;MACvB,SAAS,GAAG,WAAS;MACrB,QAAQ;MACR,eAAe,EAAE;KAClB;AAED,KAAC,UAAU,EAAE,QAAQ,cAAc,cAAc;EACnD;;;;;EAMO,OAAO,gCAAgC,QAAmB;AAC/D,UAAM,qBAAqB,SAAS,cAAc,KAAK;AACvD,WAAM,YAAY,oBAAoB;MACpC,SAAS;MACT,UAAU;MACV,KAAK;MACL,MAAM;MACN,OAAO;MACP,QAAQ;MACR,QAAQ;KACT;AACD,WAAO,YAAY,kBAAkB;AACrC,UAAM,kBAAkB,mBAAmB,sBAAqB;AAChE,WAAO,YAAY,kBAAkB;AACrC,uBAAmB,OAAM;AACzB,WAAO;MACL,QAAQ,IAAI,gBAAgB;MAC5B,QAAQ,IAAI,gBAAgB;MAC5B,SAAS,gBAAgB;MACzB,SAAS,gBAAgB;;EAE7B;;EAGO,OAAO,KAAK,GAAY,GAAW,GAAS;AACjD,QAAI,CAAC;AAAG;AACR,UAAM,MAAM,EAAE,CAAC;AAAG,MAAE,CAAC,IAAI,EAAE,CAAC;AAAG,MAAE,CAAC,IAAI;EACxC;;;;;;;;;;;;;;EAgBO,OAAO,aAAa,GAAgB;AACzC,WAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM,KAAK,iBAAkB,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAU,EAAE,QAAQ,EAAE,SAAS,EAAE;EACjJ;;;;ACxiBF,IAAa,kBAAb,MAAa,iBAAe;EA2B1B,YAAmB,OAA+B,CAAA,GAAE;AAvB7C,SAAA,aAA8B,CAAA;AAC9B,SAAA,eAAgC,CAAA;AAEhC,SAAA,gBAAgB;AAqBrB,SAAK,SAAS,KAAK,UAAU,KAAK;AAClC,QAAI,KAAK,SAAS,KAAK;AAAe,WAAK,gBAAgB,KAAK;AAChE,SAAK,SAAS,KAAK;AACnB,SAAK,SAAS,KAAK;AACnB,SAAK,QAAQ,KAAK,SAAS,CAAA;AAC3B,SAAK,WAAW,KAAK;EACvB;EAEO,YAAY,OAAO,MAAM,SAAS,MAAI;AAC3C,QAAI,CAAC,CAAC,KAAK,cAAc;AAAM,aAAO;AACtC,SAAK,YAAY;AACjB,QAAI,MAAM;AACR,WAAK,aAAa,KAAK;AACvB,WAAK,SAAS;AACd,WAAK,WAAU;AACf,WAAK,YAAW;WACX;AACL,WAAK,SAAS,KAAK;AACnB,aAAO,KAAK;AACZ,UAAI;AAAQ,aAAK,WAAU;AAC3B,WAAK,QAAO;;AAEd,WAAO;EACT;;EAGU,kBAAkB,MAAqB,IAAqB;AACpE,YAAQ,CAAC,KAAK,SAAS,KAAK,aAAa,CAAC,KAAK,eAAe,CAAC,KAAK,eAAe,CAAC,KAAK,WAAW,KAAK,aAAa,GAAG,KAAK,KAAK;EACrI;;;EAIU,eAAe,MAAqB,KAAK,MAAM,SAAyB,MAAyB,CAAA,GAAE;AAC3G,SAAK,UAAU,EAAE;AAEjB,cAAU,WAAW,KAAK,QAAQ,MAAM,EAAE;AAC1C,QAAI,CAAC;AAAS,aAAO;AAGrB,QAAI,KAAK,WAAW,CAAC,IAAI,UAAU,CAAC,KAAK,OAAO;AAC9C,UAAI,KAAK,KAAK,MAAM,OAAO;AAAG,eAAO;;AAIvC,QAAI,OAAO;AACX,QAAI,CAAC,KAAK,YAAY,KAAK,kBAAkB,MAAM,EAAE,GAAG;AACtD,aAAO,EAAC,GAAG,GAAG,GAAG,KAAK,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC;AAC9C,gBAAU,KAAK,QAAQ,MAAM,MAAM,IAAI,IAAI;;AAG7C,QAAI,UAAU;AACd,UAAM,SAA4B,EAAC,QAAQ,MAAM,MAAM,MAAK;AAC5D,QAAI,UAAU;AACd,WAAO,UAAU,WAAW,KAAK,QAAQ,MAAM,MAAM,IAAI,IAAI,GAAG;AAC9D,UAAI,YAAY,KAAK,MAAM,SAAS,GAAG;AACrC,cAAM,IAAI,MAAM,wBAAwB;;AAE1C,UAAI;AAGJ,UAAI,QAAQ,UAAU,KAAK,YAAY,KAAK,WAAW,CAAC,KAAK,aAAa,GAAG,IAAI,KAAK,KAAK,CAAC,KAAK;OAE9F,CAAC,KAAK,QAAQ,SAAS,EAAC,GAAG,SAAS,GAAG,KAAK,EAAC,GAAG,IAAI,KAAK,CAAC,KAAK,QAAQ,SAAS,EAAC,GAAG,SAAS,GAAG,GAAG,IAAI,QAAQ,EAAC,GAAG,IAAI,IAAI;AAE5H,aAAK,YAAa,KAAK,aAAa,GAAG,IAAI,KAAK;AAChD,cAAM,QAAQ,EAAC,GAAG,IAAI,GAAG,QAAQ,IAAI,QAAQ,GAAG,GAAG,OAAM;AAEzD,gBAAQ,KAAK,YAAY,MAAM,QAAQ,MAAM,KAAK,IAAI,OAAO,KAAK,SAAS,MAAM,KAAK;AAEtF,aAAK,QAAQ,UAAU,KAAK,aAAa,OAAO;AAC9C,gBAAM,QAAQ,IAAI,IAAI;mBACb,CAAC,QAAQ,UAAU,SAAS,IAAI,MAAM;AAE/C,eAAK,WAAU;AACf,aAAG,IAAI,QAAQ,IAAI,QAAQ;AAC3B,gBAAM,QAAQ,MAAM,EAAE;;AAExB,kBAAU,WAAW;aAChB;AAEL,gBAAQ,KAAK,SAAS,SAAS,EAAC,GAAG,SAAS,GAAG,GAAG,IAAI,GAAG,GAAG,MAAM,MAAM,GAAG,OAAM,CAAC;;AAGpF,UAAI,CAAC;AAAO,eAAO;AAEnB,gBAAU;;AAEZ,WAAO;EACT;;EAGO,QAAQ,MAAqB,OAAO,MAAM,OAAqB;AACpE,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU,OAAO;AACvB,WAAO,KAAK,MAAM,KAAK,OAAK,EAAE,QAAQ,UAAU,EAAE,QAAQ,WAAW,MAAM,cAAc,GAAG,IAAI,CAAC;EACnG;EACO,WAAW,MAAqB,OAAO,MAAM,OAAqB;AACvE,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU,OAAO;AACvB,WAAO,KAAK,MAAM,OAAO,OAAK,EAAE,QAAQ,UAAU,EAAE,QAAQ,WAAW,MAAM,cAAc,GAAG,IAAI,CAAC;EACrG;;EAGU,yBAAyB,MAAqB,GAAsB,UAAyB;AACrG,QAAI,CAAC,EAAE,QAAQ,CAAC,KAAK;AAAO;AAC5B,UAAM,KAAK,KAAK;AAChB,UAAM,IAAI,EAAC,GAAG,EAAE,KAAI;AAGpB,QAAI,EAAE,IAAI,GAAG,GAAG;AACd,QAAE,KAAK,EAAE,IAAI,GAAG;AAChB,QAAE,IAAI,GAAG;WACJ;AACL,QAAE,KAAK,GAAG,IAAI,EAAE;;AAElB,QAAI,EAAE,IAAI,GAAG,GAAG;AACd,QAAE,KAAK,EAAE,IAAI,GAAG;AAChB,QAAE,IAAI,GAAG;WACJ;AACL,QAAE,KAAK,GAAG,IAAI,EAAE;;AAGlB,QAAI;AACJ,QAAI,UAAU;AACd,aAAS,KAAK,UAAU;AACtB,UAAI,EAAE,UAAU,CAAC,EAAE,OAAO;AACxB;;AAEF,YAAM,KAAK,EAAE;AACb,UAAI,QAAQ,OAAO,WAAW,QAAQ,OAAO;AAG7C,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,iBAAU,EAAE,IAAI,EAAE,IAAK,GAAG,KAAK,GAAG;iBACzB,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG;AACpC,iBAAU,GAAG,IAAI,GAAG,IAAK,EAAE,KAAK,GAAG;;AAErC,UAAI,GAAG,IAAI,GAAG,GAAG;AACf,iBAAU,EAAE,IAAI,EAAE,IAAK,GAAG,KAAK,GAAG;iBACzB,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,GAAG;AACpC,iBAAU,GAAG,IAAI,GAAG,IAAK,EAAE,KAAK,GAAG;;AAErC,YAAM,OAAO,KAAK,IAAI,OAAO,KAAK;AAClC,UAAI,OAAO,SAAS;AAClB,kBAAU;AACV,kBAAU;;;AAGd,MAAE,UAAU;AACZ,WAAO;EACT;;;;;;;;;;;;;;;;;;EAoBO,WAAW,GAAW,GAAW,KAAa,OAAe,QAAgB,MAAY;AAE9F,SAAK,MAAM,QAAQ,OACjB,EAAE,QAAQ;MACR,GAAG,EAAE,IAAI,IAAI;MACb,GAAG,EAAE,IAAI,IAAI;MACb,GAAG,EAAE,IAAI,IAAI,OAAO;MACpB,GAAG,EAAE,IAAI,IAAI,MAAM;KACpB;AAEH,WAAO;EACT;;EAGO,KAAK,GAAkB,GAAgB;AAC5C,QAAI,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,EAAE;AAAQ,aAAO;AAE7C,aAAS,UAAO;AACd,YAAM,IAAI,EAAE,GAAG,IAAI,EAAE;AACrB,QAAE,IAAI,EAAE;AAAG,QAAE,IAAI,EAAE;AACnB,UAAI,EAAE,KAAK,EAAE,GAAG;AACd,UAAE,IAAI;AAAG,UAAE,IAAI,EAAE,IAAI,EAAE;iBACd,EAAE,KAAK,EAAE,GAAG;AACrB,UAAE,IAAI,EAAE,IAAI,EAAE;AAAG,UAAE,IAAI;aAClB;AACL,UAAE,IAAI;AAAG,UAAE,IAAI;;AAEjB,QAAE,SAAS,EAAE,SAAS;AACtB,aAAO;IACT;AACA,QAAI;AAGJ,QAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,WAAW,MAAM,WAAW,GAAG,CAAC;AACjG,aAAO,QAAO;AAChB,QAAI,aAAa;AAAO;AAGxB,QAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,WAAW,MAAM,WAAW,GAAG,CAAC,KAAK;AACnF,UAAI,EAAE,IAAI,EAAE,GAAG;AAAE,cAAM,IAAI;AAAG,YAAI;AAAG,YAAI;;AACzC,aAAO,QAAO;;AAEhB,QAAI,aAAa;AAAO;AAGxB,QAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,aAAa,WAAW,MAAM,WAAW,GAAG,CAAC,KAAK;AACnF,UAAI,EAAE,IAAI,EAAE,GAAG;AAAE,cAAM,IAAI;AAAG,YAAI;AAAG,YAAI;;AACzC,aAAO,QAAO;;AAEhB,WAAO;EACT;EAEO,YAAY,GAAW,GAAW,GAAW,GAAS;AAC3D,UAAM,KAAoB,EAAC,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,EAAC;AACrE,WAAO,CAAC,KAAK,QAAQ,EAAE;EACzB;;EAGO,QAAQ,SAAyB,WAAW,SAAS,MAAI;AAC9D,QAAI,KAAK,MAAM,WAAW;AAAG,aAAO;AACpC,QAAI;AAAQ,WAAK,UAAS;AAC1B,UAAM,WAAW,KAAK;AACtB,QAAI,CAAC;AAAU,WAAK,YAAW;AAC/B,UAAM,kBAAkB,KAAK;AAC7B,QAAI,CAAC;AAAiB,WAAK,kBAAkB;AAC7C,UAAM,YAAY,KAAK;AACvB,SAAK,QAAQ,CAAA;AACb,cAAU,QAAQ,CAAC,GAAG,OAAO,SAAQ;AACnC,UAAI;AACJ,UAAI,CAAC,EAAE,QAAQ;AACb,UAAE,eAAe;AACjB,YAAI,WAAW,UAAU;AAAO,kBAAQ,KAAK,QAAQ,CAAC;;AAExD,WAAK,QAAQ,GAAG,OAAO,KAAK;IAC9B,CAAC;AACD,QAAI,CAAC;AAAiB,aAAO,KAAK;AAClC,QAAI,CAAC;AAAU,WAAK,YAAY,KAAK;AACrC,WAAO;EACT;;EAGA,IAAW,MAAM,KAAY;AAC3B,QAAI,KAAK,WAAW;AAAK;AACzB,SAAK,SAAS,OAAO;AACrB,QAAI,CAAC,KAAK;AACR,WAAK,WAAU,EAAG,QAAO;;EAE7B;;EAGA,IAAW,QAAK;AAAc,WAAO,KAAK,UAAU;EAAO;;EAGpD,UAAU,MAAc,GAAC;AAC9B,SAAK,QAAQ,MAAM,KAAK,KAAK,OAAO,GAAG;AACvC,WAAO;EACT;;EAGU,aAAU;AAClB,QAAI,KAAK,WAAW;AAAE,aAAO;;AAC7B,SAAK,UAAS;AAEd,QAAI,KAAK,OAAO;AAEd,WAAK,MAAM,QAAQ,OAAI;AACrB,YAAI,EAAE,aAAa,EAAE,UAAU,UAAa,EAAE,MAAM,EAAE,MAAM;AAAG;AAC/D,YAAI,OAAO,EAAE;AACb,eAAO,OAAO,EAAE,MAAM,GAAG;AACvB,YAAE;AACF,gBAAM,UAAU,KAAK,QAAQ,GAAG,EAAC,GAAG,EAAE,GAAG,GAAG,MAAM,GAAG,EAAE,GAAG,GAAG,EAAE,EAAC,CAAC;AACjE,cAAI,CAAC,SAAS;AACZ,cAAE,SAAS;AACX,cAAE,IAAI;;;MAGZ,CAAC;WACI;AAEL,WAAK,MAAM,QAAQ,CAAC,GAAG,MAAK;AAC1B,YAAI,EAAE;AAAQ;AACd,eAAO,EAAE,IAAI,GAAG;AACd,gBAAM,OAAO,MAAM,IAAI,IAAI,EAAE,IAAI;AACjC,gBAAM,aAAa,MAAM,KAAK,CAAC,KAAK,QAAQ,GAAG,EAAC,GAAG,EAAE,GAAG,GAAG,MAAM,GAAG,EAAE,GAAG,GAAG,EAAE,EAAC,CAAC;AAChF,cAAI,CAAC;AAAY;AAIjB,YAAE,SAAU,EAAE,MAAM;AACpB,YAAE,IAAI;;MAEV,CAAC;;AAEH,WAAO;EACT;;;;;;EAOO,YAAY,MAAqB,UAAkB;AACxD,SAAK,MAAM,KAAK,OAAO,iBAAgB;AAGvC,UAAM,KAAK,KAAK;AAChB,QAAI,IAAI;AACN,UAAI,QAAQ;AACZ,aAAO,KAAK,MAAM,KAAK,OAAK,EAAE,OAAO,KAAK,MAAM,MAAM,IAAI,GAAG;AAC3D,aAAK,KAAK,KAAK,MAAO;;;AAK1B,QAAI,KAAK,MAAM,UAAa,KAAK,MAAM,UAAa,KAAK,MAAM,QAAQ,KAAK,MAAM,MAAM;AACtF,WAAK,eAAe;;AAItB,UAAM,WAA0B,EAAE,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,EAAC;AACxD,UAAM,SAAS,MAAM,QAAQ;AAE7B,QAAI,CAAC,KAAK,cAAc;AAAE,aAAO,KAAK;;AACtC,QAAI,CAAC,KAAK,UAAU;AAAE,aAAO,KAAK;;AAClC,QAAI,CAAC,KAAK,QAAQ;AAAE,aAAO,KAAK;;AAChC,UAAM,eAAe,IAAI;AAGzB,QAAI,OAAO,KAAK,KAAK,UAAU;AAAE,WAAK,IAAI,OAAO,KAAK,CAAC;;AACvD,QAAI,OAAO,KAAK,KAAK,UAAU;AAAE,WAAK,IAAI,OAAO,KAAK,CAAC;;AACvD,QAAI,OAAO,KAAK,KAAK,UAAU;AAAE,WAAK,IAAI,OAAO,KAAK,CAAC;;AACvD,QAAI,OAAO,KAAK,KAAK,UAAU;AAAE,WAAK,IAAI,OAAO,KAAK,CAAC;;AACvD,QAAI,MAAM,KAAK,CAAC,GAAG;AAAE,WAAK,IAAI,SAAS;AAAG,WAAK,eAAe;;AAC9D,QAAI,MAAM,KAAK,CAAC,GAAG;AAAE,WAAK,IAAI,SAAS;AAAG,WAAK,eAAe;;AAC9D,QAAI,MAAM,KAAK,CAAC,GAAG;AAAE,WAAK,IAAI,SAAS;;AACvC,QAAI,MAAM,KAAK,CAAC,GAAG;AAAE,WAAK,IAAI,SAAS;;AAEvC,SAAK,aAAa,MAAM,QAAQ;AAChC,WAAO;EACT;;EAGO,aAAa,MAAqB,UAAkB;AAEzD,UAAM,SAAS,KAAK,SAAS,MAAM,QAAQ,CAAA,GAAI,IAAI;AAEnD,QAAI,KAAK,MAAM;AAAE,WAAK,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI;;AACzD,QAAI,KAAK,MAAM;AAAE,WAAK,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI;;AACzD,QAAI,KAAK,MAAM;AAAE,WAAK,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI;;AACzD,QAAI,KAAK,MAAM;AAAE,WAAK,IAAI,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI;;AAKzD,UAAM,YAAY,KAAK,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK;AACtD,QAAI,YAAY,KAAK,SAAS,KAAK,iBAAiB,CAAC,KAAK,mBAAmB,CAAC,KAAK,mBAAmB,KAAK,OAAO,QAAS,KAAK,gBAAgB,MAAM,KAAK,aAAa,MAAM,IAAI;AAChL,YAAM,OAAO,EAAC,GAAG,KAAI;AACrB,UAAI,KAAK,gBAAgB,KAAK,MAAM,QAAW;AAAE,eAAO,KAAK;AAAG,eAAO,KAAK;;AACvE,aAAK,IAAI,KAAK,IAAI,KAAK,gBAAgB,GAAG,KAAK,CAAC;AACrD,WAAK,IAAI,KAAK,IAAI,KAAK,eAAe,KAAK,KAAK,CAAC;AACjD,WAAK,eAAe,MAAM,KAAK,aAAa;;AAG9C,QAAI,KAAK,IAAI,KAAK,QAAQ;AACxB,WAAK,IAAI,KAAK;eACL,KAAK,IAAI,GAAG;AACrB,WAAK,IAAI;;AAGX,QAAI,KAAK,UAAU,KAAK,IAAI,KAAK,QAAQ;AACvC,WAAK,IAAI,KAAK;eACL,KAAK,IAAI,GAAG;AACrB,WAAK,IAAI;;AAGX,QAAI,KAAK,IAAI,GAAG;AACd,WAAK,IAAI;;AAEX,QAAI,KAAK,IAAI,GAAG;AACd,WAAK,IAAI;;AAGX,QAAI,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ;AACjC,UAAI,UAAU;AACZ,aAAK,IAAI,KAAK,SAAS,KAAK;aACvB;AACL,aAAK,IAAI,KAAK,SAAS,KAAK;;;AAGhC,QAAI,KAAK,UAAU,KAAK,IAAI,KAAK,IAAI,KAAK,QAAQ;AAChD,UAAI,UAAU;AACZ,aAAK,IAAI,KAAK,SAAS,KAAK;aACvB;AACL,aAAK,IAAI,KAAK,SAAS,KAAK;;;AAIhC,QAAI,CAAC,MAAM,QAAQ,MAAM,MAAM,GAAG;AAChC,WAAK,SAAS;;AAGhB,WAAO;EACT;;EAGO,cAAc,QAAgB;AAEnC,QAAI,QAAQ;AACV,aAAO,KAAK,MAAM,OAAO,OAAK,EAAE,UAAU,CAAC,MAAM,QAAQ,GAAG,EAAE,KAAK,CAAC;;AAEtE,WAAO,KAAK,MAAM,OAAO,OAAK,EAAE,MAAM;EACxC;;EAGU,QAAQ,cAA8B;AAC9C,QAAI,KAAK,aAAa,CAAC,KAAK;AAAU,aAAO;AAC7C,UAAM,cAAc,gBAAgB,CAAA,GAAI,OAAO,KAAK,cAAa,CAAE;AACnE,SAAK,SAAS,UAAU;AACxB,WAAO;EACT;;EAGO,aAAU;AACf,QAAI,KAAK;AAAW,aAAO;AAC3B,SAAK,MAAM,QAAQ,OAAI;AACrB,aAAO,EAAE;AACT,aAAO,EAAE;IACX,CAAC;AACD,WAAO;EACT;;;;EAKO,cAAW;AAChB,SAAK,MAAM,QAAQ,OAAI;AACrB,QAAE,QAAQ,MAAM,QAAQ,CAAA,GAAI,CAAC;AAC7B,aAAO,EAAE;IACX,CAAC;AACD,SAAK,aAAa,KAAK,MAAM,KAAK,OAAK,EAAE,MAAM;AAC/C,WAAO;EACT;;EAGO,iBAAc;AACnB,SAAK,MAAM,QAAQ,OAAI;AACrB,UAAI,CAAC,EAAE,SAAS,MAAM,QAAQ,GAAG,EAAE,KAAK;AAAG;AAC3C,YAAM,QAAQ,GAAG,EAAE,KAAK;AACxB,QAAE,SAAS;IACb,CAAC;AACD,SAAK,QAAO;AACZ,WAAO;EACT;;;;;EAMO,kBAAkB,MAAqB,WAAW,KAAK,OAAO,SAAS,KAAK,QAAQ,OAAqB;AAC9G,UAAM,QAAQ,QAAQ,MAAM,IAAI,UAAU,MAAM,IAAI,MAAM,KAAK;AAC/D,QAAI,QAAQ;AACZ,aAAS,IAAI,OAAO,CAAC,OAAO,EAAE,GAAG;AAC/B,YAAM,IAAI,IAAI;AACd,YAAM,IAAI,KAAK,MAAM,IAAI,MAAM;AAC/B,UAAI,IAAI,KAAK,IAAI,QAAQ;AACvB;;AAEF,YAAM,MAAM,EAAC,GAAG,GAAG,GAAG,KAAK,GAAG,GAAG,KAAK,EAAC;AACvC,UAAI,CAAC,SAAS,KAAK,OAAK,MAAM,cAAc,KAAK,CAAC,CAAC,GAAG;AACpD,YAAI,KAAK,MAAM,KAAK,KAAK,MAAM;AAAG,eAAK,SAAS;AAChD,aAAK,IAAI;AACT,aAAK,IAAI;AACT,eAAO,KAAK;AACZ,gBAAQ;;;AAGZ,WAAO;EACT;;EAGO,QAAQ,MAAqB,kBAAkB,OAAO,OAAqB;AAChF,UAAM,MAAM,KAAK,MAAM,KAAK,OAAK,EAAE,QAAQ,KAAK,GAAG;AACnD,QAAI;AAAK,aAAO;AAGhB,SAAK,kBAAkB,KAAK,aAAa,IAAI,IAAI,KAAK,YAAY,IAAI;AACtE,WAAO,KAAK;AACZ,WAAO,KAAK;AAEZ,QAAI;AACJ,QAAI,KAAK,gBAAgB,KAAK,kBAAkB,MAAM,KAAK,OAAO,KAAK,QAAQ,KAAK,GAAG;AACrF,aAAO,KAAK;AACZ,sBAAgB;;AAGlB,SAAK,MAAM,KAAK,IAAI;AACpB,QAAI,iBAAiB;AAAE,WAAK,WAAW,KAAK,IAAI;;AAEhD,QAAI,CAAC;AAAe,WAAK,eAAe,IAAI;AAC5C,QAAI,CAAC,KAAK,WAAW;AAAE,WAAK,WAAU,EAAG,QAAO;;AAChD,WAAO;EACT;EAEO,WAAW,MAAqB,YAAY,MAAM,eAAe,OAAK;AAC3E,QAAI,CAAC,KAAK,MAAM,KAAK,OAAK,EAAE,QAAQ,KAAK,GAAG,GAAG;AAE7C,aAAO;;AAET,QAAI,cAAc;AAChB,WAAK,aAAa,KAAK,IAAI;;AAE7B,QAAI;AAAW,WAAK,aAAa;AAEjC,SAAK,QAAQ,KAAK,MAAM,OAAO,OAAK,EAAE,QAAQ,KAAK,GAAG;AACtD,QAAI,CAAC,KAAK;AAAkB,WAAK,WAAU;AAC3C,SAAK,QAAQ,CAAC,IAAI,CAAC;AACnB,WAAO;EACT;EAEO,UAAU,YAAY,MAAM,eAAe,MAAI;AACpD,WAAO,KAAK;AACZ,QAAI,CAAC,KAAK,MAAM;AAAQ,aAAO;AAC/B,iBAAa,KAAK,MAAM,QAAQ,OAAK,EAAE,aAAa,IAAI;AACxD,UAAM,eAAe,KAAK;AAC1B,SAAK,eAAe,eAAe,eAAe,CAAA;AAClD,SAAK,QAAQ,CAAA;AACb,WAAO,KAAK,QAAQ,YAAY;EAClC;;;;EAKO,cAAc,MAAqB,GAAoB;AAE5D,QAAI,CAAC,KAAK,oBAAoB,MAAM,CAAC;AAAG,aAAO;AAC/C,MAAE,OAAO;AAGT,QAAI,CAAC,KAAK,QAAQ;AAChB,aAAO,KAAK,SAAS,MAAM,CAAC;;AAI9B,QAAI;AACJ,UAAM,QAAQ,IAAI,iBAAgB;MAChC,QAAQ,KAAK;MACb,OAAO,KAAK;MACZ,OAAO,KAAK,MAAM,IAAI,OAAI;AACxB,YAAI,EAAE,QAAQ,KAAK,KAAK;AACtB,uBAAa,EAAC,GAAG,EAAC;AAClB,iBAAO;;AAET,eAAO,EAAC,GAAG,EAAC;MACd,CAAC;KACF;AACD,QAAI,CAAC;AAAY,aAAO;AAIxB,UAAM,UAAU,MAAM,SAAS,YAAY,CAAC,KAAK,MAAM,OAAM,KAAM,KAAK,IAAI,KAAK,OAAM,GAAI,KAAK,MAAM;AAEtG,QAAI,CAAC,WAAW,CAAC,EAAE,YAAY,EAAE,SAAS;AACxC,YAAM,UAAU,EAAE,QAAQ,GAAG;AAC7B,UAAI,KAAK,KAAK,MAAM,OAAO,GAAG;AAC5B,aAAK,QAAO;AACZ,eAAO;;;AAGX,QAAI,CAAC;AAAS,aAAO;AAIrB,UAAM,MAAM,OAAO,OAAK,EAAE,MAAM,EAAE,QAAQ,OAAI;AAC5C,YAAM,IAAI,KAAK,MAAM,KAAK,OAAK,EAAE,QAAQ,EAAE,GAAG;AAC9C,UAAI,CAAC;AAAG;AACR,YAAM,QAAQ,GAAG,CAAC;AAClB,QAAE,SAAS;IACb,CAAC;AACD,SAAK,QAAO;AACZ,WAAO;EACT;;EAGO,UAAU,MAAmB;AAClC,WAAO,KAAK;AACZ,QAAI,CAAC,KAAK;AAAQ,aAAO;AAEzB,UAAM,QAAQ,IAAI,iBAAgB;MAChC,QAAQ,KAAK;MACb,OAAO,KAAK;MACZ,OAAO,KAAK,MAAM,IAAI,CAAAA,OAAI;AAAE,eAAO,EAAC,GAAGA,GAAC;MAAC,CAAC;KAC3C;AACD,UAAM,IAAI,EAAC,GAAG,KAAI;AAClB,SAAK,YAAY,CAAC;AAClB,WAAO,EAAE;AAAI,WAAO,EAAE;AAAK,WAAO,EAAE;AAAS,WAAO,EAAE;AACtD,UAAM,QAAQ,CAAC;AACf,QAAI,MAAM,OAAM,KAAM,KAAK,QAAQ;AACjC,WAAK,cAAc,MAAM,QAAQ,CAAA,GAAI,CAAC;AACtC,aAAO;;AAET,WAAO;EACT;;EAGO,oBAAoB,MAAqB,GAAoB;AAElE,MAAE,IAAI,EAAE,KAAK,KAAK;AAClB,MAAE,IAAI,EAAE,KAAK,KAAK;AAClB,QAAI,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE;AAAG,aAAO;AAE7C,QAAI,KAAK,MAAM;AAAE,QAAE,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI;;AAC9C,QAAI,KAAK,MAAM;AAAE,QAAE,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI;;AAC9C,QAAI,KAAK,MAAM;AAAE,QAAE,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI;;AAC9C,QAAI,KAAK,MAAM;AAAE,QAAE,IAAI,KAAK,IAAI,EAAE,GAAG,KAAK,IAAI;;AAC9C,WAAQ,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE;EACzC;;EAGO,SAAS,MAAqB,GAAoB;AACvD,QAAI,CAAC;IAA2B,CAAC;AAAG,aAAO;AAC3C,QAAI;AACJ,QAAI,EAAE,SAAS,UAAa,CAAC,KAAK,WAAW;AAC3C,yBAAmB,EAAE,OAAO;;AAI9B,QAAI,OAAO,EAAE,MAAM,UAAU;AAAE,QAAE,IAAI,KAAK;;AAC1C,QAAI,OAAO,EAAE,MAAM,UAAU;AAAE,QAAE,IAAI,KAAK;;AAC1C,QAAI,OAAO,EAAE,MAAM,UAAU;AAAE,QAAE,IAAI,KAAK;;AAC1C,QAAI,OAAO,EAAE,MAAM,UAAU;AAAE,QAAE,IAAI,KAAK;;AAC1C,UAAM,WAAY,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE;AACjD,UAAM,KAAoB,MAAM,QAAQ,CAAA,GAAI,MAAM,IAAI;AACtD,UAAM,QAAQ,IAAI,CAAC;AACnB,SAAK,aAAa,IAAI,QAAQ;AAC9B,UAAM,QAAQ,GAAG,EAAE;AAEnB,QAAI,CAAC,EAAE,gBAAgB,MAAM,QAAQ,MAAM,CAAC;AAAG,aAAO;AACtD,UAAM,UAA6B,MAAM,QAAQ,CAAA,GAAI,IAAI;AAGzD,UAAM,WAAW,KAAK,WAAW,MAAM,IAAI,EAAE,IAAI;AACjD,QAAI,aAAa;AACjB,QAAI,SAAS,QAAQ;AACnB,YAAM,aAAa,KAAK,WAAW,CAAC,EAAE;AAEtC,UAAI,UAAU,aAAa,KAAK,yBAAyB,MAAM,GAAG,QAAQ,IAAI,SAAS,CAAC;AAExF,UAAI,cAAc,WAAW,KAAK,MAAM,MAAM,kBAAkB,CAAC,KAAK,KAAK,SAAS;AAClF,cAAM,OAAO,MAAM,cAAc,EAAE,MAAM,QAAQ,KAAK;AACtD,cAAM,KAAK,MAAM,KAAK,EAAE,IAAI;AAC5B,cAAM,KAAK,MAAM,KAAK,QAAQ,KAAK;AACnC,cAAM,OAAO,QAAQ,KAAK,KAAK,KAAK;AACpC,YAAI,OAAO,KAAI;AACb,kBAAQ,KAAK,YAAY,QAAQ,IAAI,QAAW,IAAI;AACpD,oBAAU;;;AAId,UAAI,SAAS;AACX,qBAAa,CAAC,KAAK,eAAe,MAAM,IAAI,SAAS,CAAC;aACjD;AACL,qBAAa;AACb,YAAI;AAAkB,iBAAO,EAAE;;;AAKnC,QAAI,cAAc,CAAC,MAAM,QAAQ,MAAM,EAAE,GAAG;AAC1C,WAAK,SAAS;AACd,YAAM,QAAQ,MAAM,EAAE;;AAExB,QAAI,EAAE,MAAM;AACV,WAAK,WAAU,EACZ,QAAO;;AAEZ,WAAO,CAAC,MAAM,QAAQ,MAAM,OAAO;EACrC;EAEO,SAAM;AACX,WAAO,KAAK,MAAM,OAAO,CAAC,KAAK,MAAM,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC;EAClE;EAEO,YAAY,MAAmB;AACpC,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,YAAY;AACjB,aAAO,KAAK;AACZ,UAAI,CAAC,KAAK;AAAW,aAAK,YAAW;;AAEvC,WAAO;EACT;EAEO,YAAS;AACd,UAAM,IAAI,KAAK,MAAM,KAAK,CAAAA,OAAKA,GAAE,SAAS;AAC1C,QAAI,GAAG;AACL,aAAO,EAAE;AACT,aAAO,EAAE;;AAEX,WAAO;EACT;;;EAIO,KAAK,cAAc,MAAM,QAAgB;AAE9C,UAAM,MAAM,KAAK,UAAU;AAC3B,UAAM,SAAS,OAAO,KAAK,WAAY,MAAM,IAAK,KAAK,SAAS,MAAM,CAAC,IAAI;AAC3E,UAAM,OAAwB,CAAA;AAC9B,SAAK,UAAS;AACd,SAAK,MAAM,QAAQ,OAAI;AACrB,YAAM,KAAK,QAAQ,KAAK,OAAK,EAAE,QAAQ,EAAE,GAAG;AAE5C,YAAM,IAAmB,EAAC,GAAG,GAAG,GAAI,MAAM,CAAA,EAAG;AAC7C,YAAM,sBAAsB,GAAG,CAAC,WAAW;AAC3C,UAAI;AAAQ,eAAO,GAAG,CAAC;AACvB,WAAK,KAAK,CAAC;IACb,CAAC;AACD,WAAO;EACT;;EAGO,mBAAmB,OAAsB;AAC9C,QAAI,CAAC,KAAK,YAAY,KAAK;AAAiB,aAAO;AAEnD,SAAK,SAAS,QAAQ,CAAC,QAAQ,WAAU;AACvC,UAAI,CAAC,UAAU,WAAW,KAAK;AAAQ,eAAO;AAC9C,UAAI,SAAS,KAAK,QAAQ;AACxB,aAAK,SAAS,MAAM,IAAI;aAErB;AAGH,cAAM,QAAQ,SAAS,KAAK;AAC5B,cAAM,QAAQ,UAAO;AACnB,cAAI,CAAC,KAAK;AAAO;AACjB,gBAAM,IAAI,OAAO,KAAK,OAAK,EAAE,QAAQ,KAAK,GAAG;AAC7C,cAAI,CAAC;AAAG;AAGR,cAAI,EAAE,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,GAAG;AACvC,cAAE,KAAM,KAAK,IAAI,KAAK,MAAM;;AAG9B,cAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAC3B,cAAE,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK;;AAGjC,cAAI,KAAK,MAAM,KAAK,MAAM,GAAG;AAC3B,cAAE,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK;;QAGnC,CAAC;;IAEL,CAAC;AACD,WAAO;EACT;;;;;;;;;;;EAYO,cAAc,YAAoB,QAAgB,SAAwB,aAAW;AAC1F,QAAI,CAAC,KAAK,MAAM,UAAU,CAAC,UAAU,eAAe;AAAQ,aAAO;AAGnE,UAAM,YAAY,WAAW,aAAa,WAAW;AACrD,QAAI,WAAW;AACb,WAAK,UAAU,CAAC;;AAIlB,QAAI,SAAS;AAAY,WAAK,YAAY,KAAK,OAAO,UAAU;AAChE,SAAK,YAAW;AAChB,QAAI,WAA4B,CAAA;AAChC,QAAI,QAAQ,YAAY,KAAK,QAAQ,MAAM,KAAK,KAAK,OAAO,EAAE;AAI9D,QAAI,SAAS,cAAc,KAAK,UAAU;AACxC,YAAM,aAAa,KAAK,SAAS,MAAM,KAAK,CAAA;AAG5C,YAAM,YAAY,KAAK,SAAS,SAAS;AACzC,UAAI,CAAC,WAAW,UAAU,eAAe,aAAa,KAAK,SAAS,SAAS,GAAG,QAAQ;AACtF,qBAAa;AACb,aAAK,SAAS,SAAS,EAAE,QAAQ,eAAY;AAC3C,gBAAM,IAAI,MAAM,KAAK,CAAAA,OAAKA,GAAE,QAAQ,UAAU,GAAG;AACjD,cAAI,GAAG;AAEL,gBAAI,CAAC,aAAa,CAAC,UAAU,cAAc;AACzC,gBAAE,IAAI,UAAU,KAAK,EAAE;AACvB,gBAAE,IAAI,UAAU,KAAK,EAAE;;AAEzB,cAAE,IAAI,UAAU,KAAK,EAAE;AACvB,gBAAI,UAAU,KAAK,UAAa,UAAU,MAAM;AAAW,gBAAE,eAAe;;QAEhF,CAAC;;AAIH,iBAAW,QAAQ,eAAY;AAC7B,cAAM,IAAI,MAAM,UAAU,OAAK,EAAE,QAAQ,UAAU,GAAG;AACtD,YAAI,MAAM,IAAI;AACZ,gBAAM,IAAI,MAAM,CAAC;AAEjB,cAAI,WAAW;AACb,cAAE,IAAI,UAAU;AAChB;;AAEF,cAAI,UAAU,gBAAgB,MAAM,UAAU,CAAC,KAAK,MAAM,UAAU,CAAC,GAAG;AACtE,iBAAK,kBAAkB,WAAW,QAAQ;;AAE5C,cAAI,CAAC,UAAU,cAAc;AAC3B,cAAE,IAAI,UAAU,KAAK,EAAE;AACvB,cAAE,IAAI,UAAU,KAAK,EAAE;AACvB,cAAE,IAAI,UAAU,KAAK,EAAE;AACvB,qBAAS,KAAK,CAAC;;AAEjB,gBAAM,OAAO,GAAG,CAAC;;MAErB,CAAC;;AAIH,QAAI,WAAW;AACb,WAAK,QAAQ,QAAQ,KAAK;WACrB;AAEL,UAAI,MAAM,QAAQ;AAChB,YAAI,OAAO,WAAW,YAAY;AAChC,iBAAO,QAAQ,YAAY,UAAU,KAAK;eACrC;AACL,gBAAM,QAAS,aAAa,WAAW,SAAU,IAAI,SAAS;AAC9D,gBAAM,OAAQ,WAAW,UAAU,WAAW;AAC9C,gBAAM,QAAS,WAAW,WAAW,WAAW;AAChD,gBAAM,QAAQ,UAAO;AAEnB,iBAAK,IAAK,WAAW,IAAI,IAAK,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,SAAS,CAAC;AAC7F,iBAAK,IAAM,WAAW,KAAK,eAAe,IAAK,IAAI,QAAS,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,IAAM,KAAK,IAAI,KAAK,GAAG,MAAM;AACvH,qBAAS,KAAK,IAAI;UACpB,CAAC;AACD,kBAAQ,CAAA;;;AAKZ,iBAAW,MAAM,KAAK,UAAU,EAAE;AAClC,WAAK,kBAAkB;AACvB,WAAK,QAAQ,CAAA;AACb,eAAS,QAAQ,UAAO;AACtB,aAAK,QAAQ,MAAM,KAAK;AACxB,eAAO,KAAK;MACd,CAAC;;AAGH,SAAK,MAAM,QAAQ,OAAK,OAAO,EAAE,KAAK;AACtC,SAAK,YAAY,OAAO,CAAC,SAAS;AAClC,WAAO,KAAK;AACZ,WAAO;EACT;;;;;;;EAQO,YAAY,OAAwB,QAAgB,QAAQ,OAAK;AACtE,UAAM,OAAwB,CAAA;AAC9B,UAAM,QAAQ,CAAC,GAAG,MAAK;AAErB,UAAI,EAAE,QAAQ,QAAW;AACvB,cAAM,WAAW,EAAE,KAAK,KAAK,MAAM,KAAK,QAAM,GAAG,OAAO,EAAE,EAAE,IAAI;AAChE,UAAE,MAAM,UAAU,OAAO,iBAAgB;;AAE3C,WAAK,CAAC,IAAI,EAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,KAAK,EAAE,IAAG;IAC/C,CAAC;AACD,SAAK,WAAW,QAAQ,CAAA,IAAK,KAAK,YAAY,CAAA;AAC9C,SAAK,SAAS,MAAM,IAAI;AACxB,WAAO;EACT;;;;;;EAOO,eAAe,GAAkB,QAAc;AACpD,MAAE,MAAM,EAAE,OAAO,iBAAgB;AACjC,UAAM,IAAmB,EAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,KAAK,EAAE,IAAG;AAC5D,QAAI,EAAE,gBAAgB,EAAE,MAAM,QAAW;AAAE,aAAO,EAAE;AAAG,aAAO,EAAE;AAAG,UAAI,EAAE;AAAc,UAAE,eAAe;;AACxG,SAAK,WAAW,KAAK,YAAY,CAAA;AACjC,SAAK,SAAS,MAAM,IAAI,KAAK,SAAS,MAAM,KAAK,CAAA;AACjD,UAAM,QAAQ,KAAK,gBAAgB,GAAG,MAAM;AAC5C,QAAI,UAAU;AACZ,WAAK,SAAS,MAAM,EAAE,KAAK,CAAC;;AAE5B,WAAK,SAAS,MAAM,EAAE,KAAK,IAAI;AACjC,WAAO;EACT;EAEU,gBAAgB,GAAkB,QAAc;AACxD,WAAO,KAAK,WAAW,MAAM,GAAG,UAAU,OAAK,EAAE,QAAQ,EAAE,GAAG,KAAK;EACrE;EAEO,0BAA0B,GAAgB;AAC/C,QAAI,CAAC,KAAK,UAAU;AAClB;;AAEF,aAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,YAAM,QAAQ,KAAK,gBAAgB,GAAG,CAAC;AACvC,UAAI,UAAU,IAAI;AAChB,aAAK,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC;;;EAGtC;;EAGO,YAAY,MAAmB;AACpC,eAAW,QAAQ,MAAM;AACvB,UAAI,KAAK,CAAC,MAAM,OAAO,SAAS;AAAO,eAAO,KAAK,IAAI;;AAEzD,WAAO;EACT;;AA76Bc,gBAAA,SAAS;;;AC1ClB,IAAM,eAAiC;EAC5C,wBAAwB;EACxB,SAAS;EACT,MAAM;EACN,YAAY;EACZ,oBAAoB;EACpB,gBAAgB;EAChB,QAAQ;EACR,WAAW,EAAE,QAAQ,4BAA4B,UAAU,QAAQ,QAAQ,KAAI;EAC/E,QAAQ;EACR,WAAW;EACX,QAAQ;EACR,YAAY;EACZ,QAAQ;EACR,QAAQ;EACR,kBAAkB;EAClB,iBAAiB;EACjB,kBAAkB,EAAE,QAAQ,mBAAmB,SAAS,2BAA0B;EAClF,WAAW,EAAE,SAAS,KAAI;EAC1B,KAAK;;;;;;;;;;;;AChBD,IAAO,YAAP,MAAgB;;;;ACCf,IAAM,UAAmB,OAAO,WAAW,eAAe,OAAO,aAAa,gBACjF,kBAAkB,YACf,kBAAkB,UAGhB,OAAe,iBAAiB,oBAAqB,OAAe,iBACtE,UAAU,iBAAiB,KAE1B,UAAkB,mBAAmB;AAK7C,IAAM,UAAN,MAAa;;AAoBb,SAAS,mBAAmB,GAAe,eAAqB;AAG9D,MAAI,EAAE,QAAQ,SAAS;AAAG;AAG1B,MAAI,EAAE;AAAY,MAAE,eAAc;AAGlC,QAAM,mBAAmB,EAAE,eAAe,CAAC,GAAG,aAAa;AAC7D;AAOA,SAAS,0BAA0B,GAAiB,eAAqB;AAGvE,MAAI,EAAE;AAAY,MAAE,eAAc;AAGlC,QAAM,mBAAmB,GAAG,aAAa;AAC3C;AAOM,SAAU,WAAW,GAAa;AAEtC,MAAI,QAAQ;AAAc;AAC1B,UAAQ,eAAe;AAKvB,qBAAmB,GAAG,WAAW;AACnC;AAMM,SAAU,UAAU,GAAa;AAErC,MAAI,CAAC,QAAQ;AAAc;AAE3B,qBAAmB,GAAG,WAAW;AACnC;AAMM,SAAU,SAAS,GAAa;AAGpC,MAAI,CAAC,QAAQ;AAAc;AAG3B,MAAI,QAAQ,qBAAqB;AAC/B,WAAO,aAAa,QAAQ,mBAAmB;AAC/C,WAAO,QAAQ;;AAGjB,QAAM,cAAc,CAAC,CAAC,UAAU;AAGhC,qBAAmB,GAAG,SAAS;AAI/B,MAAI,CAAC,aAAa;AAChB,uBAAmB,GAAG,OAAO;;AAI/B,UAAQ,eAAe;AACzB;AAOM,SAAU,YAAY,GAAe;AAEzC,MAAI,EAAE,gBAAgB;AAAS;AAC9B,IAAE,OAAuB,sBAAsB,EAAE,SAAS;AAC7D;AAEM,SAAU,aAAa,GAAe;AAE1C,MAAI,CAAC,UAAU,aAAa;AAE1B;;AAGF,MAAI,EAAE,gBAAgB;AAAS;AAC/B,4BAA0B,GAAG,YAAY;AAC3C;AAEM,SAAU,aAAa,GAAe;AAG1C,MAAI,CAAC,UAAU,aAAa;AAE1B;;AAEF,MAAI,EAAE,gBAAgB;AAAS;AAC/B,UAAQ,sBAAsB,OAAO,WAAW,MAAK;AACnD,WAAO,QAAQ;AAEf,8BAA0B,GAAG,YAAY;EAC3C,GAAG,EAAE;AACP;;;ACtJA,IAAa,oBAAb,MAAa,mBAAiB;EAU5B,YAAsB,MAAqC,KAAuB,QAA4B;AAAxF,SAAA,OAAA;AAAqC,SAAA,MAAA;AAAuB,SAAA,SAAA;AANxE,SAAA,SAAS;AAQjB,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AAEzC,SAAK,MAAK;EACZ;;EAGU,QAAK;AACb,UAAM,KAAK,KAAK,KAAK,SAAS,cAAc,KAAK;AACjD,OAAG,UAAU,IAAI,qBAAqB;AACtC,OAAG,UAAU,IAAI,GAAG,mBAAkB,MAAM,GAAG,KAAK,GAAG,EAAE;AACzD,OAAG,MAAM,SAAS;AAClB,OAAG,MAAM,aAAa;AACtB,SAAK,KAAK,YAAY,KAAK,EAAE;AAC7B,SAAK,GAAG,iBAAiB,aAAa,KAAK,UAAU;AACrD,QAAI,SAAS;AACX,WAAK,GAAG,iBAAiB,cAAc,UAAU;AACjD,WAAK,GAAG,iBAAiB,eAAe,WAAW;;AAGrD,WAAO;EACT;;EAGO,UAAO;AACZ,QAAI,KAAK;AAAQ,WAAK,SAAS,KAAK,cAAc;AAClD,SAAK,GAAG,oBAAoB,aAAa,KAAK,UAAU;AACxD,QAAI,SAAS;AACX,WAAK,GAAG,oBAAoB,cAAc,UAAU;AACpD,WAAK,GAAG,oBAAoB,eAAe,WAAW;;AAExD,SAAK,KAAK,YAAY,KAAK,EAAE;AAC7B,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO;EACT;;EAGU,WAAW,GAAa;AAChC,SAAK,iBAAiB;AACtB,aAAS,iBAAiB,aAAa,KAAK,YAAY,EAAE,SAAS,MAAM,SAAS,KAAI,CAAC;AACvF,aAAS,iBAAiB,WAAW,KAAK,UAAU,IAAI;AACxD,QAAI,SAAS;AACX,WAAK,GAAG,iBAAiB,aAAa,SAAS;AAC/C,WAAK,GAAG,iBAAiB,YAAY,QAAQ;;AAE/C,MAAE,gBAAe;AACjB,MAAE,eAAc;EAClB;;EAGU,WAAW,GAAa;AAChC,UAAM,IAAI,KAAK;AACf,QAAI,KAAK,QAAQ;AACf,WAAK,cAAc,QAAQ,CAAC;eACnB,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,GAAG;AAExD,WAAK,SAAS;AACd,WAAK,cAAc,SAAS,KAAK,cAAc;AAC/C,WAAK,cAAc,QAAQ,CAAC;AAE5B,eAAS,iBAAiB,WAAW,KAAK,SAAS;;AAErD,MAAE,gBAAe;EAEnB;;EAGU,SAAS,GAAa;AAC9B,QAAI,KAAK,QAAQ;AACf,WAAK,cAAc,QAAQ,CAAC;AAC5B,eAAS,oBAAoB,WAAW,KAAK,SAAS;;AAExD,aAAS,oBAAoB,aAAa,KAAK,YAAY,IAAI;AAC/D,aAAS,oBAAoB,WAAW,KAAK,UAAU,IAAI;AAC3D,QAAI,SAAS;AACX,WAAK,GAAG,oBAAoB,aAAa,SAAS;AAClD,WAAK,GAAG,oBAAoB,YAAY,QAAQ;;AAElD,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,MAAE,gBAAe;AACjB,MAAE,eAAc;EAClB;;EAGU,UAAU,GAAgB;AAClC,QAAI,EAAE,QAAQ,UAAU;AACtB,WAAK,KAAK,eAAe,MAAM,OAAO,eAAc;AACpD,WAAK,SAAS,KAAK,cAAc;;EAErC;;EAKU,cAAc,MAAc,OAAiB;AACrD,QAAI,KAAK,OAAO,IAAI;AAAG,WAAK,OAAO,IAAI,EAAE,KAAK;AAC9C,WAAO;EACT;;AAzGiB,kBAAA,SAAS;;;AChBtB,IAAgB,kBAAhB,MAA+B;EAArC,cAAA;AAOY,SAAA,iBAEN,CAAA;EA0BN;;EAjCE,IAAW,WAAQ;AAAgB,WAAO,KAAK;EAAW;EASnD,GAAG,OAAe,UAAuB;AAC9C,SAAK,eAAe,KAAK,IAAI;EAC/B;EAEO,IAAI,OAAa;AACtB,WAAO,KAAK,eAAe,KAAK;EAClC;EAEO,SAAM;AACX,SAAK,YAAY;EACnB;EAEO,UAAO;AACZ,SAAK,YAAY;EACnB;EAEO,UAAO;AACZ,WAAO,KAAK;EACd;EAEO,aAAa,WAAmB,OAAY;AACjD,QAAI,CAAC,KAAK,YAAY,KAAK,kBAAkB,KAAK,eAAe,SAAS;AACxE,aAAO,KAAK,eAAe,SAAS,EAAE,KAAK;EAC/C;;;;ACPF,IAAa,cAAb,MAAa,qBAAoB,gBAAe;;EA2B9C,YAAmB,IAAgC,SAAyB,CAAA,GAAE;AAC5E,UAAK;AADY,SAAA,KAAA;AAAgC,SAAA,SAAA;AArBzC,SAAA,YAAiC,EAAE,GAAG,GAAG,GAAG,EAAC;AAkS7C,SAAA,MAAM,MAAe;AAC7B,YAAM,gBAAgB,KAAK,GAAG;AAC9B,YAAM,kBAAkB,cAAc,sBAAqB;AAC3D,YAAM,UAAU;QACd,OAAO,KAAK,aAAa;QACzB,QAAQ,KAAK,aAAa,SAAS,KAAK;QACxC,MAAM,KAAK,aAAa;QACxB,KAAK,KAAK,aAAa,MAAM,KAAK;;AAEpC,YAAM,OAAO,KAAK,gBAAgB;AAClC,aAAO;QACL,UAAU;UACR,OAAO,KAAK,OAAO,gBAAgB,QAAQ,KAAK,UAAU;UAC1D,MAAM,KAAK,MAAM,gBAAgB,OAAO,KAAK,UAAU;;QAEzD,MAAM;UACJ,OAAO,KAAK,QAAQ,KAAK,UAAU;UACnC,QAAQ,KAAK,SAAS,KAAK,UAAU;;;;;;;;;;;;;;;;IAgB3C;AA3SE,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,OAAM;AACX,SAAK,eAAe,KAAK,OAAO,QAAQ;AACxC,SAAK,eAAc;EACrB;EAEO,GAAG,OAAgD,UAAoC;AAC5F,UAAM,GAAG,OAAO,QAAQ;EAC1B;EAEO,IAAI,OAA8C;AACvD,UAAM,IAAI,KAAK;EACjB;EAEO,SAAM;AACX,UAAM,OAAM;AACZ,SAAK,GAAG,UAAU,OAAO,uBAAuB;AAChD,SAAK,eAAe,KAAK,OAAO,QAAQ;EAC1C;EAEO,UAAO;AACZ,UAAM,QAAO;AACb,SAAK,GAAG,UAAU,IAAI,uBAAuB;AAC7C,SAAK,eAAe,KAAK;EAC3B;EAEO,UAAO;AACZ,SAAK,gBAAe;AACpB,SAAK,eAAe,KAAK;AACzB,WAAO,KAAK;AACZ,UAAM,QAAO;EACf;EAEO,aAAa,MAAoB;AACtC,UAAM,gBAAiB,KAAK,WAAW,KAAK,YAAY,KAAK,OAAO;AACpE,UAAM,iBAAkB,KAAK,YAAY,KAAK,aAAa,KAAK,OAAO;AACvE,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAO,KAAK,OAAO,GAAG,IAAI,KAAK,GAAG,CAAC;AAC7D,QAAI,eAAe;AACjB,WAAK,gBAAe;AACpB,WAAK,eAAc;;AAErB,QAAI,gBAAgB;AAClB,WAAK,eAAe,KAAK,OAAO,QAAQ;;AAE1C,WAAO;EACT;;EAGU,eAAe,MAAa;AACpC,QAAI,MAAM;AACR,WAAK,GAAG,UAAU,IAAI,uBAAuB;AAE7C,WAAK,GAAG,iBAAiB,aAAa,KAAK,UAAU;AACrD,WAAK,GAAG,iBAAiB,YAAY,KAAK,SAAS;WAC9C;AACL,WAAK,GAAG,UAAU,OAAO,uBAAuB;AAChD,WAAK,GAAG,oBAAoB,aAAa,KAAK,UAAU;AACxD,WAAK,GAAG,oBAAoB,YAAY,KAAK,SAAS;AACtD,UAAI,UAAU,sBAAsB,MAAM;AACxC,eAAO,UAAU;;;AAGrB,WAAO;EACT;;;EAIU,WAAW,GAAQ;AAG3B,QAAI,UAAU,qBAAqB,UAAU;AAAa;AAC1D,cAAU,oBAAoB;AAE9B,SAAK,GAAG,UAAU,OAAO,uBAAuB;EAClD;;;EAIU,UAAU,GAAQ;AAE1B,QAAI,UAAU,sBAAsB;AAAM;AAC1C,WAAO,UAAU;AAEjB,SAAK,GAAG,UAAU,IAAI,uBAAuB;EAC/C;;EAGU,iBAAc;AACtB,SAAK,WAAW,KAAK,OAAO,QAAQ,MAAM,GAAG,EAC1C,IAAI,SAAO,IAAI,KAAI,CAAE,EACrB,IAAI,SAAO,IAAI,kBAAkB,KAAK,IAAI,KAAK;MAC9C,OAAO,CAAC,UAAqB;AAC3B,aAAK,aAAa,KAAK;MACzB;MACA,MAAM,CAAC,UAAqB;AAC1B,aAAK,YAAY,KAAK;MACxB;MACA,MAAM,CAAC,UAAqB;AAC1B,aAAK,UAAU,OAAO,GAAG;MAC3B;KACD,CAAC;AACJ,WAAO;EACT;;EAGU,aAAa,OAAiB;AACtC,SAAK,gBAAgB,MAAM,oBAAoB,KAAK,GAAG,eAAe,IAAI;AAC1E,SAAK,eAAe,KAAK,GAAG,sBAAqB;AACjD,SAAK,WAAW,MAAM,iBAAiB,KAAK,EAAE;AAC9C,SAAK,UAAU,KAAK,SAAS;AAC7B,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,aAAY;AACjB,SAAK,aAAY;AACjB,UAAM,KAAK,MAAM,UAAsB,OAAO,EAAE,MAAM,eAAe,QAAQ,KAAK,GAAE,CAAE;AACtF,QAAI,KAAK,OAAO,OAAO;AACrB,WAAK,OAAO,MAAM,IAAI,KAAK,IAAG,CAAE;;AAElC,SAAK,GAAG,UAAU,IAAI,uBAAuB;AAC7C,SAAK,aAAa,eAAe,EAAE;AACnC,WAAO;EACT;;EAGU,UAAU,OAAmB,KAAW;AAChD,SAAK,WAAW,KAAK,SAAS,YAAY,KAAK;AAC/C,SAAK,eAAe,KAAK,WAAW,OAAO,GAAG;AAC9C,SAAK,aAAY;AACjB,UAAM,KAAK,MAAM,UAAsB,OAAO,EAAE,MAAM,UAAU,QAAQ,KAAK,GAAE,CAAE;AACjF,QAAI,KAAK,OAAO,QAAQ;AACtB,WAAK,OAAO,OAAO,IAAI,KAAK,IAAG,CAAE;;AAEnC,SAAK,aAAa,UAAU,EAAE;AAC9B,WAAO;EACT;;EAGU,YAAY,OAAiB;AACrC,UAAM,KAAK,MAAM,UAAsB,OAAO,EAAE,MAAM,cAAc,QAAQ,KAAK,GAAE,CAAE;AAErF,SAAK,aAAY;AACjB,QAAI,KAAK,OAAO,MAAM;AACpB,WAAK,OAAO,KAAK,EAAE;;AAErB,SAAK,GAAG,UAAU,OAAO,uBAAuB;AAChD,SAAK,aAAa,cAAc,EAAE;AAClC,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO;EACT;;EAGU,eAAY;AACpB,SAAK,mBAAmB,aAAY,iBAAiB,IAAI,UAAQ,KAAK,GAAG,MAAM,IAAI,CAAC;AACpF,SAAK,4BAA4B,KAAK,GAAG,cAAc,MAAM;AAE7D,UAAM,SAAS,KAAK,GAAG;AACvB,UAAM,gBAAgB,MAAM,gCAAgC,MAAM;AAClE,SAAK,YAAY;MACf,GAAG,cAAc;MACjB,GAAG,cAAc;;AAGnB,QAAI,iBAAiB,KAAK,GAAG,aAAa,EAAE,SAAS,MAAM,QAAQ,GAAG;AACpE,WAAK,GAAG,cAAc,MAAM,WAAW;;AAEzC,SAAK,GAAG,MAAM,WAAW;AACzB,SAAK,GAAG,MAAM,UAAU;AACxB,WAAO;EACT;;EAGU,eAAY;AACpB,iBAAY,iBAAiB,QAAQ,CAAC,MAAM,MAAK;AAC/C,WAAK,GAAG,MAAM,IAAI,IAAI,KAAK,iBAAiB,CAAC,KAAK;IACpD,CAAC;AACD,SAAK,GAAG,cAAc,MAAM,WAAW,KAAK,6BAA6B;AACzE,WAAO;EACT;;EAGU,WAAW,OAAmB,KAAW;AACjD,UAAM,SAAS,KAAK;AACpB,UAAM,UAAU;MACd,OAAO,KAAK,aAAa;MACzB,QAAQ,KAAK,aAAa,SAAS,KAAK;MACxC,MAAM,KAAK,aAAa;MACxB,KAAK,KAAK,aAAa,MAAM,KAAK;;AAGpC,UAAM,UAAU,MAAM,UAAU,OAAO;AACvC,UAAM,UAAU,KAAK,gBAAgB,IAAI,MAAM,UAAU,OAAO;AAChE,QAAI;AACJ,QAAI;AAEJ,QAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,cAAQ,SAAS;eACR,IAAI,QAAQ,GAAG,IAAI,IAAI;AAChC,cAAQ,SAAS;AACjB,cAAQ,QAAQ;AAChB,iBAAW;;AAEb,QAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,cAAQ,UAAU;eACT,IAAI,QAAQ,GAAG,IAAI,IAAI;AAChC,cAAQ,UAAU;AAClB,cAAQ,OAAO;AACf,eAAS;;AAEX,UAAM,YAAY,KAAK,eAAe,QAAQ,OAAO,QAAQ,QAAQ,UAAU,MAAM;AACrF,QAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,MAAM,UAAU,KAAK,GAAG;AAC7D,UAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,gBAAQ,QAAQ,QAAQ,QAAQ,UAAU;;AAE5C,cAAQ,QAAQ,UAAU;;AAE5B,QAAI,KAAK,MAAM,QAAQ,MAAM,MAAM,KAAK,MAAM,UAAU,MAAM,GAAG;AAC/D,UAAI,IAAI,QAAQ,GAAG,IAAI,IAAI;AACzB,gBAAQ,OAAO,QAAQ,SAAS,UAAU;;AAE5C,cAAQ,SAAS,UAAU;;AAE7B,WAAO;EACT;;EAGU,eAAe,QAAgB,SAAiB,UAAmB,QAAe;AAC1F,UAAM,IAAI,KAAK;AACf,UAAM,YAAY,WAAW,EAAE,mBAAmB,EAAE,aAAa,OAAO;AACxE,UAAM,WAAW,EAAE,WAAW,KAAK,UAAU,KAAK;AAClD,UAAM,aAAa,SAAS,EAAE,kBAAkB,EAAE,cAAc,OAAO;AACvE,UAAM,YAAY,EAAE,YAAY,KAAK,UAAU,KAAK;AACpD,UAAM,QAAQ,KAAK,IAAI,UAAU,KAAK,IAAI,UAAU,MAAM,CAAC;AAC3D,UAAM,SAAS,KAAK,IAAI,WAAW,KAAK,IAAI,WAAW,OAAO,CAAC;AAC/D,WAAO,EAAE,OAAO,OAAM;EACxB;;EAGU,eAAY;AACpB,QAAI,kBAAkB,EAAE,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,QAAQ,EAAC;AAC5D,QAAI,KAAK,GAAG,MAAM,aAAa,YAAY;AACzC,YAAM,gBAAgB,KAAK,GAAG;AAC9B,YAAM,EAAE,MAAM,IAAG,IAAK,cAAc,sBAAqB;AACzD,wBAAkB,EAAE,MAAM,KAAK,OAAO,GAAG,QAAQ,EAAC;;AAEpD,QAAI,CAAC,KAAK;AAAc,aAAO;AAC/B,WAAO,KAAK,KAAK,YAAY,EAAE,QAAQ,SAAM;AAC3C,YAAM,QAAQ,KAAK,aAAa,GAAG;AACnC,YAAM,kBAAkB,QAAQ,WAAW,QAAQ,SAAS,KAAK,UAAU,IAAI,QAAQ,YAAY,QAAQ,QAAQ,KAAK,UAAU,IAAI;AACtI,WAAK,GAAG,MAAM,GAAG,KAAK,QAAQ,gBAAgB,GAAG,KAAK,kBAAkB;IAC1E,CAAC;AACD,WAAO;EACT;;EAGU,kBAAe;AACvB,SAAK,SAAS,QAAQ,YAAU,OAAO,QAAO,CAAE;AAChD,WAAO,KAAK;AACZ,WAAO;EACT;;AA/QiB,YAAA,mBAAmB,CAAC,SAAS,UAAU,YAAY,QAAQ,OAAO,WAAW,QAAQ;;;AC1BxG,IAAM,gBAAgB;AAItB,IAAa,cAAb,MAAa,qBAAoB,gBAAe;EA+B9C,YAAmB,IAAgC,SAAoB,CAAA,GAAE;AACvE,UAAK;AADY,SAAA,KAAA;AAAgC,SAAA,SAAA;AAPzC,SAAA,gBAA+B;MACvC,QAAQ;MACR,QAAQ;MACR,SAAS;MACT,SAAS;;AAOT,UAAM,aAAa,QAAQ,QAAQ,UAAU,CAAC;AAC9C,UAAM,IAAI,GAAG;AACb,SAAK,UAAU,CAAC,cAAc,GAAG,UAAU,SAAS,UAAU,IAAI,CAAC,EAAE,IAAK,GAAG,UAAU,CAAC,GAAG,cAAc,OAAO,MAAM,KAAK,EAAE,IAAI,MAAM,KAAK,GAAG,iBAAiB,OAAO,MAAM,CAAC;AAC9K,QAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,WAAK,UAAU,CAAC,EAAE;;AAGpB,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,aAAa,KAAK,WAAW,KAAK,IAAI;AAC3C,SAAK,WAAW,KAAK,SAAS,KAAK,IAAI;AACvC,SAAK,YAAY,KAAK,UAAU,KAAK,IAAI;AACzC,SAAK,OAAM;EACb;EAEO,GAAG,OAAoB,UAAoC;AAChE,UAAM,GAAG,OAAO,QAAQ;EAC1B;EAEO,IAAI,OAAkB;AAC3B,UAAM,IAAI,KAAK;EACjB;EAEO,SAAM;AACX,QAAI,KAAK,aAAa;AAAO;AAC7B,UAAM,OAAM;AACZ,SAAK,QAAQ,QAAQ,YAAS;AAC5B,aAAO,iBAAiB,aAAa,KAAK,UAAU;AACpD,UAAI,SAAS;AACX,eAAO,iBAAiB,cAAc,UAAU;AAChD,eAAO,iBAAiB,eAAe,WAAW;;IAGtD,CAAC;AACD,SAAK,GAAG,UAAU,OAAO,uBAAuB;EAClD;EAEO,QAAQ,aAAa,OAAK;AAC/B,QAAI,KAAK,aAAa;AAAM;AAC5B,UAAM,QAAO;AACb,SAAK,QAAQ,QAAQ,YAAS;AAC5B,aAAO,oBAAoB,aAAa,KAAK,UAAU;AACvD,UAAI,SAAS;AACX,eAAO,oBAAoB,cAAc,UAAU;AACnD,eAAO,oBAAoB,eAAe,WAAW;;IAEzD,CAAC;AACD,QAAI,CAAC;AAAY,WAAK,GAAG,UAAU,IAAI,uBAAuB;EAChE;EAEO,UAAO;AACZ,QAAI,KAAK;AAAa,aAAO,aAAa,KAAK,WAAW;AAC1D,WAAO,KAAK;AACZ,QAAI,KAAK;AAAgB,WAAK,SAAS,KAAK,cAAc;AAC1D,SAAK,QAAQ,IAAI;AACjB,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,UAAM,QAAO;EACf;EAEO,aAAa,MAAe;AACjC,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAO,KAAK,OAAO,GAAG,IAAI,KAAK,GAAG,CAAC;AAC7D,WAAO;EACT;;EAGU,WAAW,GAAa;AAEhC,QAAI,UAAU;AAAc;AAC5B,QAAI,EAAE,WAAW;AAAG,aAAO;AAG3B,QAAI,CAAC,KAAK,QAAQ,KAAK,QAAM,OAAO,EAAE,MAAM,KAAM,EAAE,OAAuB,QAAQ,aAAa;AAAG,aAAO;AAC1G,QAAI,KAAK,OAAO,QAAQ;AACtB,UAAK,EAAE,OAAuB,QAAQ,KAAK,OAAO,MAAM;AAAG,eAAO;;AAGpE,SAAK,iBAAiB;AACtB,WAAO,KAAK;AACZ,WAAO,UAAU;AACjB,WAAO,UAAU;AAEjB,aAAS,iBAAiB,aAAa,KAAK,YAAY,EAAE,SAAS,MAAM,SAAS,KAAI,CAAE;AACxF,aAAS,iBAAiB,WAAW,KAAK,UAAU,IAAI;AACxD,QAAI,SAAS;AACX,QAAE,cAAc,iBAAiB,aAAa,SAAS;AACvD,QAAE,cAAc,iBAAiB,YAAY,QAAQ;;AAGvD,MAAE,eAAc;AAGhB,QAAI,SAAS;AAAgB,eAAS,cAA8B,KAAI;AAExE,cAAU,eAAe;AACzB,WAAO;EACT;;EAGU,UAAU,GAAY;AAC9B,QAAI,CAAC,KAAK;AAAU;AACpB,UAAM,KAAK,MAAM,UAAqB,GAAG,EAAE,QAAQ,KAAK,IAAI,MAAM,OAAM,CAAE;AAC1E,QAAI,KAAK,OAAO,MAAM;AACpB,WAAK,OAAO,KAAK,IAAI,KAAK,GAAE,CAAE;;AAEhC,SAAK,aAAa,QAAQ,EAAE;EAC9B;;EAGU,WAAW,GAAY;AAE/B,UAAM,IAAI,KAAK;AACf,SAAK,WAAW;AAEhB,QAAI,KAAK,UAAU;AACjB,WAAK,YAAY,CAAC;AAElB,UAAI,UAAU,WAAW;AACvB,cAAM,QAAQ,OAAO,UAAU,UAAU,SAAS,IAAI,UAAU,YAAsB;AACtF,YAAI,KAAK;AAAa,iBAAO,aAAa,KAAK,WAAW;AAC1D,aAAK,cAAc,OAAO,WAAW,MAAM,KAAK,UAAU,CAAC,GAAG,KAAK;aAC9D;AACL,aAAK,UAAU,CAAC;;eAET,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,GAAG;AAIxD,WAAK,WAAW;AAChB,gBAAU,cAAc;AAExB,YAAM,OAAO,KAAK,GAAG,eAAe;AACpC,UAAI,MAAM;AACR,kBAAU,cAAe,KAAK,GAAqB,UAAU;aACxD;AACL,eAAO,UAAU;;AAEnB,WAAK,SAAS,KAAK,cAAa;AAChC,WAAK,6BAA4B;AACjC,WAAK,gBAAgB,MAAM,gCAAgC,KAAK,iBAAiB;AACjF,WAAK,aAAa,KAAK,eAAe,GAAG,KAAK,IAAI,KAAK,iBAAiB;AACxE,WAAK,kBAAkB,CAAC;AAExB,YAAM,KAAK,MAAM,UAAqB,GAAG,EAAE,QAAQ,KAAK,IAAI,MAAM,YAAW,CAAE;AAC/E,UAAI,KAAK,OAAO,OAAO;AACrB,aAAK,OAAO,MAAM,IAAI,KAAK,GAAE,CAAE;;AAEjC,WAAK,aAAa,aAAa,EAAE;AAEjC,eAAS,iBAAiB,WAAW,KAAK,SAAS;;AAGrD,WAAO;EACT;;EAGU,SAAS,GAAa;AAC9B,aAAS,oBAAoB,aAAa,KAAK,YAAY,IAAI;AAC/D,aAAS,oBAAoB,WAAW,KAAK,UAAU,IAAI;AAC3D,QAAI,WAAW,EAAE,eAAe;AAC9B,QAAE,cAAc,oBAAoB,aAAa,WAAW,IAAI;AAChE,QAAE,cAAc,oBAAoB,YAAY,UAAU,IAAI;;AAEhE,QAAI,KAAK,UAAU;AACjB,aAAO,KAAK;AACZ,aAAQ,KAAK,GAAG,eAAuC;AACvD,eAAS,oBAAoB,WAAW,KAAK,SAAS;AAGtD,UAAI,UAAU,aAAa,OAAO,KAAK,GAAG,eAAe;AACvD,eAAO,UAAU;;AAGnB,WAAK,kBAAkB,MAAM,WAAW,KAAK,6BAA6B;AAC1E,UAAI,KAAK,WAAW,KAAK;AAAI,aAAK,OAAO,OAAM;AAC/C,WAAK,mBAAkB;AAEvB,YAAM,KAAK,MAAM,UAAqB,GAAG,EAAE,QAAQ,KAAK,IAAI,MAAM,WAAU,CAAE;AAC9E,UAAI,KAAK,OAAO,MAAM;AACpB,aAAK,OAAO,KAAK,EAAE;;AAErB,WAAK,aAAa,YAAY,EAAE;AAGhC,UAAI,UAAU,aAAa;AACzB,kBAAU,YAAY,KAAK,CAAC;;;AAGhC,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,UAAU;AACjB,WAAO,UAAU;AACjB,WAAO,UAAU;AACjB,MAAE,eAAc;EAClB;;EAGU,UAAU,GAAgB;AAClC,UAAM,IAAI,KAAK,GAAG;AAClB,UAAM,OAAO,GAAG,QAAS,UAAU,aAAa,IAAwB;AAExE,QAAI,EAAE,QAAQ,UAAU;AACtB,UAAI,KAAK,EAAE,aAAa;AACtB,UAAE,QAAQ,EAAE;AACZ,eAAO,EAAE;;AAEX,YAAM,WAAU;AAChB,WAAK,SAAS,KAAK,cAAc;eACxB,KAAK,SAAS,EAAE,QAAQ,OAAO,EAAE,QAAQ,MAAM;AACxD,UAAI,CAAC,MAAM,aAAa,CAAC;AAAG;AAC5B,QAAE,cAAc,EAAE,eAAe,EAAE,GAAG,EAAE,MAAK;AAC7C,aAAO,EAAE;AACT,WAAK,aAAa,KAAK,EACpB,OAAO,EAAE,IAAI,EAAE,KAAK,CAAC,KAAK,WAAW,WAAW,MAAM,CAAC,KAAK,WAAW,WAAU,CAAE,EACnF,aAAY;AACf,QAAE,UAAU;AACZ,WAAK,aAAa,KAAK,eAAe,KAAK,UAAU,EAAE,IAAI,KAAK,iBAAiB;AACjF,WAAK,OAAO,MAAM,QAAQ,KAAK,WAAW,QAAQ;AAClD,WAAK,OAAO,MAAM,SAAS,KAAK,WAAW,SAAS;AACpD,YAAM,KAAK,EAAE,OAAO,KAAK,GAAG;AAC5B,aAAO,EAAE;AACT,WAAK,WAAW,KAAK,QAAQ;;EAEjC;;EAGU,gBAAa;AACrB,QAAI,SAAS,KAAK;AAClB,QAAI,OAAO,KAAK,OAAO,WAAW,YAAY;AAC5C,eAAS,KAAK,OAAO,OAAO,KAAK,EAAE;eAC1B,KAAK,OAAO,WAAW,SAAS;AACzC,eAAS,MAAM,UAAU,KAAK,EAAE;;AAElC,QAAI,CAAC,OAAO,eAAe;AACzB,YAAM,SAAS,QAAQ,KAAK,OAAO,aAAa,WAAW,KAAK,GAAG,gBAAgB,KAAK,OAAO,QAAQ;;AAEzG,SAAK,yBAAyB,aAAY,gBAAgB,IAAI,UAAQ,KAAK,GAAG,MAAM,IAAI,CAAC;AACzF,WAAO;EACT;;EAGU,kBAAkB,GAAY;AACtC,SAAK,OAAO,UAAU,IAAI,uBAAuB;AAEjD,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,gBAAgB;AAEtB,UAAM,QAAQ,KAAK,WAAW,QAAQ;AACtC,UAAM,SAAS,KAAK,WAAW,SAAS;AACxC,UAAM,aAAa;AACnB,UAAM,WAAW;AACjB,SAAK,YAAY,CAAC;AAClB,UAAM,aAAa;AACnB,eAAW,MAAK;AACd,UAAI,KAAK,QAAQ;AACf,cAAM,aAAa;;IAEvB,GAAG,CAAC;AACJ,WAAO;EACT;;EAGU,qBAAkB;AAC1B,SAAK,OAAO,UAAU,OAAO,uBAAuB;AACpD,UAAM,OAAQ,KAAK,QAAgC;AAEnD,QAAI,CAAC,MAAM,oBAAoB,KAAK,wBAAwB;AAC1D,YAAM,SAAS,KAAK;AAMpB,YAAM,aAAa,KAAK,uBAAuB,YAAY,KAAK;AAChE,aAAO,MAAM,aAAa,KAAK,uBAAuB,YAAY,IAAI;AACtE,mBAAY,gBAAgB,QAAQ,UAAQ,OAAO,MAAM,IAAI,IAAI,KAAK,uBAAuB,IAAI,KAAK,IAAI;AAC1G,iBAAW,MAAM,OAAO,MAAM,aAAa,YAAY,EAAE;;AAE3D,WAAO,KAAK;AACZ,WAAO;EACT;;EAGU,YAAY,GAAY;AAChC,UAAM,kBAAkB,EAAE,MAAM,GAAG,KAAK,EAAC;AAKzC,UAAM,QAAQ,KAAK,OAAO;AAC1B,UAAM,SAAS,KAAK;AACpB,UAAM,QAAQ,EAAE,UAAU,OAAO,aAAa,gBAAgB,QAAQ,KAAK,cAAc,SAAS;AAClG,UAAM,OAAO,EAAE,UAAU,OAAO,YAAY,gBAAgB,OAAO,KAAK,cAAc,SAAS;EACjG;;EAGU,+BAA4B;AACpC,SAAK,oBAAoB,KAAK,OAAO;AACrC,QAAI,KAAK,OAAO,MAAM,aAAa,SAAS;AAC1C,WAAK,4BAA4B,KAAK,kBAAkB,MAAM;AAC9D,UAAI,iBAAiB,KAAK,iBAAiB,EAAE,SAAS,MAAM,QAAQ,GAAG;AACrE,aAAK,kBAAkB,MAAM,WAAW;;;AAG5C,WAAO;EACT;;EAGU,eAAe,OAAkB,IAAiB,QAAmB;AAG7E,QAAI,eAAe;AACnB,QAAI,eAAe;AACnB,QAAI,QAAQ;AACV,qBAAe,KAAK,cAAc;AAClC,qBAAe,KAAK,cAAc;;AAGpC,UAAM,eAAe,GAAG,sBAAqB;AAC7C,WAAO;MACL,MAAM,aAAa;MACnB,KAAK,aAAa;MAClB,YAAY,CAAE,MAAM,UAAU,aAAa,OAAO;MAClD,WAAW,CAAE,MAAM,UAAU,aAAa,MAAM;MAChD,OAAO,aAAa,QAAQ,KAAK,cAAc;MAC/C,QAAQ,aAAa,SAAS,KAAK,cAAc;;EAErD;;EAGO,KAAE;AACP,UAAM,gBAAgB,KAAK,GAAG;AAC9B,UAAM,kBAAkB,cAAc,sBAAqB;AAC3D,UAAM,SAAS,KAAK,OAAO,sBAAqB;AAChD,WAAO;MACL,UAAU;QACR,MAAM,OAAO,MAAM,gBAAgB,OAAO,KAAK,cAAc;QAC7D,OAAO,OAAO,OAAO,gBAAgB,QAAQ,KAAK,cAAc;;;;;;;EAOtE;;AAvWiB,YAAA,kBAAkB,CAAC,SAAS,UAAU,aAAa,oBAAoB,cAAc,iBAAiB,YAAY,QAAQ,OAAO,YAAY,YAAY;;;AC/BtK,IAAO,cAAP,cAA2B,gBAAe;EAI9C,YAAmB,IAAwB,SAAyB,CAAA,GAAE;AACpE,UAAK;AADY,SAAA,KAAA;AAAwB,SAAA,SAAA;AAGzC,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,cAAc,KAAK,YAAY,KAAK,IAAI;AAC7C,SAAK,OAAM;AACX,SAAK,aAAY;EACnB;EAEO,GAAG,OAAwC,UAAoC;AACpF,UAAM,GAAG,OAAO,QAAQ;EAC1B;EAEO,IAAI,OAAsC;AAC/C,UAAM,IAAI,KAAK;EACjB;EAEO,SAAM;AACX,QAAI,KAAK,aAAa;AAAO;AAC7B,UAAM,OAAM;AACZ,SAAK,GAAG,UAAU,IAAI,cAAc;AACpC,SAAK,GAAG,UAAU,OAAO,uBAAuB;AAChD,SAAK,GAAG,iBAAiB,cAAc,KAAK,WAAW;AACvD,SAAK,GAAG,iBAAiB,cAAc,KAAK,WAAW;AACvD,QAAI,SAAS;AACX,WAAK,GAAG,iBAAiB,gBAAgB,YAAY;AACrD,WAAK,GAAG,iBAAiB,gBAAgB,YAAY;;EAEzD;EAEO,QAAQ,aAAa,OAAK;AAC/B,QAAI,KAAK,aAAa;AAAM;AAC5B,UAAM,QAAO;AACb,SAAK,GAAG,UAAU,OAAO,cAAc;AACvC,QAAI,CAAC;AAAY,WAAK,GAAG,UAAU,IAAI,uBAAuB;AAC9D,SAAK,GAAG,oBAAoB,cAAc,KAAK,WAAW;AAC1D,SAAK,GAAG,oBAAoB,cAAc,KAAK,WAAW;AAC1D,QAAI,SAAS;AACX,WAAK,GAAG,oBAAoB,gBAAgB,YAAY;AACxD,WAAK,GAAG,oBAAoB,gBAAgB,YAAY;;EAE5D;EAEO,UAAO;AACZ,SAAK,QAAQ,IAAI;AACjB,SAAK,GAAG,UAAU,OAAO,cAAc;AACvC,SAAK,GAAG,UAAU,OAAO,uBAAuB;AAChD,UAAM,QAAO;EACf;EAEO,aAAa,MAAoB;AACtC,WAAO,KAAK,IAAI,EAAE,QAAQ,SAAO,KAAK,OAAO,GAAG,IAAI,KAAK,GAAG,CAAC;AAC7D,SAAK,aAAY;AACjB,WAAO;EACT;;EAGU,YAAY,GAAa;AAEjC,QAAI,CAAC,UAAU;AAAa;AAC5B,QAAI,CAAC,KAAK,SAAS,UAAU,YAAY,EAAE;AAAG;AAC9C,MAAE,eAAc;AAChB,MAAE,gBAAe;AAGjB,QAAI,UAAU,eAAe,UAAU,gBAAgB,MAAM;AAC3D,gBAAU,YAAY,YAAY,GAAgB,IAAI;;AAExD,cAAU,cAAc;AAExB,UAAM,KAAK,MAAM,UAAqB,GAAG,EAAE,QAAQ,KAAK,IAAI,MAAM,WAAU,CAAE;AAC9E,QAAI,KAAK,OAAO,MAAM;AACpB,WAAK,OAAO,KAAK,IAAI,KAAK,IAAI,UAAU,WAAW,CAAC;;AAEtD,SAAK,aAAa,YAAY,EAAE;AAChC,SAAK,GAAG,UAAU,IAAI,mBAAmB;EAE3C;;EAGU,YAAY,GAAe,gBAAgB,OAAK;AAExD,QAAI,CAAC,UAAU,eAAe,UAAU,gBAAgB;AAAM;AAC9D,MAAE,eAAc;AAChB,MAAE,gBAAe;AAEjB,UAAM,KAAK,MAAM,UAAqB,GAAG,EAAE,QAAQ,KAAK,IAAI,MAAM,UAAS,CAAE;AAC7E,QAAI,KAAK,OAAO,KAAK;AACnB,WAAK,OAAO,IAAI,IAAI,KAAK,IAAI,UAAU,WAAW,CAAC;;AAErD,SAAK,aAAa,WAAW,EAAE;AAE/B,QAAI,UAAU,gBAAgB,MAAM;AAClC,aAAO,UAAU;AAIjB,UAAI,CAAC,eAAe;AAClB,YAAI;AACJ,YAAI,SAAwB,KAAK,GAAG;AACpC,eAAO,CAAC,cAAc,QAAQ;AAC5B,uBAAa,OAAO,WAAW;AAC/B,mBAAS,OAAO;;AAElB,YAAI,YAAY;AACd,qBAAW,YAAY,CAAC;;;;EAIhC;;EAGO,KAAK,GAAa;AACvB,MAAE,eAAc;AAChB,UAAM,KAAK,MAAM,UAAqB,GAAG,EAAE,QAAQ,KAAK,IAAI,MAAM,OAAM,CAAE;AAC1E,QAAI,KAAK,OAAO,MAAM;AACpB,WAAK,OAAO,KAAK,IAAI,KAAK,IAAI,UAAU,WAAW,CAAC;;AAEtD,SAAK,aAAa,QAAQ,EAAE;EAC9B;;EAGU,SAAS,IAAe;AAChC,WAAO,OAAO,CAAC,KAAK,UAAU,KAAK,OAAO,EAAE;EAC9C;;EAGU,eAAY;AACpB,QAAI,CAAC,KAAK,OAAO;AAAQ,aAAO;AAChC,QAAI,OAAO,KAAK,OAAO,WAAW,UAAU;AAC1C,WAAK,SAAS,CAAC,OAAoB,GAAG,UAAU,SAAS,KAAK,OAAO,MAAgB,KAAK,GAAG,QAAQ,KAAK,OAAO,MAAgB;WAC5H;AACL,WAAK,SAAS,KAAK,OAAO;;AAE5B,WAAO;EACT;;EAGU,IAAI,MAAiB;AAC7B,WAAO;MACL,WAAW,KAAK;MAChB,GAAG,KAAK,GAAE;;EAEd;;;;AC3JI,IAAO,YAAP,MAAO,WAAS;EAEpB,OAAO,KAAK,IAAiB;AAC3B,QAAI,CAAC,GAAG,WAAW;AAAE,SAAG,YAAY,IAAI,WAAU,EAAE;;AACpD,WAAO,GAAG;EACZ;EAMA,YAAmB,IAAiB;AAAjB,SAAA,KAAA;EAAoB;EAEhC,GAAG,WAAmB,UAAqC;AAChE,QAAI,KAAK,eAAe,CAAC,QAAQ,aAAa,UAAU,EAAE,QAAQ,SAAS,IAAI,IAAI;AACjF,WAAK,YAAY,GAAG,WAAgD,QAAQ;eACnE,KAAK,eAAe,CAAC,QAAQ,YAAY,SAAS,EAAE,QAAQ,SAAS,IAAI,IAAI;AACtF,WAAK,YAAY,GAAG,WAA8C,QAAQ;eACjE,KAAK,eAAe,CAAC,eAAe,UAAU,YAAY,EAAE,QAAQ,SAAS,IAAI,IAAI;AAC9F,WAAK,YAAY,GAAG,WAAsD,QAAQ;;AAEpF,WAAO;EACT;EAEO,IAAI,WAAiB;AAC1B,QAAI,KAAK,eAAe,CAAC,QAAQ,aAAa,UAAU,EAAE,QAAQ,SAAS,IAAI,IAAI;AACjF,WAAK,YAAY,IAAI,SAA8C;eAC1D,KAAK,eAAe,CAAC,QAAQ,YAAY,SAAS,EAAE,QAAQ,SAAS,IAAI,IAAI;AACtF,WAAK,YAAY,IAAI,SAA4C;eACxD,KAAK,eAAe,CAAC,eAAe,UAAU,YAAY,EAAE,QAAQ,SAAS,IAAI,IAAI;AAC9F,WAAK,YAAY,IAAI,SAAoD;;AAE3E,WAAO;EACT;EAEO,eAAe,MAAe;AACnC,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,YAAY,KAAK,IAAI,IAAI;WAC3C;AACL,WAAK,YAAY,aAAa,IAAI;;AAEpC,WAAO;EACT;EAEO,iBAAc;AACnB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAO;AACxB,aAAO,KAAK;;AAEd,WAAO;EACT;EAEO,eAAe,MAAoB;AACxC,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,YAAY,KAAK,IAAI,IAAI;WAC3C;AACL,WAAK,YAAY,aAAa,IAAI;;AAEpC,WAAO;EACT;EAEO,iBAAc;AACnB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAO;AACxB,aAAO,KAAK;;AAEd,WAAO;EACT;EAEO,eAAe,MAAoB;AACxC,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,IAAI,YAAY,KAAK,IAAI,IAAI;WAC3C;AACL,WAAK,YAAY,aAAa,IAAI;;AAEpC,WAAO;EACT;EAEO,iBAAc;AACnB,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,QAAO;AACxB,aAAO,KAAK;;AAEd,WAAO;EACT;;;;AClEI,IAAO,cAAP,MAAkB;EAEf,UAAU,IAAyB,MAAc,KAAa,OAAe;AAClF,SAAK,eAAe,IAAI,IAAI,EAAE,QAAQ,SAAM;AAC1C,UAAI,SAAS,aAAa,SAAS,UAAU;AAC3C,YAAI,eAAe,IAAI,YAAY,IAAI,EAAC;iBAC/B,SAAS,WAAW;AAC7B,YAAI,eAAe,IAAI,eAAc;iBAC5B,SAAS,UAAU;AAC5B,YAAI,eAAe,EAAE,CAAC,GAAG,GAAG,MAAK,CAAE;aAC9B;AACL,cAAM,IAAI,IAAI,GAAG;AACjB,cAAM,OAAO,EAAE;AACf,YAAI,UAAU,IAAI,GAAG,aAAa,mBAAmB,KAAK,KAAK,KAAK,UAAU,WAAW;AACzF,YAAI,YAAY;AAAO,oBAAU;AAQjC,cAAM,WAAW,CAAC,KAAK,KAAK;AAC5B,YAAI,eAAe;UACjB,GAAG,KAAK,KAAK;UACb,GAAG,EAAE,SAAS,SAAQ;UACtB,GAAG;YACD,OAAO,KAAK;YACZ,MAAM,KAAK;YACX,QAAQ,KAAK;;SAEhB;;IAEL,CAAC;AACD,WAAO;EACT;EAEO,UAAU,IAAyB,MAAc,KAAa,OAAe;AAClF,SAAK,eAAe,IAAI,IAAI,EAAE,QAAQ,SAAM;AAC1C,UAAI,SAAS,aAAa,SAAS,UAAU;AAC3C,YAAI,eAAe,IAAI,YAAY,IAAI,EAAC;iBAC/B,SAAS,WAAW;AAC7B,YAAI,eAAe,IAAI,eAAc;iBAC5B,SAAS,UAAU;AAC5B,YAAI,eAAe,EAAE,CAAC,GAAG,GAAG,MAAK,CAAE;aAC9B;AACL,cAAM,OAAO,IAAI,GAAG,cAAc;AAClC,YAAI,eAAe;UACjB,GAAG,KAAK,KAAK;UACb,GAAG;;YAED,OAAO,KAAK;YACZ,MAAM,KAAK;YACX,MAAM,KAAK;;SAEd;;IAEL,CAAC;AACD,WAAO;EACT;EAEO,OAAO,IAAsB,MAAe;AACjD,SAAK,eAAe,EAAE,EAAE,QAAQ,SAAO,IAAI,eAAe,IAAI,CAAC;AAC/D,WAAO;EACT;EAEO,UAAU,IAAyB,MAA0B,KAAa,OAAe;AAC9F,QAAI,OAAO,KAAK,WAAW,cAAc,CAAC,KAAK,SAAS;AACtD,WAAK,UAAU,KAAK;AACpB,WAAK,SAAS,CAACC,QAAO,KAAK,QAAQA,GAAE;;AAEvC,SAAK,eAAe,IAAI,IAAI,EAAE,QAAQ,SAAM;AAC1C,UAAI,SAAS,aAAa,SAAS,UAAU;AAC3C,YAAI,eAAe,IAAI,YAAY,IAAI,EAAC;iBAC/B,SAAS,WAAW;AAC7B,YAAI,eAAe,IAAI,eAAc;iBAC5B,SAAS,UAAU;AAC5B,YAAI,eAAe,EAAE,CAAC,GAAG,GAAG,MAAK,CAAE;aAC9B;AACL,YAAI,eAAe,IAAI;;IAE3B,CAAC;AACD,WAAO;EACT;;EAGO,YAAY,IAAiB;AAClC,WAAO,CAAC,EAAE,IAAI,WAAW,eAAe,CAAC,GAAG,UAAU,YAAY;EACpE;;EAGO,YAAY,IAAiB;AAClC,WAAO,CAAC,EAAE,IAAI,WAAW,eAAe,CAAC,GAAG,UAAU,YAAY;EACpE;;EAGO,YAAY,IAAiB;AAClC,WAAO,CAAC,EAAE,IAAI,WAAW,eAAe,CAAC,GAAG,UAAU,YAAY;EACpE;EAEO,GAAG,IAAyB,MAAc,UAAoB;AACnE,SAAK,eAAe,EAAE,EAAE,QAAQ,SAC9B,IAAI,GAAG,MAAM,CAAC,UAAgB;AAC5B,eACE,OACA,UAAU,cAAc,UAAU,YAAY,KAAK,MAAM,QACzD,UAAU,cAAc,UAAU,YAAY,SAAS,IAAI;IAC/D,CAAC,CAAC;AAEJ,WAAO;EACT;EAEO,IAAI,IAAyB,MAAY;AAC9C,SAAK,eAAe,EAAE,EAAE,QAAQ,SAAO,IAAI,IAAI,IAAI,CAAC;AACpD,WAAO;EACT;;EAGU,eAAe,KAAuB,MAAa;AAE3D,UAAM,SAAU,IAAwB,aAAc,SAAS,aAAa,SAAS;AACrF,UAAM,QAAQ,MAAM,YAAY,GAAG;AACnC,QAAI,CAAC,MAAM;AAAQ,aAAO,CAAA;AAC1B,UAAM,OAAO,MAAM,IAAI,OAAK,EAAE,cAAc,SAAS,UAAU,KAAK,CAAC,IAAI,KAAK,EAAE,OAAO,OAAK,CAAC;AAC7F,WAAO;EACT;;;;ACnIF,IAAM,KAAK,IAAI;AA0Cf,IAAa,YAAb,MAAa,WAAS;;;;;;;;;;;;;;EAeb,OAAO,KAAK,UAA4B,CAAA,GAAI,aAA+B,eAAa;AAC7F,QAAI,OAAO,aAAa;AAAa,aAAO;AAC5C,UAAM,KAAK,WAAU,eAAe,UAAU;AAC9C,QAAI,CAAC,IAAI;AACP,UAAI,OAAO,eAAe,UAAU;AAClC,gBAAQ,MAAM,0DAA0D,aAAa,6IACmB;aACnG;AACL,gBAAQ,MAAM,8CAA8C;;AAE9D,aAAO;;AAET,QAAI,CAAC,GAAG,WAAW;AACjB,SAAG,YAAY,IAAI,WAAU,IAAI,MAAM,UAAU,OAAO,CAAC;;AAE3D,WAAO,GAAG;EACZ;;;;;;;;;;EAWO,OAAO,QAAQ,UAA4B,CAAA,GAAI,WAAW,eAAa;AAC5E,UAAM,QAAqB,CAAA;AAC3B,QAAI,OAAO,aAAa;AAAa,aAAO;AAC5C,eAAU,gBAAgB,QAAQ,EAAE,QAAQ,QAAK;AAC/C,UAAI,CAAC,GAAG,WAAW;AACjB,WAAG,YAAY,IAAI,WAAU,IAAI,MAAM,UAAU,OAAO,CAAC;;AAE3D,YAAM,KAAK,GAAG,SAAS;IACzB,CAAC;AACD,QAAI,MAAM,WAAW,GAAG;AACtB,cAAQ,MAAM,0DAA0D,WAAW,6IACqB;;AAE1G,WAAO;EACT;;;;;;;;EASO,OAAO,QAAQ,QAAqB,MAAwB,CAAA,GAAE;AACnE,QAAI,CAAC;AAAQ,aAAO;AAEpB,QAAI,KAAK;AACT,QAAI,GAAG,WAAW;AAEhB,YAAMC,QAAO,GAAG;AAChB,UAAI;AAAK,QAAAA,MAAK,OAAO,EAAE,GAAGA,MAAK,MAAM,GAAG,IAAG;AAC3C,UAAI,IAAI,aAAa;AAAW,QAAAA,MAAK,KAAK,IAAI,QAAQ;AACtD,aAAOA;;AAIT,UAAM,eAAe,OAAO,UAAU,SAAS,YAAY;AAC3D,QAAI,CAAC,gBAAgB,WAAU,aAAa;AAC1C,UAAI,WAAU,aAAa;AACzB,aAAK,WAAU,YAAY,QAAQ,KAAK,MAAM,IAAI;aAC7C;AACL,aAAK,MAAM,UAAU,CAAC,cAAc,IAAI,KAAK,GAAG,MAAM;;;AAK1D,UAAM,OAAO,WAAU,KAAK,KAAK,EAAE;AACnC,WAAO;EACT;;;;;EAMA,OAAO,eAAe,aAAmC;AACvD,eAAU,cAAc;EAC1B;;EAqDA,IAAW,cAAW;AACpB,QAAI,CAAC,KAAK,cAAc;AACtB,WAAK,eAAe,MAAM,UAAU,CAAC,KAAK,KAAK,kBAAkB,aAAa,WAAW,KAAK,KAAK,SAAS,CAAC;AAC7G,YAAM,mBAAmB,MAAM,UAAU,CAAC,qBAAqB,GAAG,KAAK,YAAY;AACnF,UAAI,KAAK,KAAK,iBAAiB;AAC7B,yBAAiB,cAAc,KAAK,KAAK;;;AAG7C,WAAO,KAAK;EACd;;;;;;EA2BA,YAA0B,IAA4B,OAAyB,CAAA,GAAE;AAAvD,SAAA,KAAA;AAA4B,SAAA,OAAA;AA7C/C,SAAA,iBAAiB,MAAM;AAwBvB,SAAA,kBAAkB,CAAA;AAQf,SAAA,gBAAgB;AAIhB,SAAA,gBAA+B,EAAE,QAAQ,GAAG,QAAQ,GAAG,SAAS,GAAG,SAAS,EAAC;AAUrF,OAAG,YAAY;AACf,SAAK,OAAO,OAAO,QAAQ,CAAA;AAE3B,QAAI,CAAC,GAAG,UAAU,SAAS,YAAY,GAAG;AACxC,WAAK,GAAG,UAAU,IAAI,YAAY;;AAIpC,QAAI,KAAK,KAAK;AACZ,WAAK,SAAS,KAAK,SAAS,KAAK;AACjC,aAAO,KAAK;;AAEd,UAAM,UAAU,MAAM,SAAS,GAAG,aAAa,QAAQ,CAAC;AAGxD,QAAI,KAAK,WAAW,QAAQ;AAC1B,aAAO,KAAK;;AAGd,QAAI,KAAK,2BAA2B,QAAW;AAC5C,WAAkC,0BAA0B,KAAK;;AAIpE,UAAM,OAAO,KAAK;AAClB,QAAI,MAAM;AACR,YAAM,KAAK,KAAK;AAChB,UAAI,CAAC,KAAK,eAAe,CAAC,IAAI,QAAQ;AACpC,eAAO,KAAK;aACP;AACL,aAAK,YAAY,KAAK,aAAa;AACnC,YAAI,IAAI,SAAS;AAAG,aAAG,KAAK,CAAC,GAAG,OAAO,EAAE,KAAK,MAAM,EAAE,KAAK,EAAE;;;AAKjE,UAAM,WAA6B;MACjC,GAAG,MAAM,UAAU,YAAY;MAC/B,QAAQ,MAAM,SAAS,GAAG,aAAa,WAAW,CAAC,KAAK,aAAa;MACrE,QAAQ,UAAU,UAAU,MAAM,SAAS,GAAG,aAAa,YAAY,CAAC,KAAK,aAAa;MAC1F,QAAQ,UAAU,UAAU,MAAM,SAAS,GAAG,aAAa,YAAY,CAAC,KAAK,aAAa;MAC1F,YAAY,MAAM,OAAO,GAAG,aAAa,WAAW,CAAC,KAAK,aAAa;MACvE,eAAe,MAAM,OAAO,GAAG,aAAa,oBAAoB,CAAC,KAAK;MACtE,WAAW;QACT,SAAS,KAAK,cAAc,MAAM,KAAK,cAAe,KAAK,SAAS,KAAK,SAAS,OAAQ,aAAa,UAAU;;MAEnH,kBAAkB;QAChB,QAAQ,KAAK,aAAa,aAAa,iBAAiB;QACxD,SAAS,aAAa,iBAAiB;;;AAG3C,QAAI,GAAG,aAAa,YAAY,GAAG;AACjC,eAAS,UAAU,MAAM,OAAO,GAAG,aAAa,YAAY,CAAC;;AAG/D,WAAO,MAAM,SAAS,MAAM,QAAQ;AACpC,SAAK,YAAW;AAGhB,SAAK,mBAAkB;AACvB,SAAK,iBAAiB,IAAI;AAE1B,QAAI,KAAK,QAAQ,QAAQ;AACvB,WAAK,MAAO,GAAG,MAAM,cAAc;;AAErC,QAAI,KAAK,KAAK;AACZ,WAAK,GAAG,UAAU,IAAI,gBAAgB;;AAIxC,UAAM,iBAAsC,KAAK,GAAG,QAAQ,MAAM,aAAa,SAAS;AACxF,UAAM,aAAa,gBAAgB;AACnC,QAAI,YAAY;AACd,iBAAW,UAAU;AACrB,WAAK,iBAAiB;AACtB,WAAK,GAAG,UAAU,IAAI,mBAAmB;AACzC,iBAAW,GAAG,UAAU,IAAI,qBAAqB;;AAGnD,SAAK,oBAAqB,KAAK,eAAe;AAC9C,QAAI,KAAK,qBAAqB,KAAK,eAAe,WAAW;AAE3D,WAAK,WAAW,MAAS;WACpB;AAEL,UAAI,OAAO,KAAK,cAAc,YAAY,KAAK,kBAAkB,KAAK,mBAAmB,aAAa,gBAAgB;AACpH,aAAK,aAAa,KAAK,aAAa,KAAK;AACzC,eAAO,KAAK;;AAEd,YAAM,MAAM,KAAK;AACjB,aAAO,KAAK;AACZ,WAAK,WAAW,GAAG;;AAIrB,QAAI,KAAK,2BAA2B,UAAU;AAC5C,WAAK,yBAAyB;;AAGhC,SAAK,gBAAe;AAEpB,UAAM,cAAc,KAAK,eAAe,WAAU,eAAe;AACjE,SAAK,SAAS,IAAI,YAAY;MAC5B,QAAQ,KAAK,UAAS;MACtB,OAAO,KAAK;MACZ,QAAQ,KAAK;MACb,UAAU,CAAC,YAAW;AACpB,gBAAQ,QAAQ,OAAI;AAClB,gBAAMC,MAAK,EAAE;AACb,cAAI,CAACA;AAAI;AACT,cAAI,EAAE,YAAY;AAChB,gBAAIA;AAAI,cAAAA,IAAG,OAAM;AACjB,mBAAO,EAAE;iBACJ;AACL,iBAAK,cAAcA,KAAI,CAAC;;QAE5B,CAAC;AACD,aAAK,uBAAsB;MAC7B;KACD;AAED,QAAI,KAAK,MAAM;AACb,WAAK,YAAW;AAChB,WAAK,OAAO,WAAW;AACvB,WAAK,aAAY,EAAG,QAAQ,CAAAA,QAAM,KAAK,gBAAgBA,GAAE,CAAC;AAC1D,aAAO,KAAK,OAAO;AACnB,WAAK,YAAY,KAAK;;AAIxB,QAAI,KAAK,UAAU;AACjB,YAAM,WAAW,KAAK;AACtB,aAAO,KAAK;AACZ,UAAI,SAAS;AAAQ,aAAK,KAAK,QAAQ;;AAGzC,SAAK,aAAY;AAGjB,QAAI,KAAK,kBAAkB,CAAC,UAAU;AAAW,gBAAU,YAAY;AACvE,QAAI,KAAK,WAAW,UAAU;AAAW,gBAAU,YAAY,KAAK,UAAU;AAE9E,SAAK,iBAAgB;AACrB,SAAK,mBAAkB;AACvB,SAAK,mBAAkB;EACzB;EAEQ,iBAAiB,OAAyB,KAAK,MAAI;AACzD,SAAK,GAAG,UAAU,IAAI,QAAQ,KAAK,MAAM;AACzC,QAAI,OAAO,KAAK,WAAW;AAAU,WAAK,GAAG,MAAM,YAAY,qBAAqB,GAAG,MAAI,KAAK,MAAM,GAAG;EAC3G;;;;;;;;;;;;;;EAeO,UAAU,GAAkB;AACjC,QAAI,OAAO,MAAM,UAAU;AAAE,cAAQ,MAAM,uEAAuE;AAAG;;AACrH,QAAK,EAAkB,cAAc;AAAE,cAAQ,MAAM,mFAAmF;AAAG,aAAO,KAAK,WAAW,CAAgB;;AAElL,QAAI;AACJ,QAAI,OAAsB;AAC1B,SAAK,OAAO;AACZ,QAAI,MAAM,IAAI;AACZ,WAAK,KAAK;eACD,WAAU,aAAa;AAChC,WAAK,WAAU,YAAY,KAAK,IAAI,GAAG,MAAM,KAAK;WAC7C;AACL,WAAK,KAAK,iBAAiB,IAAI;;AAGjC,QAAI,CAAC;AAAI;AAGT,WAAO,GAAG;AACV,QAAI,QAAQ,GAAG,kBAAkB,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK,OAAK,EAAE,QAAQ,KAAK,GAAG;AAAG,aAAO;AAKpG,UAAM,UAAU,KAAK,UAAU,EAAE;AACjC,UAAM,SAAS,GAAG,OAAO;AACzB,SAAK,OAAO,YAAY,CAAC;AAGzB,SAAK,GAAG,YAAY,EAAE;AAEtB,SAAK,WAAW,IAAI,CAAC;AAErB,WAAO;EACT;;EAGO,iBAAiB,GAAgB;AACtC,UAAM,KAAK,MAAM,UAAU,CAAC,mBAAmB,KAAK,KAAK,SAAS,CAAC;AACnE,UAAM,OAAO,MAAM,UAAU,CAAC,yBAAyB,GAAG,EAAE;AAE5D,QAAI,MAAM,SAAS,CAAC,GAAG;AACrB,UAAI,CAAC,EAAE,mBAAmB;AACxB,UAAE,oBAAoB,IAAI,qBAAqB,CAAC,CAAC,KAAK,MAAK;AAAG,cAAI,MAAM,gBAAgB;AACtF,cAAE,mBAAmB,WAAU;AAC/B,mBAAO,EAAE;AACT,uBAAU,SAAS,MAAM,CAAC;AAC1B,cAAE,MAAM,gBAAgB,EAAE,EAAE;;QAC7B,CAAC;AACF,eAAO,WAAW,MAAM,EAAE,mBAAmB,QAAQ,EAAE,CAAC;;;AAErD,iBAAU,SAAS,MAAM,CAAC;AAEjC,WAAO;EACT;;;;;;;;;;EAWO,YAAY,IAAyB,KAAwB,WAA2B,cAAc,MAAI;AAC/G,QAAI,OAAO,GAAG;AACd,QAAI,CAAC,MAAM;AACT,aAAO,KAAK,WAAW,EAAE,EAAE;;AAE7B,QAAI,KAAK,SAAS;AAAI,aAAO,KAAK;AAGlC,QAAI;AACJ,QAAI,OAAkB;AACtB,WAAO,QAAQ,CAAC,iBAAiB;AAC/B,wBAAkB,KAAK,MAAM;AAC7B,aAAO,KAAK,gBAAgB;;AAG9B,UAAM,MAAM,UAAU;;MAEpB,GAAG,KAAK;MAAM,IAAI;MAAW,UAAU;MAAW,QAAQ;MAAQ,YAAY;MAAW,QAAQ;MAAQ,aAAa;MACtH,GAAI,mBAAmB,CAAA;MACvB,GAAI,OAAO,KAAK,eAAe,CAAA;KAChC;AACD,SAAK,cAAc;AAGnB,QAAI;AACJ,QAAI,IAAI,WAAW,QAAQ;AACzB,mBAAa;AACb,UAAI,SAAS,KAAK,IAAI,KAAK,KAAK,GAAG,WAAW,KAAK,CAAC;AACpD,aAAO,IAAI;;AAIb,QAAI,UAAU,KAAK,GAAG,cAAc,0BAA0B;AAC9D,QAAI;AACJ,QAAI;AACJ,QAAI,aAAa;AACf,WAAK,UAAU,KAAK,EAAE;AACtB,mBAAa,EAAE,GAAG,MAAM,GAAG,GAAG,GAAG,EAAC;AAClC,YAAM,sBAAsB,UAAU;AACtC,aAAO,WAAW;AAClB,UAAI,KAAK,SAAS;AAChB,mBAAW,UAAU,KAAK;AAC1B,eAAO,KAAK;;AAEd,UAAI,WAAU,aAAa;AACzB,kBAAU,WAAU,YAAY,KAAK,IAAI,YAAY,MAAM,KAAK;aAC3D;AACL,kBAAU,MAAM,UAAU,CAAC,iBAAiB,CAAC;AAC7C,gBAAQ,YAAY,OAAO;AAC3B,kBAAU,MAAM,UAAU,CAAC,yBAAyB,GAAG,KAAK,EAAE;;AAEhE,WAAK,gBAAgB,KAAK,EAAE;;AAI9B,QAAI,WAAW;AACb,YAAM,IAAI,aAAa,IAAI,SAAS,KAAK;AACzC,YAAM,IAAI,KAAK,IAAI,UAAU;AAC7B,YAAM,QAAQ,KAAK,GAAG;AACtB,YAAM,aAAa;AACnB,WAAK,OAAO,KAAK,IAAI,EAAE,GAAG,EAAC,CAAE;AAC7B,iBAAW,MAAM,MAAM,aAAa,IAAI;;AAG1C,UAAM,UAAU,KAAK,UAAU,WAAU,QAAQ,SAAS,GAAG;AAC7D,QAAI,WAAW;AAAS,cAAQ,UAAU;AAC1C,QAAI;AAAY,cAAQ,cAAc;AAGtC,QAAI,aAAa;AACf,cAAQ,WAAW,SAAS,UAAU;;AAIxC,QAAI,WAAW;AACb,UAAI,UAAU,SAAS;AAErB,eAAO,WAAW,MAAM,MAAM,mBAAmB,UAAU,QAAQ,cAAc,QAAQ,EAAE,GAAG,CAAC;aAC1F;AACL,gBAAQ,WAAW,KAAK,IAAI,IAAI;;;AAKpC,SAAK,qBAAqB,OAAO,IAAI;AAErC,WAAO;EACT;;;;;EAMO,gBAAgB,iBAA+B;AACpD,UAAM,QAAQ,KAAK,gBAAgB;AACnC,QAAI,CAAC;AAAO;AAEZ,UAAM,YAAW;AACjB,UAAM,aAAa,KAAK,eAAe,IAAI,MAAM,IAAI;AACrD,SAAK,OAAO,MAAM,QAAQ,OAAI;AAE5B,QAAE,KAAK,KAAK,eAAe;AAC3B,QAAE,KAAK,KAAK,eAAe;AAC3B,YAAM,WAAW,EAAE,IAAI,CAAC;IAC1B,CAAC;AACD,UAAM,YAAY,KAAK;AACvB,QAAI,KAAK;AAAgB,aAAO,KAAK,eAAe;AACpD,WAAO,KAAK;AAGZ,QAAI,iBAAiB;AACnB,aAAO,WAAW,MAAM,MAAM,mBAAmB,gBAAgB,QAAQ,cAAc,MAAM,EAAE,GAAG,CAAC;;EAEvG;;;;;;;;;;EAWO,KAAK,cAAc,MAAM,cAAc,OAAO,SAAS,WAAU,QAAM;AAE5E,UAAM,OAAO,KAAK,OAAO,KAAK,aAAa,MAAM;AAGjD,SAAK,QAAQ,OAAI;AACf,UAAI,eAAe,EAAE,MAAM,CAAC,EAAE,WAAW,CAAC,QAAQ;AAChD,cAAM,cAAc,EAAE,GAAG,cAAc,0BAA0B;AACjE,UAAE,UAAU,aAAa;AACzB,YAAI,CAAC,EAAE;AAAS,iBAAO,EAAE;aACpB;AACL,YAAI,CAAC,eAAe,CAAC,QAAQ;AAAE,iBAAO,EAAE;;AAExC,YAAI,EAAE,SAAS,IAAI;AACjB,gBAAM,YAAY,EAAE,QAAQ,KAAK,aAAa,aAAa,MAAM;AACjE,YAAE,cAAe,cAAc,YAAY,EAAE,UAAU,UAAS;AAChE,iBAAO,EAAE;;;AAGb,aAAO,EAAE;IACX,CAAC;AAGD,QAAI,aAAa;AACf,YAAM,IAA8B,MAAM,UAAU,KAAK,IAAI;AAE7D,UAAI,EAAE,iBAAiB,EAAE,aAAa,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa;AACrG,UAAE,SAAS,EAAE;AACb,eAAO,EAAE;AAAW,eAAO,EAAE;AAAa,eAAO,EAAE;AAAc,eAAO,EAAE;;AAE5E,UAAI,EAAE,SAAS,KAAK,GAAG,MAAM,cAAc,QAAQ;AAAE,UAAE,MAAM;;AAC7D,UAAI,KAAK,mBAAmB;AAC1B,UAAE,aAAa;;AAEjB,UAAI,KAAK,aAAa;AACpB,UAAE,SAAS;;AAEb,YAAM,WAAW,EAAE;AACnB,aAAO,EAAE;AACT,UAAI,aAAa,QAAW;AAC1B,UAAE,yBAAyB;aACtB;AACL,eAAO,EAAE;;AAEX,YAAM,sBAAsB,GAAG,YAAY;AAC3C,QAAE,WAAW;AACb,aAAO;;AAGT,WAAO;EACT;;;;;;;;;;;EAYO,KAAK,OAA0B,YAAoC,WAAU,eAAe,MAAI;AACrG,YAAQ,MAAM,UAAU,KAAK;AAC7B,UAAM,SAAS,KAAK,UAAS;AAG7B,UAAM,QAAQ,OAAI;AAAG,QAAE,IAAI,EAAE,KAAK;AAAG,QAAE,IAAI,EAAE,KAAK;IAAE,CAAC;AAGrD,YAAQ,MAAM,KAAK,KAAK;AAExB,SAAK,OAAO,kBAAkB,KAAK,2BAA2B;AAI9D,QAAI,YAAY;AAChB,UAAM,QAAQ,OAAI;AAAG,kBAAY,KAAK,IAAI,YAAY,EAAE,KAAK,KAAK,EAAE,CAAC;IAAE,CAAC;AACxE,QAAI,YAAY,KAAK,OAAO;AAAe,WAAK,OAAO,gBAAgB;AACvE,QAAI,YAAY,QAAQ;AAEtB,UAAI,KAAK,OAAO,MAAM,WAAW,KAAK,KAAK,gBAAgB;AACzD,aAAK,OAAO,QAAQ;AACpB,aAAK,OAAO,cAAc,WAAW,QAAQ,KAAK,cAAc;AAChE,gBAAQ,KAAK,OAAO;AACpB,aAAK,OAAO,QAAQ,CAAA;AACpB,eAAO,KAAK;;AACP,aAAK,OAAO,YAAY,OAAO,WAAW,IAAI;;AAIvD,UAAM,SAAS,WAAU;AACzB,QAAI,OAAQ,cAAe;AAAY,iBAAU,cAAc;AAE/D,UAAM,UAA2B,CAAA;AACjC,SAAK,YAAW;AAGhB,UAAM,QAAQ,CAAC,KAAK,OAAO,MAAM;AACjC,UAAM,SAAS,SAAS,KAAK,KAAK;AAClC,QAAI;AAAQ,WAAK,aAAa,KAAK;AAGnC,QAAI,CAAC,SAAS,WAAW;AACvB,YAAM,YAAY,CAAC,GAAG,KAAK,OAAO,KAAK;AACvC,gBAAU,QAAQ,OAAI;AACpB,YAAI,CAAC,EAAE;AAAI;AACX,cAAM,OAAO,MAAM,KAAK,OAAO,EAAE,EAAE;AACnC,YAAI,CAAC,MAAM;AACT,cAAI,WAAU;AAAa,uBAAU,YAAY,KAAK,IAAI,GAAG,OAAO,KAAK;AACzE,kBAAQ,KAAK,CAAC;AACd,eAAK,aAAa,EAAE,IAAI,MAAM,KAAK;;MAEvC,CAAC;;AAKH,SAAK,OAAO,WAAW;AACvB,UAAM,cAAiC,CAAA;AACvC,SAAK,OAAO,QAAQ,KAAK,OAAO,MAAM,OAAO,OAAI;AAC/C,UAAI,MAAM,KAAK,OAAO,EAAE,EAAE,GAAG;AAAE,oBAAY,KAAK,CAAC;AAAG,eAAO;;AAC3D,aAAO;IACT,CAAC;AACD,UAAM,QAAQ,OAAI;AAChB,YAAM,OAAO,MAAM,KAAK,aAAa,EAAE,EAAE;AACzC,UAAI,MAAM;AAER,YAAI,MAAM,oBAAoB,IAAI;AAAG,YAAE,IAAI,KAAK;AAEhD,aAAK,OAAO,aAAa,CAAC;AAC1B,YAAI,EAAE,gBAAgB,EAAE,MAAM,UAAa,EAAE,MAAM,QAAW;AAC5D,YAAE,IAAI,EAAE,KAAK,KAAK;AAClB,YAAE,IAAI,EAAE,KAAK,KAAK;AAClB,eAAK,OAAO,kBAAkB,CAAC;;AAIjC,aAAK,OAAO,MAAM,KAAK,IAAI;AAC3B,YAAI,MAAM,QAAQ,MAAM,CAAC,KAAK,KAAK,OAAO,MAAM,SAAS,GAAG;AAC1D,eAAK,SAAS,MAAM,EAAE,GAAG,GAAG,cAAc,KAAI,CAAE;AAChD,gBAAM,QAAQ,GAAG,IAAI;;AAGvB,aAAK,OAAO,KAAK,IAAI,CAAC;AAEtB,YAAI,EAAE,aAAa,UAAU;AAC3B,gBAAM,MAAM,KAAK,GAAG,cAAc,aAAa;AAC/C,cAAI,OAAO,IAAI,WAAW;AACxB,gBAAI,UAAU,KAAK,EAAE,YAAY,QAAQ;;;iBAGpC,WAAW;AACpB,aAAK,UAAU,CAAC;;IAEpB,CAAC;AAED,WAAO,KAAK,OAAO;AACnB,SAAK,OAAO,eAAe;AAC3B,SAAK,YAAY,KAAK;AAGtB,WAAO,KAAK;AACZ,WAAO,KAAK,OAAO;AACnB,aAAS,WAAU,cAAc,SAAS,OAAO,WAAU;AAC3D,QAAI;AAAQ,WAAK,aAAa,MAAM,IAAI;AACxC,WAAO;EACT;;;;;EAMO,YAAY,OAAO,MAAI;AAC5B,SAAK,OAAO,YAAY,IAAI;AAC5B,QAAI,CAAC,MAAM;AACT,WAAK,uBAAsB;AAC3B,WAAK,oBAAmB;AACxB,WAAK,iBAAgB;AACrB,WAAK,oBAAmB;;AAE1B,WAAO;EACT;;;;EAKO,cAAc,aAAa,OAAK;AACrC,QAAI,KAAK,KAAK,cAAc,KAAK,KAAK,eAAe,WAClD,CAAC,cAAc,CAAC,KAAK,KAAK,kBAAkB,KAAK,KAAK,mBAAmB,OAAO;AACjF,aAAO,KAAK,KAAK;;AAGnB,QAAI,KAAK,KAAK,mBAAmB,OAAO;AACtC,aAAQ,KAAK,KAAK,aAAwB,WAAW,iBAAiB,SAAS,eAAe,EAAE,QAAQ;;AAE1G,QAAI,KAAK,KAAK,mBAAmB,MAAM;AACrC,aAAQ,KAAK,KAAK,aAAwB,WAAW,iBAAiB,KAAK,EAAE,EAAE,QAAQ;;AAEzF,QAAI,KAAK,KAAK,mBAAmB,MAAM;AAErC,aAAQ,KAAK,KAAK,cAAyB,KAAK;;AAElD,QAAI,KAAK,KAAK,mBAAmB,MAAM;AACrC,aAAQ,KAAK,KAAK,cAAyB,KAAK,QAAQ;;AAG1D,UAAM,KAAK,KAAK,GAAG,cAAc,MAAM,KAAK,KAAK,SAAS;AAC1D,QAAI,IAAI;AACN,YAAM,IAAI,MAAM,SAAS,GAAG,aAAa,MAAM,CAAC,KAAK;AACrD,aAAO,KAAK,MAAM,GAAG,eAAe,CAAC;;AAGvC,UAAM,OAAO,SAAS,KAAK,GAAG,aAAa,gBAAgB,CAAC;AAC5D,WAAO,OAAO,KAAK,MAAM,KAAK,GAAG,sBAAqB,EAAG,SAAS,IAAI,IAAI,KAAK,KAAK;EACtF;;;;;;;;;;;;;;EAeO,WAAW,KAAoB;AAGpC,QAAI,QAAQ,QAAW;AACrB,UAAI,KAAK,uBAAuB,QAAQ,SAAS;AAC/C,aAAK,oBAAqB,QAAQ;AAClC,aAAK,mBAAkB;;;AAG3B,QAAI,QAAQ,aAAa,QAAQ,QAAQ;AAAE,YAAM;;AAGjD,QAAI,QAAQ,QAAW;AACrB,YAAM,aAAa,CAAG,KAAK,KAAK,cAA0B,KAAK,KAAK,aAC/D,KAAK,KAAK,YAAwB,KAAK,KAAK;AACjD,YAAM,KAAK,UAAS,IAAK;;AAG3B,UAAM,OAAO,MAAM,YAAY,GAAG;AAClC,QAAI,KAAK,KAAK,mBAAmB,KAAK,QAAQ,KAAK,KAAK,eAAe,KAAK,GAAG;AAC7E,aAAO;;AAET,SAAK,KAAK,iBAAiB,KAAK;AAChC,SAAK,KAAK,aAAa,KAAK;AAG5B,SAAK,GAAG,MAAM,YAAY,oBAAoB,GAAG,KAAK,KAAK,UAAU,GAAG,KAAK,KAAK,cAAc,EAAE;AAClG,SAAK,uBAAsB;AAC3B,SAAK,qBAAoB;AAEzB,WAAO;EACT;;EAGO,YAAS;AACd,WAAO,KAAK,kBAAiB,IAAK,KAAK,UAAS;EAClD;;EAEU,kBAAkB,gBAAgB,OAAK;AAG/C,WAAO,iBAAiB,KAAK,KAAK,YAAY,sBAAsB,OAAO,aAAc,KAAK,GAAG,eAAe,KAAK,GAAG,cAAc,eAAe,OAAO;EAC9J;;EAEU,qBAAkB;AAC1B,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI,CAAC,QAAS,CAAC,KAAK,eAAe,CAAC,KAAK,aAAa;AAAS,aAAO;AACtE,UAAM,SAAS,KAAK,UAAS;AAC7B,QAAI,YAAY;AAChB,UAAM,IAAI,KAAK,kBAAkB,IAAI;AACrC,QAAI,KAAK,aAAa;AACpB,kBAAY,KAAK,IAAI,KAAK,MAAM,IAAI,KAAK,WAAW,KAAK,GAAG,KAAK,SAAS;WACrE;AAEL,kBAAY,KAAK;AACjB,UAAI,IAAI;AACR,aAAO,IAAI,KAAK,YAAY,UAAU,KAAK,KAAK,YAAY,CAAC,EAAE,GAAG;AAChE,oBAAY,KAAK,YAAY,GAAG,EAAE,KAAK;;;AAG3C,QAAI,cAAc,QAAQ;AACxB,YAAM,KAAK,KAAK,aAAa,KAAK,OAAK,EAAE,MAAM,SAAS;AACxD,WAAK,OAAO,WAAW,IAAI,UAAU,KAAK,MAAM;AAChD,aAAO;;AAET,WAAO;EACT;;;;;;;;EASO,QAAQ,SAAyB,WAAW,SAAS,MAAI;AAC9D,SAAK,OAAO,QAAQ,QAAQ,MAAM;AAClC,SAAK,oBAAmB;AACxB,WAAO;EACT;;;;;;;;EASO,OAAO,QAAgB,SAAwB,aAAW;AAC/D,QAAI,CAAC,UAAU,SAAS,KAAK,KAAK,KAAK,WAAW;AAAQ,aAAO;AAEjE,UAAM,YAAY,KAAK,UAAS;AAChC,SAAK,KAAK,SAAS;AACnB,QAAI,CAAC,KAAK,QAAQ;AAEhB,WAAK,iBAAiB;AACtB,aAAO;;AAGT,SAAK,OAAO,SAAS;AACrB,SAAK,GAAG,UAAU,OAAO,QAAQ,SAAS;AAC1C,SAAK,iBAAgB;AAGrB,SAAK,OAAO,cAAc,WAAW,QAAQ,MAAM;AACnD,QAAI,KAAK;AAAmB,WAAK,WAAU;AAE3C,SAAK,qBAAqB,IAAI;AAG9B,SAAK,2BAA2B;AAChC,SAAK,oBAAmB;AACxB,WAAO,KAAK;AAEZ,WAAO;EACT;;;;EAKO,YAAS;AAAa,WAAO,KAAK,KAAK;EAAkB;;EAGzD,eAAY;AACjB,WAAO,MAAM,KAAK,KAAK,GAAG,QAAQ,EAC/B,OAAO,CAAC,OAAoB,GAAG,QAAQ,MAAM,KAAK,KAAK,SAAS,KAAK,CAAC,GAAG,QAAQ,MAAM,KAAK,KAAK,gBAAgB,CAAC;EACvH;;EAGO,mBAAgB;AAAc,WAAO,KAAK;EAA0B;;;;;EAMpE,QAAQ,YAAY,MAAI;AAC7B,QAAI,CAAC,KAAK;AAAI;AACd,SAAK,OAAM;AACX,SAAK,mBAAmB,IAAI;AAC5B,SAAK,UAAU,MAAM,KAAK;AAC1B,SAAK,aAAa,KAAK;AACvB,QAAI,CAAC,WAAW;AACd,WAAK,UAAU,SAAS;AACxB,WAAK,GAAG,gBAAgB,gBAAgB;WACnC;AACL,WAAK,GAAG,WAAW,YAAY,KAAK,EAAE;;AAExC,QAAI,KAAK;AAAgB,aAAO,KAAK,eAAe;AACpD,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK,cAAc;AAC1B,WAAO,KAAK;AACZ,WAAO,KAAK;AACZ,WAAO,KAAK,GAAG;AACf,WAAO,KAAK;AACZ,WAAO;EACT;;;;EAKO,MAAM,KAAY;AACvB,QAAI,KAAK,KAAK,UAAU,KAAK;AAC3B,WAAK,KAAK,QAAQ,KAAK,OAAO,QAAQ;AACtC,WAAK,oBAAmB;;AAE1B,WAAO;EACT;;;;EAKO,WAAQ;AACb,WAAO,KAAK,OAAO;EACrB;;;;;;;;;;EAWO,iBAAiB,UAAyB,iBAAiB,OAAK;AACrE,UAAM,MAAM,KAAK,GAAG,sBAAqB;AAEzC,QAAI;AACJ,QAAI,gBAAgB;AAClB,qBAAe,EAAE,KAAK,IAAI,MAAM,SAAS,gBAAgB,WAAW,MAAM,IAAI,KAAI;WAE7E;AACL,qBAAe,EAAE,KAAK,KAAK,GAAG,WAAW,MAAM,KAAK,GAAG,WAAU;;AAGnE,UAAM,eAAe,SAAS,OAAO,aAAa;AAClD,UAAM,cAAc,SAAS,MAAM,aAAa;AAEhD,UAAM,cAAe,IAAI,QAAQ,KAAK,UAAS;AAC/C,UAAM,YAAa,IAAI,SAAS,SAAS,KAAK,GAAG,aAAa,gBAAgB,CAAC;AAE/E,WAAO,EAAE,GAAG,KAAK,MAAM,eAAe,WAAW,GAAG,GAAG,KAAK,MAAM,cAAc,SAAS,EAAC;EAC5F;;EAGO,SAAM;AACX,WAAO,KAAK,IAAI,KAAK,OAAO,OAAM,GAAI,KAAK,KAAK,UAAU,CAAC;EAC7D;;;;;;;;EASO,YAAY,GAAW,GAAW,GAAW,GAAS;AAC3D,WAAO,KAAK,OAAO,YAAY,GAAG,GAAG,GAAG,CAAC;EAC3C;;;;;;;;;;;;;;EAeO,WAAW,KAAuB,SAAyB;AAChE,UAAM,KAAK,WAAU,WAAW,GAAG;AACnC,QAAI,CAAC,MAAM,GAAG;AAAe,aAAO;AACpC,QAAI,CAAC,GAAG;AAAe,WAAK,GAAG,YAAY,EAAE;AAC7C,SAAK,gBAAgB,IAAI,MAAM,OAAO;AACtC,UAAM,OAAO,GAAG;AAEhB,SAAK,uBAAsB;AAG3B,QAAI,KAAK,aAAa;AACpB,WAAK,YAAY,IAAI,KAAK,aAAa,QAAW,KAAK;;AAKzD,QAAI;AACJ,QAAI,KAAK,KAAK,WAAW,KAAK,CAAC,KAAK,0BAA0B;AAC5D,qCAA+B,KAAK,2BAA2B;;AAEjE,SAAK,iBAAgB;AACrB,SAAK,oBAAmB;AACxB,QAAI;AAA8B,aAAO,KAAK;AAE9C,WAAO;EACT;EAuBO,GAAG,MAA+B,UAAuC;AAE9E,QAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,YAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,YAAM,QAAQ,CAAAC,UAAQ,KAAK,GAAGA,OAAM,QAAQ,CAAC;AAC7C,aAAO;;AAIT,QAAI,SAAS,YAAY,SAAS,WAAW,SAAS,aAAa,SAAS,YAAY,SAAS,WAAW;AAC1G,YAAM,SAAU,SAAS,YAAY,SAAS;AAC9C,UAAI,QAAQ;AACV,aAAK,gBAAgB,IAAI,IAAI,CAAC,UAAkB,SAAmC,KAAK;aACnF;AACL,aAAK,gBAAgB,IAAI,IAAI,CAAC,UAAsB;AAAE,cAAI,MAAM;AAAS,qBAAmC,OAAO,MAAM,MAAM;QAAC;;AAElI,WAAK,GAAG,iBAAiB,MAAM,KAAK,gBAAgB,IAAI,CAAC;eAChD,SAAS,UAAU,SAAS,eAAe,SAAS,cAAc,SAAS,iBAAiB,SAAS,YAC3G,SAAS,gBAAgB,SAAS,aAAa,SAAS,iBAAiB;AAG5E,WAAK,gBAAgB,IAAI,IAAI;WACxB;AACL,cAAQ,MAAM,kBAAkB,OAAO,uBAAuB;;AAEhE,WAAO;EACT;;;;;EAMO,IAAI,MAA6B;AAEtC,QAAI,KAAK,QAAQ,GAAG,MAAM,IAAI;AAC5B,YAAM,QAAQ,KAAK,MAAM,GAAG;AAC5B,YAAM,QAAQ,CAAAA,UAAQ,KAAK,IAAIA,KAAI,CAAC;AACpC,aAAO;;AAGT,QAAI,SAAS,YAAY,SAAS,WAAW,SAAS,aAAa,SAAS,YAAY,SAAS,WAAW;AAE1G,UAAI,KAAK,gBAAgB,IAAI,GAAG;AAC9B,aAAK,GAAG,oBAAoB,MAAM,KAAK,gBAAgB,IAAI,CAAC;;;AAGhE,WAAO,KAAK,gBAAgB,IAAI;AAEhC,WAAO;EACT;;EAGO,SAAM;AACX,WAAO,KAAK,KAAK,eAAe,EAAE,QAAQ,CAAC,QAAwB,KAAK,IAAI,GAAG,CAAC;AAChF,WAAO;EACT;;;;;;;EAQO,aAAa,KAAuB,YAAY,MAAM,eAAe,MAAI;AAC9E,QAAI,CAAC,KAAK;AAAE,cAAQ,MAAM,iDAAiD;AAAG,aAAO;;AAErF,eAAU,YAAY,GAAG,EAAE,QAAQ,QAAK;AACtC,UAAI,GAAG,iBAAiB,GAAG,kBAAkB,KAAK;AAAI;AACtD,UAAI,OAAO,GAAG;AAEd,UAAI,CAAC,MAAM;AACT,eAAO,KAAK,OAAO,MAAM,KAAK,OAAK,OAAO,EAAE,EAAE;;AAEhD,UAAI,CAAC;AAAM;AAEX,UAAI,aAAa,WAAU,aAAa;AACtC,mBAAU,YAAY,KAAK,IAAI,MAAM,OAAO,KAAK;;AAInD,aAAO,GAAG;AACV,WAAK,UAAU,EAAE;AAEjB,WAAK,OAAO,WAAW,MAAM,WAAW,YAAY;AAEpD,UAAI,aAAa,GAAG,eAAe;AACjC,WAAG,OAAM;;IAEb,CAAC;AACD,QAAI,cAAc;AAChB,WAAK,oBAAmB;AACxB,WAAK,oBAAmB;;AAE1B,WAAO;EACT;;;;;;EAOO,UAAU,YAAY,MAAM,eAAe,MAAI;AAEpD,SAAK,OAAO,MAAM,QAAQ,OAAI;AAC5B,UAAI,aAAa,WAAU,aAAa;AACtC,mBAAU,YAAY,KAAK,IAAI,GAAG,OAAO,KAAK;;AAEhD,aAAO,EAAE,GAAG;AACZ,UAAI,CAAC,KAAK,KAAK;AAAY,aAAK,UAAU,EAAE,EAAE;IAChD,CAAC;AACD,SAAK,OAAO,UAAU,WAAW,YAAY;AAC7C,QAAI;AAAc,WAAK,oBAAmB;AAC1C,WAAO;EACT;;;;;;EAOO,aAAa,YAAY,KAAK,KAAK,SAAS,OAAe;AAChE,QAAI,OAAO;AAET,iBAAW,MAAK;AAAG,YAAI,KAAK;AAAM,eAAK,aAAa,SAAS;MAAE,CAAC;eACvD,WAAW;AACpB,WAAK,GAAG,UAAU,IAAI,oBAAoB;WACrC;AACL,WAAK,GAAG,UAAU,OAAO,oBAAoB;;AAE/C,SAAK,KAAK,UAAU;AACpB,WAAO;EACT;;EAGQ,kBAAe;AAAc,WAAO,KAAK,GAAG,UAAU,SAAS,oBAAoB;EAAE;;;;;;;;EAStF,UAAU,KAAc,cAAc,MAAM,UAAU,MAAI;AAC/D,QAAI,CAAC,CAAC,KAAK,KAAK,eAAe;AAAK,aAAO;AAC3C,UAAM,KAAK,KAAK,aAAa,OAAO,OAAO,KAAK,KAAK;AACrD,SAAK,iBAAgB;AACrB,SAAK,mBAAkB;AACvB,SAAK,OAAO,MAAM,QAAQ,OAAI;AAC5B,WAAK,gBAAgB,EAAE,EAAE;AACzB,UAAI,EAAE,WAAW;AAAS,UAAE,QAAQ,UAAU,KAAK,aAAa,OAAO;IACzE,CAAC;AACD,QAAI,aAAa;AAAE,WAAK,gBAAe;;AACvC,WAAO;EACT;;;;;;EAOO,cAAc,GAAmB;AACtC,UAAM,OAAO,KAAK;AAClB,QAAI,MAAM;AAAM,aAAO;AACvB,QAAI,EAAE,kBAAkB,QAAW;AAAE,WAAK,gBAAgB,EAAE;AAAe,WAAK,mBAAkB;;AAClG,QAAI,EAAE,YAAY;AAAW,WAAK,aAAa,EAAE,OAAO;AACxD,QAAI,EAAE;AAAY,WAAK,WAAW,EAAE,UAAU;AAC9C,QAAI,EAAE,UAAU,UAAa,EAAE,UAAU,KAAK,OAAO;AAAE,UAAI,KAAK;AAAO,aAAK,GAAG,UAAU,OAAO,KAAK,KAAK;AAAG,UAAI,EAAE;AAAO,aAAK,GAAG,UAAU,IAAI,EAAE,KAAK;;AAEvJ,QAAI,EAAE,YAAY;AAChB,WAAK,KAAK,aAAa,EAAE;AACzB,WAAK,mBAAkB;eACd,EAAE,eAAe,QAAQ,KAAK,KAAK,YAAY;AACxD,aAAO,KAAK,KAAK;AACjB,WAAK,mBAAkB;eACd,OAAO,EAAE,WAAY;AAAU,WAAK,OAAO,EAAE,MAAM;AAC9D,QAAI,EAAE,WAAW;AAAW,WAAK,OAAO,EAAE,MAAM;AAChD,QAAI,EAAE,eAAe;AAAW,WAAK,UAAU,EAAE,UAAU;AAC3D,QAAI,EAAE,gBAAgB,UAAa,CAAC,EAAE;AAAY,WAAK,WAAW,CAAC,EAAE,WAAW;AAChF,QAAI,EAAE,kBAAkB,UAAa,CAAC,EAAE;AAAY,WAAK,aAAa,CAAC,EAAE,aAAa;AACtF,QAAI,EAAE,UAAU;AAAW,WAAK,MAAM,EAAE,KAAK;AAC7C,QAAI,EAAE,QAAQ,QAAW;AACvB,WAAK,SAAS,KAAK,SAAS,KAAK,MAAM,EAAE;AACzC,WAAK,uBAAsB;WACtB;AACL,UAAI,EAAE,WAAW,QAAW;AAAE,aAAK,SAAS,EAAE;AAAQ,aAAK,uBAAsB;;AACjF,UAAI,EAAE,WAAW;AAAW,aAAK,SAAS,EAAE;;AAE9C,QAAI,EAAE,UAAU;AAAQ,WAAK,KAAK,EAAE,QAAQ;AAG5C,WAAO;EACT;;;;;;EAOO,OAAO,KAAuB,KAAoB;AAEvD,eAAU,YAAY,GAAG,EAAE,QAAQ,QAAK;AACtC,YAAM,IAAI,IAAI;AACd,UAAI,CAAC;AAAG;AACR,YAAM,IAAI,EAAC,GAAG,MAAM,QAAQ,CAAA,GAAI,CAAC,GAAG,GAAG,MAAM,UAAU,GAAG,EAAC;AAC3D,WAAK,OAAO,aAAa,CAAC;AAC1B,aAAO,EAAE;AAGT,YAAM,OAAO,CAAC,KAAK,KAAK,KAAK,GAAG;AAChC,UAAI;AACJ,UAAI,KAAK,KAAK,OAAK,EAAE,CAAC,MAAM,UAAa,EAAE,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG;AACvD,YAAI,CAAA;AACJ,aAAK,QAAQ,OAAI;AACf,YAAE,CAAC,IAAK,EAAE,CAAC,MAAM,SAAa,EAAE,CAAC,IAAI,EAAE,CAAC;AACxC,iBAAO,EAAE,CAAC;QACZ,CAAC;;AAGH,UAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;AAChD,YAAI,CAAA;;AAIN,UAAI,EAAE,YAAY,QAAW;AAC3B,cAAM,cAAc,GAAG,cAAc,0BAA0B;AAC/D,YAAI,eAAe,YAAY,gBAAgB,EAAE,SAAS;AACxD,YAAE,UAAU,EAAE;AACd,qBAAU,SAAS,aAAa,CAAC;AAEjC,cAAI,EAAE,SAAS,IAAI;AACjB,wBAAY,YAAY,EAAE,QAAQ,EAAE;AACpC,cAAE,QAAQ,uBAAsB;;;AAGpC,eAAO,EAAE;;AAIX,UAAI,UAAU;AACd,UAAI,YAAY;AAChB,iBAAW,OAAO,GAAG;AACnB,YAAI,IAAI,CAAC,MAAM,OAAO,EAAE,GAAG,MAAM,EAAE,GAAG,GAAG;AACvC,YAAE,GAAG,IAAI,EAAE,GAAG;AACd,oBAAU;AACV,sBAAY,aAAc,CAAC,KAAK,KAAK,eAAe,QAAQ,cAAc,QAAQ,YAAY,QAAQ;;;AAG1G,YAAM,eAAe,CAAC;AAGtB,UAAI,GAAG;AACL,cAAM,eAAgB,EAAE,MAAM,UAAa,EAAE,MAAM,EAAE;AACrD,aAAK,SAAS,GAAG,CAAC;AAClB,YAAI,gBAAgB,EAAE,SAAS;AAE7B,YAAE,QAAQ,SAAS,KAAK,gBAAe,IAAK,EAAE,IAAI,MAAS;eACtD;AACL,eAAK,qBAAqB,cAAc,CAAC;;AAE3C,eAAO,EAAE;;AAEX,UAAI,KAAK,SAAS;AAChB,aAAK,WAAW,IAAI,CAAC;;AAEvB,UAAI,WAAW;AACb,aAAK,gBAAgB,EAAE,EAAE;;AAE3B,UAAI,WAAU;AAAU,mBAAU,SAAS,CAAC;IAC9C,CAAC;AAED,WAAO;EACT;EAEQ,SAAS,GAAkB,GAAoB;AACrD,UAAM,cAAc,EAAE;AACtB,QAAI,CAAC;AAAa,WAAK,OAAO,WAAU,EAAG,YAAY,CAAC;AACxD,SAAK,OAAO,SAAS,GAAG,CAAC;AACzB,SAAK,uBAAsB;AAC3B,QAAI,CAAC,aAAa;AAChB,WAAK,oBAAmB;AACxB,WAAK,OAAO,UAAS;;EAEzB;;;;;;;EAQO,gBAAgB,IAAuB;AAC5C,QAAI,CAAC;AAAI;AACT,OAAG,UAAU,OAAO,qBAAqB;AACzC,QAAI,CAAC,GAAG;AAAc;AACtB,UAAM,IAAI,GAAG;AACb,QAAI,CAAC;AAAG;AACR,UAAM,OAAO,EAAE;AACf,QAAI,CAAC,QAAQ,GAAG,kBAAkB,KAAK;AAAI;AAC3C,UAAM,OAAO,KAAK,cAAc,IAAI;AACpC,QAAI,CAAC;AAAM;AACX,QAAI,SAAS,EAAE,IAAI,EAAE,IAAI,OAAO,GAAG;AACnC,QAAI;AACJ,QAAI,EAAE;AAAuB,aAAO,GAAG,cAAc,EAAE,qBAAqB;AAC5E,QAAI,CAAC;AAAM,aAAO,GAAG,cAAc,WAAU,qBAAqB;AAClE,QAAI,CAAC;AAAM;AACX,UAAM,UAAU,GAAG,eAAe,KAAK;AACvC,UAAM,QAAQ,EAAE,IAAI,EAAE,IAAI,OAAO,UAAU,KAAK;AAChD,QAAI;AACJ,QAAI,EAAE,SAAS;AAEb,gBAAU,EAAE,QAAQ,OAAM,IAAK,EAAE,QAAQ,cAAc,IAAI;AAC3D,YAAM,SAAS,EAAE,QAAQ,GAAG,sBAAqB;AACjD,YAAM,YAAY,GAAG,sBAAqB;AAC1C,iBAAW,OAAO,MAAM,UAAU;eACzB,EAAE,aAAa,UAAU,QAAQ;AAE1C;WACK;AAEL,YAAM,QAAQ,KAAK;AACnB,UAAI,CAAC,OAAO;AACV,gBAAQ,MAAM,gDAAgD,EAAE,EAAE,KAAK,WAAU,qBAAqB,uFAAuF;AAC7L;;AAEF,gBAAU,MAAM,sBAAqB,EAAG,UAAU;;AAEpD,QAAI,UAAU;AAAS;AACvB,cAAU,UAAU;AACpB,QAAI,IAAI,KAAK,KAAK,SAAS,IAAI;AAE/B,UAAM,UAAU,OAAO,UAAU,EAAE,aAAa,IAAI,EAAE,gBAA0B;AAChF,QAAI,WAAW,IAAI,SAAS;AAC1B,UAAI;AACJ,SAAG,UAAU,IAAI,qBAAqB;;AAExC,QAAI,EAAE,QAAQ,IAAI,EAAE;AAAM,UAAI,EAAE;aACvB,EAAE,QAAQ,IAAI,EAAE;AAAM,UAAI,EAAE;AACrC,QAAI,MAAM,EAAE,GAAG;AACb,WAAK,2BAA2B;AAChC,WAAK,SAAS,GAAG,EAAE,EAAC,CAAE;AACtB,aAAO,KAAK;;EAEhB;;EAGQ,uBAAuB,IAAuB;AACpD,QAAI,WAAU;AAAmB,iBAAU,kBAAkB,EAAE;;AAC1D,WAAK,gBAAgB,EAAE;EAC9B;;;;;EAMO,OAAO,KAAuB,UAAmB;AACtD,eAAU,YAAY,GAAG,EAAE,QAAQ,QAAK;AACtC,YAAM,IAAI,GAAG;AACb,UAAI,CAAC,MAAM,aAAa,CAAC;AAAG;AAC5B,YAAM,MAAuB,EAAE,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,MAAM,EAAE,MAAM,MAAM,EAAE,MAAM,MAAM,EAAE,MAAM,MAAM,EAAE,KAAI;AAErG,UAAI,UAAU;AACZ,cAAM,SAAS,SAAS,OAAO,IAAI,KAAK,MAAM,SAAS,OAAO,KAAK,UAAS,CAAE,IAAI;AAClF,cAAM,SAAS,SAAS,MAAM,IAAI,KAAK,MAAM,SAAS,MAAO,KAAK,KAAK,UAAqB,IAAI;AAChG,YAAI,IAAI,EAAE,IAAI,UAAU,EAAE,KAAK,SAAO;AACtC,YAAI,IAAK,EAAE,IAAI,SAAU;;AAE3B,aAAO,KAAK,GAAG,EAAE,QAAQ,OAAI;AAAG,YAAI,IAAI,CAAC,MAAM;AAAW,iBAAO,IAAI,CAAC;MAAG,CAAC;AAC1E,YAAM,QAAQ,EAAE;AAChB,WAAK,OAAO,IAAI,GAAG;AACnB,QAAE,QAAQ;IACZ,CAAC;AACD,WAAO;EACT;;;;;EAMO,OAAO,OAAqB;AACjC,UAAM,eAAgB,OAAO,UAAU,YAAY,MAAM,MAAM,GAAG,EAAE,SAAS;AAE7E,QAAI,CAAC,cAAc;AACjB,YAAM,OAAO,MAAM,YAAY,KAAK;AACpC,UAAI,KAAK,KAAK,eAAe,KAAK,QAAQ,KAAK,KAAK,WAAW,KAAK;AAAG;;AAGzE,SAAK,KAAK,SAAS;AACnB,SAAK,KAAK,YAAY,KAAK,KAAK,eAAe,KAAK,KAAK,aAAa,KAAK,KAAK,cAAc;AAC9F,SAAK,YAAW;AAEhB,WAAO;EACT;;EAGO,YAAS;AAAa,WAAO,KAAK,KAAK;EAAkB;;;;;;;;;;;;;EAczD,UAAU,MAAqB;AAEpC,QAAI,UAAU,SAAS,GAAG;AACxB,cAAQ,KAAK,qHAAqH;AAElI,YAAM,IAAI;AAAW,UAAI,IAAI,GAC3B,IAAqB,EAAE,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,cAAc,EAAE,GAAG,EAAC;AACzF,aAAO,KAAK,UAAU,CAAC;;AAEzB,WAAO,KAAK,OAAO,UAAU,IAAI;EACnC;;EAGU,sBAAmB;AAC3B,QAAI,KAAK,OAAO;AAAW,aAAO;AAClC,UAAM,WAAW,KAAK,OAAO,cAAc,IAAI;AAC/C,QAAI,YAAY,SAAS,QAAQ;AAC/B,UAAI,CAAC,KAAK,0BAA0B;AAClC,aAAK,OAAO,mBAAmB,QAAQ;;AAEzC,WAAK,cAAc,UAAU,QAAQ;;AAEvC,SAAK,OAAO,YAAW;AACvB,WAAO;EACT;;EAGU,mBAAgB;AACxB,QAAI,KAAK,OAAO;AAAW,aAAO;AAClC,QAAI,KAAK,OAAO,YAAY,QAAQ;AAClC,UAAI,CAAC,KAAK,0BAA0B;AAClC,aAAK,OAAO,mBAAmB,KAAK,OAAO,UAAU;;AAGvD,WAAK,OAAO,WAAW,QAAQ,OAAI;AAAG,eAAO,EAAE;MAAQ,CAAC;AACxD,YAAM,aAAa,CAAC,GAAG,KAAK,OAAO,UAAU;AAC7C,WAAK,OAAO,aAAa,CAAA;AACzB,WAAK,cAAc,SAAS,UAAU;;AAExC,WAAO;EACT;;EAGO,sBAAmB;AACxB,QAAI,KAAK,OAAO;AAAW,aAAO;AAClC,QAAI,KAAK,OAAO,cAAc,QAAQ;AACpC,YAAM,eAAe,CAAC,GAAG,KAAK,OAAO,YAAY;AACjD,WAAK,OAAO,eAAe,CAAA;AAC3B,WAAK,cAAc,WAAW,YAAY;;AAE5C,WAAO;EACT;;EAGU,cAAc,MAAc,MAAsB;AAC1D,UAAM,QAAQ,OAAO,IAAI,YAAY,MAAM,EAAE,SAAS,OAAO,QAAQ,KAAI,CAAE,IAAI,IAAI,MAAM,IAAI;AAG7F,QAAI,OAAkB;AACtB,WAAO,KAAK;AAAgB,aAAO,KAAK,eAAe;AACvD,SAAK,GAAG,cAAc,KAAK;AAC3B,WAAO;EACT;;EAGU,yBAAsB;AAC9B,QAAI,CAAC,KAAK,UAAU,KAAK,OAAO;AAAW,aAAO;AAClD,UAAM,SAAS,KAAK;AACpB,QAAI,MAAM,KAAK,OAAM,IAAK,KAAK;AAC/B,UAAM,aAAa,KAAK,KAAK;AAC7B,UAAM,OAAO,KAAK,KAAK;AACvB,QAAI,CAAC;AAAY,aAAO;AAKxB,QAAI,CAAC,UAAU,CAAC,KAAK,KAAK,QAAQ;AAChC,YAAM,eAAe,MAAM,YAAY,iBAAiB,KAAK,EAAE,EAAE,WAAW,CAAC;AAC7E,UAAI,aAAa,IAAI,KAAK,aAAa,SAAS,MAAM;AACpD,cAAM,SAAS,KAAK,MAAM,aAAa,IAAI,UAAU;AACrD,YAAI,MAAM,QAAQ;AAChB,gBAAM;;;;AAKZ,SAAK,GAAG,aAAa,kBAAkB,OAAO,GAAG,CAAC;AAClD,SAAK,GAAG,MAAM,eAAe,YAAY;AACzC,SAAK,GAAG,MAAM,eAAe,QAAQ;AACrC,QAAI,KAAK;AAEP,WAAK,GAAG,MAAM,SAAS,cAAc,QAAQ,IAAI,MAAM,aAAa;;AAItE,QAAI,UAAU,MAAM,oBAAoB,MAAM,GAAG;AAC/C,aAAO,KAAK,uBAAuB,OAAO,EAAE;;AAG9C,WAAO;EACT;;EAGU,gBAAgB,IAAyB,kBAAkB,OAAO,MAAoB;AAC9F,WAAO,QAAQ,KAAK,UAAU,EAAE;AAChC,OAAG,gBAAgB;AACnB,SAAK,KAAK;AACV,SAAK,OAAO;AACZ,WAAO,KAAK,OAAO,QAAQ,MAAM,eAAe;AAGhD,SAAK,WAAW,IAAI,IAAI;AACxB,OAAG,UAAU,IAAI,aAAa,WAAW,KAAK,KAAK,SAAS;AAC5D,UAAM,gBAAgB,MAAM,oBAAoB,IAAI;AACpD,oBAAgB,GAAG,UAAU,IAAI,iBAAiB,IAAI,GAAG,UAAU,OAAO,iBAAiB;AAC3F,QAAI;AAAe,WAAK,qBAAqB,OAAO,IAAI;AAExD,QAAI,CAAC,MAAM,SAAS,IAAI;AAAG,WAAK,gBAAgB,KAAK,EAAE;AAEvD,WAAO;EACT;;EAGU,cAAc,IAAiB,GAAgB;AAEvD,QAAK,CAAC,EAAE,WAAW,CAAC,EAAE,aAAc,KAAK,iBAAiB,IAAI;AAE5D,SAAG,MAAM,MAAM,EAAE,IAAK,EAAE,MAAM,IAAI,0BAA0B,QAAQ,EAAE,CAAC,8BAA+B;AACtG,SAAG,MAAM,OAAO,EAAE,IAAK,EAAE,MAAM,IAAI,2BAA2B,QAAQ,EAAE,CAAC,+BAAgC;AACzG,SAAG,MAAM,QAAQ,EAAE,IAAI,IAAI,QAAQ,EAAE,CAAC,+BAA+B;AACrE,SAAG,MAAM,SAAS,EAAE,IAAI,IAAI,QAAQ,EAAE,CAAC,8BAA8B;;AAGvE,MAAE,IAAI,IAAI,GAAG,aAAa,QAAQ,OAAO,EAAE,CAAC,CAAC,IAAI,GAAG,gBAAgB,MAAM;AAC1E,MAAE,IAAI,IAAI,GAAG,aAAa,QAAQ,OAAO,EAAE,CAAC,CAAC,IAAI,GAAG,gBAAgB,MAAM;AAC1E,MAAE,IAAI,IAAI,GAAG,aAAa,QAAQ,OAAO,EAAE,CAAC,CAAC,IAAI,GAAG,gBAAgB,MAAM;AAC1E,MAAE,IAAI,IAAI,GAAG,aAAa,QAAQ,OAAO,EAAE,CAAC,CAAC,IAAI,GAAG,gBAAgB,MAAM;AAC1E,WAAO;EACT;;EAGU,WAAW,IAAiB,MAAmB;AACvD,QAAI,CAAC;AAAM,aAAO;AAClB,SAAK,cAAc,IAAI,IAAI;AAE3B,UAAM,QAA2C;;MAE/C,UAAU;MACV,QAAQ;MACR,QAAQ;MACR,IAAI;MACJ,eAAe;;AAEjB,eAAW,OAAO,OAAO;AACvB,UAAI,KAAK,GAAG,GAAG;AACb,WAAG,aAAa,MAAM,GAAG,GAAG,OAAO,KAAK,GAAG,CAAC,CAAC;aACxC;AACL,WAAG,gBAAgB,MAAM,GAAG,CAAC;;;AAGjC,WAAO;EACT;;EAGU,UAAU,IAAiB,mBAAmB,MAAI;AAC1D,UAAM,IAAmB,CAAA;AACzB,MAAE,IAAI,MAAM,SAAS,GAAG,aAAa,MAAM,CAAC;AAC5C,MAAE,IAAI,MAAM,SAAS,GAAG,aAAa,MAAM,CAAC;AAC5C,MAAE,IAAI,MAAM,SAAS,GAAG,aAAa,MAAM,CAAC;AAC5C,MAAE,IAAI,MAAM,SAAS,GAAG,aAAa,MAAM,CAAC;AAC5C,MAAE,eAAe,MAAM,OAAO,GAAG,aAAa,kBAAkB,CAAC;AACjE,MAAE,WAAW,MAAM,OAAO,GAAG,aAAa,cAAc,CAAC;AACzD,MAAE,SAAS,MAAM,OAAO,GAAG,aAAa,YAAY,CAAC;AACrD,MAAE,SAAS,MAAM,OAAO,GAAG,aAAa,WAAW,CAAC;AACpD,UAAM,OAAO,GAAG,aAAa,oBAAoB;AACjD,QAAI,MAAM;AACR,UAAI,SAAS,UAAU,SAAS;AAAS,UAAE,gBAAgB,MAAM,OAAO,IAAI;;AACvE,UAAE,gBAAgB,SAAS,MAAM,EAAE;;AAE1C,MAAE,KAAK,GAAG,aAAa,OAAO;AAG9B,MAAE,OAAO,MAAM,SAAS,GAAG,aAAa,UAAU,CAAC;AACnD,MAAE,OAAO,MAAM,SAAS,GAAG,aAAa,UAAU,CAAC;AACnD,MAAE,OAAO,MAAM,SAAS,GAAG,aAAa,UAAU,CAAC;AACnD,MAAE,OAAO,MAAM,SAAS,GAAG,aAAa,UAAU,CAAC;AAGnD,QAAI,kBAAkB;AACpB,UAAI,EAAE,MAAM;AAAG,WAAG,gBAAgB,MAAM;AACxC,UAAI,EAAE,MAAM;AAAG,WAAG,gBAAgB,MAAM;AACxC,UAAI,EAAE;AAAM,WAAG,gBAAgB,UAAU;AACzC,UAAI,EAAE;AAAM,WAAG,gBAAgB,UAAU;AACzC,UAAI,EAAE;AAAM,WAAG,gBAAgB,UAAU;AACzC,UAAI,EAAE;AAAM,WAAG,gBAAgB,UAAU;;AAI3C,eAAW,OAAO,GAAG;AACnB,UAAI,CAAC,EAAE,eAAe,GAAG;AAAG;AAC5B,UAAI,CAAC,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,KAAK,QAAQ,iBAAiB;AACtD,eAAO,EAAE,GAAG;;;AAIhB,WAAO;EACT;;EAGU,kBAAe;AACvB,UAAM,UAAU,CAAC,mBAAmB;AAEpC,QAAI,KAAK,KAAK,YAAY;AACxB,WAAK,GAAG,UAAU,IAAI,GAAG,OAAO;AAChC,WAAK,GAAG,aAAa,aAAa,MAAM;WACnC;AACL,WAAK,GAAG,UAAU,OAAO,GAAG,OAAO;AACnC,WAAK,GAAG,gBAAgB,WAAW;;AAGrC,WAAO;EACT;;;;;;EAOO,SAAS,cAAc,KAAK,IAAI,aAAW;AAChD,QAAI,CAAC;AAAa;AAClB,QAAI,KAAK,cAAc;AAAa;AACpC,SAAK,YAAY;AAGjB,SAAK,YAAW;AAGhB,QAAI,gBAAgB;AACpB,QAAI,KAAK,eAAe,KAAK,gBAAgB;AAC3C,UAAI,KAAK,KAAK,WAAW,KAAK,eAAe,GAAG;AAC9C,aAAK,OAAO,KAAK,eAAe,GAAG,KAAK,KAAK,UAAU,MAAM;AAC7D,wBAAgB;;WAEb;AAEL,sBAAgB,KAAK,mBAAkB;;AAIzC,QAAI,KAAK;AAAmB,WAAK,WAAU;AAG3C,SAAK,OAAO,MAAM,QAAQ,OAAI;AAC5B,UAAI,EAAE;AAAS,UAAE,QAAQ,SAAQ;IACnC,CAAC;AAED,QAAI,CAAC,KAAK;AAAoB,WAAK,qBAAqB,aAAa;AACrE,WAAO,KAAK;AAEZ,SAAK,YAAY,KAAK;AAEtB,WAAO;EACT;;EAGQ,qBAAqB,QAAQ,OAAO,IAAmB,QAAS;AACtE,QAAI,CAAC,KAAK;AAAQ;AAIlB,QAAI,SAAS,KAAK,gBAAe;AAAI,aAAO,WAAW,MAAM,KAAK,qBAAqB,OAAO,CAAC,GAAG,KAAK,cAAc;AAErH,QAAI,GAAG;AACL,UAAI,MAAM,oBAAoB,CAAC;AAAG,aAAK,uBAAuB,EAAE,EAAE;eACzD,KAAK,OAAO,MAAM,KAAK,CAAAC,OAAK,MAAM,oBAAoBA,EAAC,CAAC,GAAG;AACpE,YAAM,QAAQ,CAAC,GAAG,KAAK,OAAO,KAAK;AACnC,WAAK,YAAW;AAChB,YAAM,QAAQ,CAAAA,OAAI;AAChB,YAAI,MAAM,oBAAoBA,EAAC;AAAG,eAAK,uBAAuBA,GAAE,EAAE;MACpE,CAAC;AACD,WAAK,2BAA2B;AAChC,WAAK,YAAY,KAAK;AACtB,WAAK,2BAA2B;;AAGlC,QAAI,KAAK,gBAAgB,eAAe;AAAG,WAAK,gBAAgB,eAAe,EAAE,MAAM,IAAI,CAAC,CAAC,IAAI,KAAK,OAAO,KAAK;EACpH;;EAGU,mBAAmB,cAAc,OAAK;AAG9C,UAAM,YAAY,CAAC,KAAK,mBAAmB,KAAK,qBAAqB,KAAK,KAAK,iBAAiB,KAAK,KAAK,cACrG,KAAK,OAAO,MAAM,KAAK,OAAK,EAAE,aAAa;AAEhD,QAAI,CAAC,eAAe,aAAa,CAAC,KAAK,gBAAgB;AACrD,WAAK,gBAAgB,MAAM,SAAS,MAAM,KAAK,SAAQ,GAAI,KAAK,KAAK,kBAAkB;AACvF,WAAK,iBAAiB,IAAI,eAAe,MAAM,KAAK,cAAa,CAAE;AACnE,WAAK,eAAe,QAAQ,KAAK,EAAE;AACnC,WAAK,qBAAqB;gBAChB,eAAe,CAAC,cAAc,KAAK,gBAAgB;AAC7D,WAAK,eAAe,WAAU;AAC9B,aAAO,KAAK;AACZ,aAAO,KAAK;;AAGd,WAAO;EACT;;EAGO,OAAO,WAAW,MAAwB,oBAAkB;AAAyB,WAAO,MAAM,WAAW,GAAG;EAAE;;EAElH,OAAO,YAAY,MAAwB,oBAAkB;AAA2B,WAAO,MAAM,YAAY,GAAG;EAAE;;EAEtH,OAAO,eAAe,KAAqB;AAAqB,WAAO,WAAU,WAAW,GAAG;EAAE;;EAEjG,OAAO,gBAAgB,KAAW;AAAuB,WAAO,MAAM,YAAY,GAAG;EAAE;;EAGpF,cAAW;AACnB,QAAI;AACJ,QAAI,SAAS;AAGb,QAAI,UAAoB,CAAA;AACxB,QAAI,OAAO,KAAK,KAAK,WAAW,UAAU;AACxC,gBAAU,KAAK,KAAK,OAAO,MAAM,GAAG;;AAEtC,QAAI,QAAQ,WAAW,GAAG;AACxB,WAAK,KAAK,YAAY,KAAK,KAAK,eAAe,QAAQ,CAAC;AACxD,WAAK,KAAK,aAAa,KAAK,KAAK,cAAc,QAAQ,CAAC;eAC/C,QAAQ,WAAW,GAAG;AAC/B,WAAK,KAAK,YAAY,QAAQ,CAAC;AAC/B,WAAK,KAAK,cAAc,QAAQ,CAAC;AACjC,WAAK,KAAK,eAAe,QAAQ,CAAC;AAClC,WAAK,KAAK,aAAa,QAAQ,CAAC;WAC3B;AACL,aAAO,MAAM,YAAY,KAAK,KAAK,MAAM;AACzC,WAAK,KAAK,aAAa,KAAK;AAC5B,eAAS,KAAK,KAAK,SAAS,KAAK;;AAInC,UAAM,OAAO,CAAC,aAAa,eAAe,gBAAgB,YAAY;AACtE,SAAK,QAAQ,OAAI;AACf,UAAI,KAAK,KAAK,CAAC,MAAM,QAAW;AAC9B,aAAK,KAAK,CAAC,IAAI;aACV;AACL,eAAO,MAAM,YAAY,KAAK,KAAK,CAAC,CAAC;AACrC,aAAK,KAAK,CAAC,IAAI,KAAK;AACpB,eAAO,KAAK,KAAK;;IAErB,CAAC;AACD,SAAK,KAAK,aAAa,KAAK;AAC5B,QAAI,KAAK,KAAK,cAAc,KAAK,KAAK,gBAAgB,KAAK,KAAK,eAAe,KAAK,KAAK,eAAe,KAAK,KAAK,cAAc,KAAK,KAAK,aAAa;AACrJ,WAAK,KAAK,SAAS,KAAK,KAAK;;AAI/B,UAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,YAAY,wBAAwB,GAAG,KAAK,KAAK,SAAS,GAAG,KAAK,KAAK,UAAU,EAAE;AACzF,UAAM,YAAY,2BAA2B,GAAG,KAAK,KAAK,YAAY,GAAG,KAAK,KAAK,UAAU,EAAE;AAC/F,UAAM,YAAY,0BAA0B,GAAG,KAAK,KAAK,WAAW,GAAG,KAAK,KAAK,UAAU,EAAE;AAC7F,UAAM,YAAY,yBAAyB,GAAG,KAAK,KAAK,UAAU,GAAG,KAAK,KAAK,UAAU,EAAE;AAE3F,WAAO;EACT;;;;;;;EAWO,OAAO,QAAK;AACjB,WAAO;EACT;;;;;;;;;;EAWO,OAAO,YAAY,QAAiC,eAA2B,SAA6B,OAA+B,UAAQ;AACxJ,QAAI,eAAe,UAAU,QAAW;AACtC,gBAAU,YAAY,cAAc;;AAGtC,oBAAgB,EAAE,UAAU,QAAQ,QAAQ,SAAS,GAAI,iBAAiB,CAAA,EAAG;AAC7E,UAAM,MAAO,OAAO,WAAW,WAAY,MAAM,YAAY,QAAQ,IAAI,IAAI;AAC7E,QAAI,QAAQ,CAAC,IAAI,MAAK;AACpB,UAAI,CAAC,GAAG,YAAY,EAAE;AAAG,WAAG,OAAO,IAAI,aAAa;AACpD,UAAI,UAAU,CAAC;AAAI,WAA2B,gBAAgB,QAAQ,CAAC;IACzE,CAAC;EACH;;;;;;;EAQO,QAAQ,KAAuB,KAAY;AAChD,QAAI,KAAK,KAAK;AAAY,aAAO;AACjC,eAAU,YAAY,GAAG,EAAE,QAAQ,QAAK;AACtC,YAAM,IAAI,GAAG;AACb,UAAI,CAAC;AAAG;AACR,YAAM,OAAO,EAAE,SAAS,EAAE,SAAS;AACnC,WAAK,gBAAgB,EAAE,EAAE;IAC3B,CAAC;AACD,WAAO;EACT;;;;;;EAOO,UAAU,KAAuB,KAAY;AAClD,QAAI,KAAK,KAAK;AAAY,aAAO;AACjC,eAAU,YAAY,GAAG,EAAE,QAAQ,QAAK;AACtC,YAAM,IAAI,GAAG;AACb,UAAI,CAAC;AAAG;AACR,YAAM,OAAO,EAAE,WAAW,EAAE,WAAW;AACvC,WAAK,gBAAgB,EAAE,EAAE;IAC3B,CAAC;AACD,WAAO;EACT;;;;;;;;;;;EAYO,QAAQ,UAAU,MAAI;AAC3B,QAAI,KAAK,KAAK;AAAY;AAC1B,SAAK,WAAW,OAAO,OAAO;AAC9B,SAAK,aAAa,OAAO,OAAO;AAChC,SAAK,cAAc,SAAS;AAC5B,WAAO;EACT;;;;;;;;;;EAUO,OAAO,UAAU,MAAI;AAC1B,QAAI,KAAK,KAAK;AAAY;AAC1B,SAAK,WAAW,MAAM,OAAO;AAC7B,SAAK,aAAa,MAAM,OAAO;AAC/B,SAAK,cAAc,QAAQ;AAC3B,WAAO;EACT;;;;;EAMO,WAAW,UAAmB,UAAU,MAAI;AACjD,QAAI,KAAK,KAAK;AAAY,aAAO;AACjC,eAAW,OAAO,KAAK,KAAK,cAAc,KAAK,KAAK,cAAc;AAClE,SAAK,OAAO,MAAM,QAAQ,OAAI;AAC5B,WAAK,gBAAgB,EAAE,EAAE;AACzB,UAAI,EAAE,WAAW;AAAS,UAAE,QAAQ,WAAW,UAAU,OAAO;IAClE,CAAC;AACD,WAAO;EACT;;;;;EAMO,aAAa,UAAmB,UAAU,MAAI;AACnD,QAAI,KAAK,KAAK;AAAY,aAAO;AACjC,eAAW,OAAO,KAAK,KAAK,gBAAgB,KAAK,KAAK,gBAAgB;AACtE,SAAK,OAAO,MAAM,QAAQ,OAAI;AAC5B,WAAK,gBAAgB,EAAE,EAAE;AACzB,UAAI,EAAE,WAAW;AAAS,UAAE,QAAQ,aAAa,UAAU,OAAO;IACpE,CAAC;AACD,WAAO;EACT;;EAGO,aAAU;AACf,UAAM,IAAI,KAAK,cAAc;AAC7B,QAAI,CAAC;AAAG;AACR,QAAI,EAAE,aAAa;AAEjB,QAAE,mBAAmB;AACrB,WAAK,OAAO,WAAW,CAAC;eACf,EAAE,kBAAkB;AAE7B,iBAAU,cAAc,EAAE,IAAI,KAAK;;AAGrC,SAAK,OAAO,eAAc;EAC5B;;EAGU,UAAU,IAAiB;AACnC,OAAG,UAAU,IAAI,SAAS,EAAE,UAAU,IAAI,SAAS;AACnD,QAAI,GAAG,eAAe;AACpB,aAAO,GAAG,cAAc;;AAE1B,WAAO,GAAG;AACV,WAAO;EACT;;EAGU,qBAAkB;AAG1B,QAAI,KAAK,KAAK,cAAe,CAAC,KAAK,KAAK,iBAAiB,CAAC,KAAK,KAAK,WAAY;AAC9E,SAAG,UAAU,KAAK,IAAI,SAAS;AAC/B,aAAO;;AAIT,QAAI,YAAoB;AAExB,UAAM,SAAS,CAAC,OAAkB,IAAyB,WAA+B;AACxF,eAAS,UAAU;AACnB,YAAM,OAAO,OAAO;AACpB,UAAI,CAAC;AAAM;AAIX,UAAI,CAAC,KAAK,MAAM,IAAI;AAElB,eAAO,MAAM,YAAY,SAAS,IAAI,KAAK,cAAc,MAAM,IAAI,IAAI,KAAK,cAAc,MAAM;AAEhG,cAAM,aAAa,OAAO,sBAAqB;AAC/C,eAAO,MAAM,OAAO,WAAW,KAAK,KAAK,cAAc,SAAS,MAAM,MAAM,UAAU,WAAW,KAAK,KAAK,cAAc,SAAS;AAClI,eAAO,MAAM,MAAM,WAAW,KAAK,KAAK,cAAc,SAAS,MAAM,MAAM,UAAU,WAAW,KAAK,KAAK,cAAc,SAAS;AACjI,eAAO,MAAM,kBAAkB;;AAGjC,UAAI,EAAE,KAAK,KAAI,IAAK,OAAO,sBAAqB;AAChD,YAAM,OAAO,KAAK,GAAG,sBAAqB;AAC1C,cAAQ,KAAK;AACb,aAAO,KAAK;AACZ,YAAM,KAAe;QACnB,UAAU;UACR,KAAK,MAAM,KAAK,cAAc;UAC9B,MAAM,OAAO,KAAK,cAAc;;;AAIpC,UAAI,KAAK,mBAAmB;AAC1B,aAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,OAAO,SAAS,CAAC;AACjD,aAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,MAAM,UAAU,CAAC;AACjD,eAAO,KAAK;AACZ,aAAK,OAAO,aAAa,IAAI;AAG7B,YAAI,CAAC,KAAK,OAAO,UAAU,IAAI,GAAG;AAChC,eAAK,eAAe;AACpB,cAAI,CAAC,KAAK,OAAO,UAAU,IAAI,GAAG;AAChC,eAAG,IAAI,IAAI,MAAM;AACjB;;AAEF,cAAI,KAAK,aAAa;AAEpB,kBAAM,QAAQ,MAAM,KAAK,WAAW;AACpC,mBAAO,KAAK;;;AAKhB,aAAK,eAAe,QAAQ,OAAO,IAAI,MAAM,WAAW,UAAU;aAC7D;AAEL,aAAK,cAAc,QAAQ,OAAO,IAAI,MAAM,WAAW,UAAU;;IAErE;AAEA,OAAG,UAAU,KAAK,IAAI;MACpB,QAAQ,CAAC,OAA2B;AAClC,cAAM,OAAsB,GAAG,iBAAiB,KAAK,UAAU,IAAI,KAAK;AAExE,YAAI,MAAM,SAAS;AAAM,iBAAO;AAChC,YAAI,CAAC,KAAK,KAAK;AAAe,iBAAO;AAErC,YAAI,YAAY;AAChB,YAAI,OAAO,KAAK,KAAK,kBAAkB,YAAY;AACjD,sBAAY,KAAK,KAAK,cAAc,EAAE;eACjC;AACL,gBAAM,WAAY,KAAK,KAAK,kBAAkB,OAAO,qBAAqB,KAAK,KAAK;AACpF,sBAAY,GAAG,QAAQ,QAAQ;;AAGjC,YAAI,aAAa,QAAQ,KAAK,KAAK,QAAQ;AACzC,gBAAM,IAAI,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,GAAG,MAAM,KAAK,MAAM,MAAM,KAAK,KAAI;AAClE,sBAAY,KAAK,OAAO,UAAU,CAAC;;AAErC,eAAO;MACT;KACD,EAIE,GAAG,KAAK,IAAI,YAAY,CAAC,OAAc,IAAyB,WAA+B;AAE9F,UAAI,OAAO,QAAQ,iBAAiB,GAAG;AAEvC,UAAI,MAAM,SAAS,QAAQ,CAAC,KAAK,mBAAmB;AAElD,eAAO;;AAIT,UAAI,MAAM,cAAc;AACtB,aAAK,IAAI,KAAK,aAAa;AAC3B,aAAK,IAAI,KAAK,aAAa;;AAI7B,UAAI,MAAM,QAAQ,KAAK,SAAS,QAAQ,CAAC,KAAK,mBAAmB;AAE/D,cAAM,YAAY,KAAK;AACvB,kBAAU,OAAO,IAAI,MAAM;;AAE7B,eAAS,UAAU;AAGnB,kBAAY,KAAK,UAAS;AAC1B,mBAAa,KAAK,cAAc,IAAI;AAGpC,UAAI,CAAC,MAAM;AACT,cAAM,OAAO,OAAO,aAAa,gBAAgB,KAAK,OAAO,aAAa,eAAe;AACzF,YAAI,MAAM;AACR,cAAI;AACF,mBAAO,KAAK,MAAM,IAAI;mBACf,OAAO;AACd,oBAAQ,MAAM,yCAAyC,IAAI;;AAE7D,iBAAO,gBAAgB,gBAAgB;AACvC,iBAAO,gBAAgB,eAAe;;AAExC,YAAI,CAAC;AAAM,iBAAO,KAAK,UAAU,MAAM;AAEvC,aAAK,eAAe,EAAE,GAAG,KAAK,GAAG,GAAG,KAAK,EAAC;;AAE5C,UAAI,CAAC,KAAK,MAAM;AACd,YAAI,CAAC,KAAK;AAAI,iBAAO,EAAC,GAAG,KAAI;AAC7B,aAAK,cAAc;AACnB,eAAO,gBAAgB;;AAIzB,YAAM,IAAI,KAAK,KAAK,KAAK,MAAM,OAAO,cAAc,SAAS,KAAK;AAClE,YAAM,IAAI,KAAK,KAAK,KAAK,MAAM,OAAO,eAAe,UAAU,KAAK;AAGpE,UAAI,KAAK,QAAQ,KAAK,SAAS,MAAM;AAGnC,YAAI,CAAC,GAAG;AAAoB,aAAG,qBAAqB;AACpD,WAAG,gBAAgB,OAAO,EAAE,GAAG,MAAM,GAAG,GAAG,MAAM,KAAI;AACrD,eAAO,KAAK;AACZ,eAAO,KAAK;AACZ,aAAK,OAAO,YAAY,IAAI,EACzB,aAAa,IAAI;AAEpB,aAAK,UACH,KAAK;QACL,KAAK,oBAAoB;aACtB;AACL,aAAK,IAAI;AACT,aAAK,IAAI;AACT,aAAK,oBAAoB;;AAI3B,iBAAU,cAAc,KAAK,IAAI,KAAK;AAEtC,SAAG,GAAG,IAAI,QAAQ,MAAM;AAExB,aAAO,OAAoB,IAAI,MAAM;AACrC,aAAO;IACT,CAAC,EAIA,GAAG,KAAK,IAAI,WAAW,CAAC,OAAO,IAAyB,WAA+B;AAEtF,YAAM,OAAO,QAAQ,iBAAiB,GAAG;AACzC,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,CAAC,KAAK,QAAQ,KAAK,SAAS,MAAM;AACpC,aAAK,OAAO,IAAI,MAAM;AAEtB,YAAI,KAAK,SAAS;AAChB,eAAK,gBAAgB,IAAI;;;AAG7B,aAAO;IACT,CAAC,EAIA,GAAG,KAAK,IAAI,QAAQ,CAAC,OAAO,IAAyB,WAA+B;AACnF,YAAM,OAAO,QAAQ,iBAAiB,GAAG;AAEzC,UAAI,MAAM,SAAS,QAAQ,CAAC,KAAK;AAAa,eAAO;AAErD,YAAM,WAAW,CAAC,CAAC,KAAK,YAAY;AACpC,YAAM,aAAa,OAAO;AAC1B,WAAK,YAAY,OAAM;AACvB,aAAO,KAAK,YAAY;AAGxB,UAAI,YAAY,KAAK,KAAK,SAAS;AACjC,aAAK,aAAa,KAAK;AACvB,aAAK,aAAa,MAAM,IAAI;;AAK9B,YAAM,WAAW,GAAG;AACpB,aAAO,GAAG;AACV,UAAI,YAAY,UAAU,QAAQ,SAAS,SAAS,MAAM;AACxD,cAAM,QAAQ,SAAS;AACvB,cAAM,OAAO,0BAA0B,QAAQ;AAC/C,cAAM,OAAO,aAAa,KAAK,QAAQ;AACvC,cAAM,oBAAmB,EAAG,oBAAmB;AAE/C,YAAI,MAAM,kBAAkB,CAAC,MAAM,OAAO,MAAM,UAAU,MAAM,KAAK,gBAAgB;AACnF,gBAAM,gBAAe;;;AAIzB,UAAI,CAAC;AAAM,eAAO;AAGlB,UAAI,UAAU;AACZ,aAAK,OAAO,YAAY,IAAI;AAC5B,aAAK,OAAO;;AAEd,aAAO,KAAK,MAAM;AAClB,SAAG,IAAI,IAAI,MAAM;AAEjB,UAAI,WAAW,IAAI;AACjB,eAAO,OAAM;AACb,aAAK;aACA;AACL,WAAG,OAAM;;AAEX,WAAK,UAAU,EAAE;AACjB,UAAI,CAAC;AAAU,eAAO;AACtB,YAAM,UAAU,KAAK,SAAS,IAAI;AAClC,YAAM,QAAQ,MAAM,KAAK,UAAU,KAAK,WAAW,CAAC;AACpD,YAAM,wBAAwB,EAAE;AAGhC,UAAI,eAAe,KAAK,WAAW,KAAK,eAAe,WAAU,cAAc;AAC7E,eAAO,KAAK;AACZ,aAAK,KAAK,UAAU,IAAI;aACnB;AACL,aAAK,gBAAgB,IAAI,MAAM,IAAI;AACnC,aAAK,GAAG,YAAY,EAAE;AAEtB,aAAK,qBAAqB,OAAO,IAAI;AACrC,YAAI,SAAS;AACX,kBAAQ,iBAAiB;;AAE3B,aAAK,uBAAsB;;AAE7B,WAAK,OAAO,WAAW,KAAK,IAAI;AAChC,WAAK,iBAAgB;AACrB,WAAK,oBAAmB;AAExB,WAAK,OAAO,UAAS;AACrB,UAAI,KAAK,gBAAgB,SAAS,GAAG;AACnC,aAAK,gBAAgB,SAAS,EAAE,EAAE,GAAG,OAAO,MAAM,UAAS,GAAI,YAAY,SAAS,OAAO,WAAW,QAAW,IAAI;;AAGvH,aAAO;IACT,CAAC;AACH,WAAO;EACT;;EAGQ,OAAO,cAAc,IAAyB,QAAe;AACnE,QAAI,CAAC;AAAI;AACT,UAAM,OAAO,KAAK,GAAG,gBAAgB;AACrC,QAAI,CAAC,MAAM,QAAQ,GAAG,UAAU,SAAS,KAAK,KAAK,KAAK,iBAAiB,OAAO;AAAG;AACnF,aAAS,KAAK,mBAAmB,OAAO,OAAO,KAAK;AACpD,aAAS,GAAG,UAAU,IAAI,0BAA0B,IAAI,GAAG,UAAU,OAAO,0BAA0B;EACxG;;EAGU,mBAAgB;AACxB,QAAI,OAAO,KAAK,KAAK,cAAc;AAAU,aAAO;AACpD,UAAM,UAAU,SAAS,cAAc,KAAK,KAAK,SAAS;AAC1D,QAAI,CAAC;AAAS,aAAO;AAKrB,QAAI,CAAC,KAAK,KAAK,cAAc,CAAC,GAAG,YAAY,OAAO,GAAG;AACrD,SAAG,UAAU,SAAS,KAAK,KAAK,gBAAgB,EAC7C,GAAG,SAAS,YAAY,CAAC,OAAO,OAAO,WAAU,cAAc,IAAI,IAAI,CAAC,EACxE,GAAG,SAAS,WAAW,CAAC,OAAO,OAAO,WAAU,cAAc,IAAI,KAAK,CAAC;;AAE7E,WAAO;EACT;;;;;;EAOO,gBAAgB,IAAyB,QAAQ,OAAK;AAC3D,UAAM,OAAO,IAAI;AACjB,QAAI,CAAC;AAAM;AACX,UAAM,SAAS,KAAK,UAAU,KAAK,KAAK;AACxC,UAAM,WAAW,KAAK,YAAY,KAAK,KAAK;AAG5C,UAAM,UAAU,KAAK,KAAK,cAAe,UAAU;AACnD,QAAI,SAAS,SAAS;AACpB,UAAI,KAAK,SAAS;AAChB,aAAK,UAAU,EAAE;AACjB,eAAO,KAAK;;AAEd,UAAI;AAAS,WAAG,UAAU,IAAI,yBAAyB,uBAAuB;AAC9E,UAAI,CAAC;AAAO,eAAO;;AAGrB,QAAI,CAAC,KAAK,SAAS;AAEjB,UAAI;AACJ,UAAI;AAGJ,YAAM,gBAAgB,CAAC,OAAc,OAAgB;AAEnD,aAAK,aAAa,OAAO,MAAM,MAA6B;AAC5D,oBAAY,KAAK,UAAS;AAC1B,qBAAa,KAAK,cAAc,IAAI;AAEpC,aAAK,eAAe,IAAI,OAAO,IAAI,MAAM,WAAW,UAAU;MAChE;AAGA,YAAM,eAAe,CAAC,OAAmB,OAAgB;AACvD,aAAK,cAAc,IAAI,OAAO,IAAI,MAAM,WAAW,UAAU;MAC/D;AAGA,YAAM,cAAc,CAAC,UAAgB;AACnC,aAAK,YAAY,OAAM;AACvB,eAAO,KAAK,YAAY;AACxB,eAAO,KAAK;AACZ,eAAO,KAAK;AACZ,eAAO,KAAK;AACZ,eAAO,KAAK;AACZ,cAAM,eAAe,KAAK,MAAM,KAAK,MAAM;AAG3C,cAAM,SAA8B,MAAM;AAC1C,YAAI,CAAC,OAAO,iBAAiB,OAAO,cAAc,SAAS;AAAM;AAEjE,aAAK,KAAK;AAEV,YAAI,KAAK,kBAAkB;AACzB,gBAAM,OAAO,GAAG,cAAc;AAC9B,cAAI,KAAK,gBAAgB,MAAM,IAAI,GAAG;AACpC,iBAAK,gBAAgB,MAAM,IAAI,EAAE,OAAO,MAAM;;AAEhD,eAAK,OAAO,MAAM,KAAK,IAAI;AAC3B,eAAK,aAAa,IAAI,MAAM,IAAI;eAC3B;AACL,gBAAM,wBAAwB,MAAM;AACpC,cAAI,KAAK,mBAAmB;AAE1B,kBAAM,QAAQ,MAAM,KAAK,KAAK;AAC9B,iBAAK,cAAc,QAAQ,IAAI;AAC/B,iBAAK,OAAO,QAAQ,IAAI;iBACnB;AAEL,iBAAK,cAAc,QAAQ,IAAI;;AAEjC,eAAK,aAAa,OAAO,MAAM;;AAGjC,aAAK,gBAAgB;AACrB,aAAK,uBAAsB;AAC3B,aAAK,oBAAmB;AAExB,aAAK,OAAO,UAAS;AAErB,YAAI,MAAM,SAAS,cAAc;AAC/B,cAAI,OAAO,UAAU,KAAK,aAAa;AAAG,iBAAK,gBAAgB,KAAK;AACpE,eAAK,qBAAqB,cAAc,IAAI;;MAEhD;AAEA,SAAG,UAAU,IAAI;QACf,OAAO;QACP,MAAM;QACN,MAAM;OACP,EAAE,UAAU,IAAI;QACf,OAAO;QACP,MAAM;QACN,QAAQ;OACT;AACD,WAAK,UAAU;;AAIjB,OAAG,UAAU,IAAI,SAAS,YAAY,QAAQ,EAC3C,UAAU,IAAI,WAAW,YAAY,QAAQ;AAEhD,WAAO;EACT;;EAGU,eAAe,IAAyB,OAAc,IAAc,MAAqB,WAAmB,YAAkB;AACtI,SAAK,OAAO,WAAU,EACnB,YAAY,IAAI;AAEnB,SAAK,cAAc,KAAK,aAAa,IAAI;AACzC,SAAK,GAAG,YAAY,KAAK,WAAW;AACpC,SAAK,YAAY,gBAAgB;AAKjC,QAAI,KAAK,MAAM,IAAI;AACjB,WAAK,gBAAgB,MAAM,gCAAgC,EAAE;eAItD,KAAK,eAAe,KAAK,YAAY,QAAQ,aAAa,GAAG;AACpE,YAAM,SAAS,KAAK,YAAY,QAAQ,aAAa;AACrD,WAAK,gBAAgB,MAAM,gCAAgC,MAAM;WAG9D;AACH,WAAK,gBAAgB;QACnB,QAAQ;QACR,SAAS;QACT,QAAQ;QACR,SAAS;;;AAIb,SAAK,KAAK,KAAK;AACf,SAAK,kBAAkB,GAAG;AAC1B,SAAK,YAAY,GAAG,SAAS;AAC7B,SAAK,UAAW,MAAM,SAAS;AAC/B,SAAK,YAAa,MAAM,SAAS;AACjC,WAAO,KAAK;AAEZ,QAAI,MAAM,SAAS,cAAc,KAAK,mBAAmB;AAEvD,WAAK,OAAO,QAAQ,IAAI;AACxB,WAAK,UAAU;;AAIjB,SAAK,OAAO,WAAW,WAAW,YAAY,KAAK,KAAK,WAAqB,KAAK,KAAK,aAAuB,KAAK,KAAK,cAAwB,KAAK,KAAK,UAAoB;AAC9K,QAAI,MAAM,SAAS,eAAe;AAChC,YAAM,UAAU,KAAK,UAAS,IAAK,KAAK;AACxC,YAAM,WAAW,KAAK,KAAK,UAAU,OAAO,oBAAoB,KAAK;AACrE,SAAG,UAAU,IAAI,UAAU,YAAY,YAAY,KAAK,IAAI,KAAK,QAAQ,GAAG,OAAO,CAAC,EACjF,UAAU,IAAI,UAAU,aAAa,aAAa,KAAK,IAAI,KAAK,QAAQ,GAAG,OAAO,CAAC,EACnF,UAAU,IAAI,UAAU,YAAY,YAAY,KAAK,IAAI,KAAK,QAAQ,OAAO,kBAAkB,OAAO,CAAC,EACvG,UAAU,IAAI,UAAU,oBAAoB,YAAY,KAAK,IAAI,KAAK,QAAQ,OAAO,kBAAkB,KAAK,IAAE,KAAK,CAAC,CAAC,EACrH,UAAU,IAAI,UAAU,aAAa,aAAa,KAAK,IAAI,KAAK,QAAQ,OAAO,kBAAkB,OAAO,CAAC,EACzG,UAAU,IAAI,UAAU,mBAAmB,aAAa,KAAK,IAAI,KAAK,QAAQ,OAAO,kBAAkB,KAAK,IAAE,KAAK,CAAC,CAAC;;EAE5H;;EAGU,cAAc,IAAyB,OAAmB,IAAc,MAAqB,WAAmB,YAAkB;AAC1I,UAAM,IAAI,EAAE,GAAG,KAAK,MAAK;AACzB,QAAI;AACJ,QAAI,QAAQ,KAAK,KAAK,YACpB,SAAS,KAAK,KAAK,aACnB,OAAO,KAAK,KAAK,WACjB,UAAU,KAAK,KAAK;AAGtB,UAAM,UAAU,KAAK,MAAM,aAAa,GAAG,GACzC,SAAS,KAAK,MAAM,YAAY,GAAG;AACrC,YAAQ,KAAK,IAAI,OAAO,MAAM;AAC9B,aAAS,KAAK,IAAI,QAAQ,MAAM;AAChC,WAAO,KAAK,IAAI,MAAM,OAAO;AAC7B,cAAU,KAAK,IAAI,SAAS,OAAO;AAEnC,QAAI,MAAM,SAAS,QAAQ;AACzB,UAAI,KAAK;AAAmB;AAC5B,YAAM,WAAW,GAAG,SAAS,MAAM,KAAK;AACxC,WAAK,YAAY,GAAG,SAAS;AAC7B,UAAI,KAAK,KAAK,UAAU,WAAW,OAAO;AACxC,cAAM,qBAAqB,IAAI,GAAG,UAAU,QAAQ;;AAItD,YAAM,OAAO,GAAG,SAAS,QAAQ,GAAG,SAAS,OAAO,KAAK,gBAAgB,OAAO,CAAC,SAAS;AAC1F,YAAM,MAAM,GAAG,SAAS,OAAO,GAAG,SAAS,MAAM,KAAK,gBAAgB,MAAM,CAAC,UAAU;AACvF,QAAE,IAAI,KAAK,MAAM,OAAO,SAAS;AACjC,QAAE,IAAI,KAAK,MAAM,MAAM,UAAU;AAGjC,YAAM,OAAO,KAAK;AAClB,UAAI,KAAK,OAAO,QAAQ,MAAM,CAAC,GAAG;AAChC,cAAM,MAAM,KAAK,OAAM;AACvB,YAAI,QAAQ,KAAK,IAAI,GAAI,EAAE,IAAI,KAAK,IAAK,GAAG;AAC5C,YAAI,KAAK,KAAK,UAAU,MAAM,QAAQ,KAAK,KAAK,QAAQ;AACtD,kBAAQ,KAAK,IAAI,GAAG,KAAK,KAAK,SAAS,GAAG;;AAE5C,aAAK,gBAAgB;;AAChB,aAAK,gBAAgB;AAC5B,UAAI,KAAK,kBAAkB;AAAM,aAAK,uBAAsB;AAE5D,UAAI,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE;AAAG;eAG7B,MAAM,SAAS,UAAU;AAClC,UAAI,EAAE,IAAI;AAAG;AAEb,YAAM,mBAAmB,OAAO,IAAI,UAAU;AAG9C,QAAE,IAAI,KAAK,OAAO,GAAG,KAAK,QAAQ,SAAS,SAAS;AACpD,QAAE,IAAI,KAAK,OAAO,GAAG,KAAK,SAAS,QAAQ,UAAU;AACrD,UAAI,KAAK,MAAM,EAAE,KAAK,KAAK,MAAM,EAAE;AAAG;AACtC,UAAI,KAAK,cAAc,KAAK,WAAW,MAAM,EAAE,KAAK,KAAK,WAAW,MAAM,EAAE;AAAG;AAG/E,YAAM,OAAO,GAAG,SAAS,OAAO;AAChC,YAAM,MAAM,GAAG,SAAS,MAAM;AAC9B,QAAE,IAAI,KAAK,MAAM,OAAO,SAAS;AACjC,QAAE,IAAI,KAAK,MAAM,MAAM,UAAU;AAEjC,iBAAW;;AAGb,SAAK,SAAS;AACd,SAAK,aAAa;AAClB,UAAM,OAA0B;MAC9B,GAAG,GAAG,SAAS,OAAO;MACtB,GAAG,GAAG,SAAS,MAAM;MACrB,IAAI,GAAG,OAAO,GAAG,KAAK,QAAQ,KAAK,IAAI,aAAa,QAAQ;MAC5D,IAAI,GAAG,OAAO,GAAG,KAAK,SAAS,KAAK,IAAI,cAAc,OAAO;;AAE/D,QAAI,KAAK,OAAO,cAAc,MAAM,EAAE,GAAG,GAAG,WAAW,YAAY,MAAM,SAAQ,CAAE,GAAG;AACpF,WAAK,kBAAkB,GAAG;AAC1B,WAAK,OAAO,WAAW,WAAW,YAAY,MAAM,QAAQ,SAAS,KAAK;AAC1E,aAAO,KAAK;AACZ,UAAI,YAAY,KAAK;AAAS,aAAK,QAAQ,SAAQ;AACnD,WAAK,gBAAgB;AACrB,WAAK,uBAAsB;AAE3B,YAAM,SAAS,MAAM;AAErB,UAAI,CAAC,KAAK,cAAc;AACtB,aAAK,cAAc,QAAQ,IAAI;;AAEjC,WAAK,aAAa,OAAO,MAAM;;EAEnC;;EAGU,aAAa,OAAc,QAA2B;AAE9D,QAAI,OAAkB;AACtB,WAAO,KAAK;AAAgB,aAAO,KAAK,eAAe;AACvD,QAAI,KAAK,gBAAgB,MAAM,IAAI,GAAG;AACpC,WAAK,gBAAgB,MAAM,IAAI,EAAE,OAAO,MAAM;;EAElD;;;;;EAMU,OAAO,IAAyB,QAA4B;AACpE,aAAS,UAAU;AACnB,UAAM,OAAO,OAAO;AACpB,QAAI,CAAC;AAAM;AAGX,WAAO,MAAM,YAAY,OAAO,MAAM,kBAAkB;AACxD,OAAG,IAAI,IAAI,MAAM;AAGjB,QAAI,KAAK;AAAmB;AAC5B,SAAK,oBAAoB;AAEzB,SAAK,OAAO,WAAW,IAAI;AAC3B,SAAK,KAAK,KAAK,eAAe,SAAS,SAAS;AAChD,UAAM,cAAc,KAAK;AACzB,QAAI,KAAK;AAAa,WAAK,OAAO,YAAY,IAAI;AAElD,SAAK,eAAe;AAEpB,QAAI,KAAK,KAAK,cAAc,MAAM;AAEhC,iBAAU,cAAc,IAAI,IAAI;;AAIlC,QAAI,GAAG,oBAAoB;AAEzB,SAAG,gBAAgB,GAAG;AACtB,aAAO,GAAG;eACD,KAAK,aAAa;AAE3B,WAAK,OAAO,eAAc;;EAE9B;;EAGO,SAAM;AAAgB,aAAS,MAAM,KAAK,YAAY,KAAK,GAAG,UAAU,eAAe,KAAK;AAAG,WAAO;EAAM;;AAh7ErG,UAAA,WAAuB,CAAC,IAAiB,MAAoB;AAAG,MAAI,MAAM,GAAG;AAAS,OAAG,cAAc,EAAE;AAAS;AAQlH,UAAA,wBAAwB;AAGxB,UAAA,QAAQ;AAGR,UAAA,SAAS;AAkqDhB,UAAA,QAAQ;", "names": ["n", "el", "grid", "el", "name", "n"]}