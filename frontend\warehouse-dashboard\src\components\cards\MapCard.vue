<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';
import { sciFiTheme } from '@/echarts-theme.js';
import { useDataSourceStore } from '@/stores/dataSource';
 
const props = defineProps({
  title: {
    type: String,
    default: '地图'
  },
  dataSourceId: {
    type: String,
    default: null
  }
});
 
const chartRef = ref(null);
const chartInstance = ref(null);
const dataSourceStore = useDataSourceStore();
 
const initChart = async () => {
  // 1. 获取地图JSON并注册地图
  const response = await fetch('/maps/china.json');
  const mapJson = await response.json();
  echarts.registerMap('china', mapJson);
 
  // 2. 注册主题并初始化
  echarts.registerTheme('sci-fi', sciFiTheme);
  chartInstance.value = echarts.init(chartRef.value, 'sci-fi');
 
  // 3. 初始加载数据
  updateChartData();
};
 
const updateChartData = async () => {
  if (!props.dataSourceId || !chartInstance.value) return;
 
  chartInstance.value.showLoading();
  const data = await dataSourceStore.fetchData(props.dataSourceId);
  chartInstance.value.hideLoading();
 
  chartInstance.value.setOption({
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>{a}: {c}'
    },
    // 视觉映射组件，用于根据数据大小映射颜色
    visualMap: {
      min: 0,
      max: 5000, // 建议根据你的数据范围动态设置
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#002256', '#0055a4', '#0091ff', '#40e0d0'] // 从深蓝到青色的渐变
      },
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '业务量', // 这个会显示在 tooltip 中
        type: 'map',
        map: 'china',
        roam: true, // 开启鼠标缩放和平移漫游
        label: {
          show: false // 默认不显示省份标签
        },
        emphasis: {
          label: {
            show: true,
            color: '#fff'
          },
          itemStyle: {
            areaColor: '#FFD700' // 高亮时的颜色
          }
        },
        data: data
      }
    ]
  });
};
 
onMounted(() => {
  initChart();
});
 
watch(() => [props.dataSourceId, props.title], () => {
  updateChartData();
}, { deep: true });
</script>
 
<template>
  <div ref="chartRef" class="w-full h-full"></div>
</template>
