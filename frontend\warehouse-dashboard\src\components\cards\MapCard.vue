<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';
import { sciFiTheme } from '@/echarts-theme.js';
import { useDataSourceStore } from '@/stores/dataSource';
import { useFilterStore } from '@/stores/filterStore'; // 1. 引入 filter store
import { useLayoutStore } from '@/stores/layout';
import WidgetConfigurator from '@/components/configurators/WidgetConfigurator.vue';
 
const props = defineProps({
  title: {
    type: String,
    default: '地图'
  },
  dataSourceId: {
    type: String,
    default: null
  },
  widgetId: {
    type: String,
    default: undefined
  }
});
 
const chartRef = ref(null);
const chartInstance = ref(null);
const dataSourceStore = useDataSourceStore();
const filterStore = useFilterStore(); // 2. 获取 filter store 实例
const layoutStore = useLayoutStore();

// 编辑状态
const isEditing = ref(false);
 
const initChart = async () => {
  try {
    // 1. 获取地图JSON并注册地图
    console.log('开始加载地图数据...');
    const response = await fetch('/maps/china.json');
    if (!response.ok) {
      throw new Error(`地图数据加载失败: ${response.status}`);
    }
    const mapJson = await response.json();
    echarts.registerMap('china', mapJson);
    console.log('地图数据加载成功');

    // 2. 注册主题并初始化
    echarts.registerTheme('sci-fi', sciFiTheme);
    chartInstance.value = echarts.init(chartRef.value, 'sci-fi');
    console.log('地图组件初始化成功');

    // 3. 监听图表点击事件
    chartInstance.value.on('click', (params) => {
      // params.name 就是被点击区域的名称，即"省份"
      if (params.name) {
        console.log(`[MapCard] 点击省份: ${params.name}`);
        filterStore.setProvinceFilter(params.name);
      }
    });

    // 4. 初始加载数据
    updateChartData();
  } catch (error) {
    console.error('地图初始化失败:', error);
  }
};
 
const updateChartData = async () => {
  if (!props.dataSourceId || !chartInstance.value) {
    console.log('地图数据更新跳过: dataSourceId=', props.dataSourceId, 'chartInstance=', !!chartInstance.value);
    return;
  }

  try {
    chartInstance.value.showLoading();
    const data = await dataSourceStore.fetchData(props.dataSourceId);
    console.log('地图数据获取成功:', data);
    chartInstance.value.hideLoading();
 
  chartInstance.value.setOption({
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>{a}: {c}'
    },
    // 视觉映射组件，用于根据数据大小映射颜色
    visualMap: {
      min: 0,
      max: 5000, // 建议根据你的数据范围动态设置
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#002256', '#0055a4', '#0091ff', '#40e0d0'] // 从深蓝到青色的渐变
      },
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '业务量', // 这个会显示在 tooltip 中
        type: 'map',
        map: 'china',
        roam: true, // 开启鼠标缩放和平移漫游
        label: {
          show: false // 默认不显示省份标签
        },
        emphasis: {
          label: {
            show: true,
            color: '#fff'
          },
          itemStyle: {
            areaColor: '#FFD700' // 高亮时的颜色
          }
        },
        data: data
      }
    ]
  });
  } catch (error) {
    console.error('地图数据更新失败:', error);
    if (chartInstance.value) {
      chartInstance.value.hideLoading();
    }
  }
};
 
onMounted(() => {
  initChart();
});
 
watch(() => [props.dataSourceId, props.title], () => {
  updateChartData();
}, { deep: true });

// 配置相关事件处理
const handleSave = (newProps) => {
  if (props.widgetId) {
    layoutStore.updateWidgetProps(props.widgetId, newProps);
    console.log('保存地图配置:', props.widgetId, newProps);
  } else {
    console.warn('无法保存配置：缺少 widgetId');
  }
  isEditing.value = false;
};

const handleCancel = () => {
  isEditing.value = false;
};
</script>
 
<template>
  <div class="map-card h-full bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg p-4 text-white relative">
    <!-- 设置图标 -->
    <button
      @click="isEditing = true"
      class="absolute top-2 right-2 w-6 h-6 bg-black bg-opacity-30 hover:bg-opacity-50 rounded-full flex items-center justify-center transition-all duration-200 z-10"
      title="配置组件"
    >
      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
      </svg>
    </button>

    <!-- 正常显示模式 -->
    <div v-if="!isEditing" class="h-full">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">{{ title }}</h3>
        <div class="text-2xl">🗺️</div>
      </div>
      <div ref="chartRef" class="w-full" style="height: calc(100% - 60px);"></div>
    </div>

    <!-- 配置模式 -->
    <div v-else class="h-full flex items-center justify-center">
      <WidgetConfigurator
        :initial-props="props"
        @save="handleSave"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>
