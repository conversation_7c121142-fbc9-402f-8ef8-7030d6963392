<script setup>
import { ref, onMounted, watch } from 'vue';
import * as echarts from 'echarts';
import { sciFiTheme } from '@/echarts-theme.js';
import { useDataSourceStore } from '@/stores/dataSource';
import { useFilterStore } from '@/stores/filterStore'; // 1. 引入 filter store
 
const props = defineProps({
  title: {
    type: String,
    default: '地图'
  },
  dataSourceId: {
    type: String,
    default: null
  }
});
 
const chartRef = ref(null);
const chartInstance = ref(null);
const dataSourceStore = useDataSourceStore();
const filterStore = useFilterStore(); // 2. 获取 filter store 实例
 
const initChart = async () => {
  try {
    // 1. 获取地图JSON并注册地图
    console.log('开始加载地图数据...');
    const response = await fetch('/maps/china.json');
    if (!response.ok) {
      throw new Error(`地图数据加载失败: ${response.status}`);
    }
    const mapJson = await response.json();
    echarts.registerMap('china', mapJson);
    console.log('地图数据加载成功');

    // 2. 注册主题并初始化
    echarts.registerTheme('sci-fi', sciFiTheme);
    chartInstance.value = echarts.init(chartRef.value, 'sci-fi');
    console.log('地图组件初始化成功');

    // 3. 监听图表点击事件
    chartInstance.value.on('click', (params) => {
      // params.name 就是被点击区域的名称，即"省份"
      if (params.name) {
        console.log(`[MapCard] 点击省份: ${params.name}`);
        filterStore.setProvinceFilter(params.name);
      }
    });

    // 4. 初始加载数据
    updateChartData();
  } catch (error) {
    console.error('地图初始化失败:', error);
  }
};
 
const updateChartData = async () => {
  if (!props.dataSourceId || !chartInstance.value) {
    console.log('地图数据更新跳过: dataSourceId=', props.dataSourceId, 'chartInstance=', !!chartInstance.value);
    return;
  }

  try {
    chartInstance.value.showLoading();
    const data = await dataSourceStore.fetchData(props.dataSourceId);
    console.log('地图数据获取成功:', data);
    chartInstance.value.hideLoading();
 
  chartInstance.value.setOption({
    title: {
      text: props.title,
      left: 'center'
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}<br/>{a}: {c}'
    },
    // 视觉映射组件，用于根据数据大小映射颜色
    visualMap: {
      min: 0,
      max: 5000, // 建议根据你的数据范围动态设置
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#002256', '#0055a4', '#0091ff', '#40e0d0'] // 从深蓝到青色的渐变
      },
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '业务量', // 这个会显示在 tooltip 中
        type: 'map',
        map: 'china',
        roam: true, // 开启鼠标缩放和平移漫游
        label: {
          show: false // 默认不显示省份标签
        },
        emphasis: {
          label: {
            show: true,
            color: '#fff'
          },
          itemStyle: {
            areaColor: '#FFD700' // 高亮时的颜色
          }
        },
        data: data
      }
    ]
  });
  } catch (error) {
    console.error('地图数据更新失败:', error);
    if (chartInstance.value) {
      chartInstance.value.hideLoading();
    }
  }
};
 
onMounted(() => {
  initChart();
});
 
watch(() => [props.dataSourceId, props.title], () => {
  updateChartData();
}, { deep: true });
</script>
 
<template>
  <div ref="chartRef" class="w-full h-full"></div>
</template>
