<template>
  <div class="dashboard-app-container logistics-dashboard-bg">
    <!-- Header Section -->
    <header class="dashboard-header" v-if="!isTestMode && !isKpiTestMode && !isDashboardTestMode && !isLaborCostTestMode && !isMonthlyOrdersTestMode && !isLogisticsMapTestMode && !isRealTimeOrderListTestMode && !isCrossFilterTestMode && !isDynamicDashboardMode && !isDynamicDashboardTestMode">
      <!-- Left: Date and Time -->
      <div class="header-datetime">
        <p class="date-text">{{ currentDate }}</p>
        <p class="time-text">{{ currentTime }} {{ currentWeekday }}</p>
      </div>

      <!-- Center: Main Title -->
      <h1 class="dashboard-title">
        <span class="title-glow">物流大数据</span>
        <span class="title-accent">展示平台</span>
      </h1>

      <!-- Right: Navigation Buttons -->
      <div class="nav-buttons">
        <button @click="toggleDynamicDashboardMode" class="nav-btn nav-btn-primary">动态仪表盘</button>
        <button @click="toggleDynamicDashboardTestMode" class="nav-btn nav-btn-secondary">仪表盘测试</button>
        <button @click="toggleTestMode" class="nav-btn nav-btn-accent">组件测试</button>
        <button @click="toggleKpiTestMode" class="nav-btn nav-btn-accent">KPI卡片</button>
        <button @click="toggleDashboardTestMode" class="nav-btn nav-btn-accent">大屏布局</button>
        <button @click="toggleLaborCostTestMode" class="nav-btn nav-btn-accent">成本图表</button>
        <button @click="toggleMonthlyOrdersTestMode" class="nav-btn nav-btn-accent">趋势图表</button>
        <button @click="toggleLogisticsMapTestMode" class="nav-btn nav-btn-accent">物流地图</button>
        <button @click="toggleRealTimeOrderListTestMode" class="nav-btn nav-btn-accent">订单列表</button>
        <button @click="toggleCrossFilterTestMode" class="nav-btn nav-btn-accent">交叉筛选</button>
      </div>
    </header>

    <!-- 动态仪表盘页面 -->
    <DashboardView v-if="isDynamicDashboardMode" />

    <!-- 动态仪表盘测试页面 -->
    <DynamicDashboardTest v-if="isDynamicDashboardTestMode" />

    <!-- 交叉筛选测试页面 -->
    <CrossFilterTest v-if="isCrossFilterTestMode" />

    <!-- 实时订单列表测试页面 -->
    <RealTimeOrderListTest v-if="isRealTimeOrderListTestMode" />

    <!-- 物流地图测试页面 -->
    <LogisticsMapTest v-if="isLogisticsMapTestMode" />

    <!-- 月度订单图表测试页面 -->
    <MonthlyOrdersTest v-if="isMonthlyOrdersTestMode" />

    <!-- 人力成本图表测试页面 -->
    <LaborCostTest v-if="isLaborCostTestMode" />

    <!-- 大屏布局测试页面 -->
    <DashboardTest v-if="isDashboardTestMode" />

    <!-- KPI 卡片测试页面 -->
    <KpiCardTest v-if="isKpiTestMode" />

    <!-- 测试组件页面 -->
    <TestComponents v-if="isTestMode" />

    <!-- 原始应用 -->
    <div v-else-if="!isTestMode && !isKpiTestMode && !isDashboardTestMode && !isLaborCostTestMode && !isMonthlyOrdersTestMode && !isLogisticsMapTestMode && !isRealTimeOrderListTestMode && !isCrossFilterTestMode" class="dashboard-main">

    <!-- 顶部KPI卡片区域 -->
    <section class="top-kpi-section">
      <div class="kpi-cards-container">
        <KpiCard
          title="TOC总单量"
          :value="12580"
          unit="单"
          type="primary"
          icon="📦"
          :trend="5.2"
          trend-label="较昨日"
          description="今日TOC业务总订单量"
        />

        <KpiCard
          title="TOB总单量"
          :value="8960"
          unit="单"
          type="success"
          icon="🏢"
          :trend="-2.1"
          trend-label="较昨日"
          description="今日TOB业务总订单量"
        />

        <KpiCard
          title="运输成本"
          :value="156.8"
          unit="万元"
          type="warning"
          icon="🚛"
          :trend="1.8"
          trend-label="较昨日"
          :precision="1"
          description="当日运输总成本"
        />

        <KpiCard
          title="客户投诉"
          :value="23"
          unit="件"
          type="danger"
          icon="⚠️"
          :trend="-15.3"
          trend-label="较昨日"
          description="当日客户投诉总数"
        />
      </div>
    </section>

    <!-- 主内容区 (三列布局) -->
    <main class="main-content">
      <!-- 左侧列 -->
      <section class="column-left">
        <div class="data-panel">
          <div class="panel-header">
            <span class="panel-title">实时运营指标</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <EnhancedDataDisplay
              :metrics="operationalMetrics"
              :show-scrolling-data="false"
              type="grid"
            />
          </div>
        </div>
        <div class="data-panel">
          <div class="panel-header">
            <span class="panel-title">仓库状态监控</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <EnhancedDataDisplay
              :scrolling-data="warehouseStatus"
              :show-scrolling-data="true"
              type="list"
            />
          </div>
        </div>
        <div class="data-panel">
          <div class="panel-header">
            <span class="panel-title">商品分类占比</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <PieChart
              :data="categoryData"
              chart-id="category-pie-chart"
              title=""
            />
          </div>
        </div>

        <!-- 新增组件测试 -->
        <div class="data-panel">
          <div class="panel-header">
            <span class="panel-title">核心运营指标</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <OverallKpiCard
              title=""
              :data="apiData"
            />
          </div>
        </div>
      </section>

      <!-- 中间列 -->
      <section class="column-center">
        <div class="central-map-panel">
           <div class="map-stats-header">
              <div class="stat-display">
                <span class="stat-label">数量统计</span>
                <span class="stat-value">365个</span>
              </div>
              <div class="stat-display">
                <span class="stat-label">同比上升</span>
                <span class="stat-value stat-positive">+13%</span>
              </div>
           </div>
           <div class="map-container">
             <LogisticsVisualization
               :nodes="logisticsData"
             />
           </div>
        </div>
        <div class="data-panel">
          <div class="panel-header">
            <span class="panel-title">月度运营统计</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <MonthlyStatsChart
              :data="monthlyStatsData"
              theme="dark"
            />
          </div>
        </div>

        <div class="data-panel">
          <div class="panel-header">
            <span class="panel-title">订单趋势分析</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <MonthlyOrdersChart
              :chartData="trendChartData"
              title=""
              subtitle="月度订单量变化趋势"
              :loading="false"
              :showLabel="true"
              :enableAnimation="true"
              themeColor="#00CED1"
              chartHeight="250px"
            />
          </div>
        </div>
      </section>

      <!-- 右侧列 -->
      <section class="column-right">
        <div class="data-panel tech-panel-wrapper">
          <div class="panel-header">
            <span class="panel-title">数据分析中心</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <TechDataPanel
              :top-stats="salesStats"
              :data-list="salesRanking"
              :status-list="platformStatus"
              list-title="商品销售排行"
              :system-health="98"
              :show-data-stream="true"
            />
          </div>
        </div>

        <!-- 成本分析图表 -->
        <div class="data-panel">
          <div class="panel-header">
            <span class="panel-title">成本结构分析</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <LaborCostPieChart
              :chartData="laborCostData"
              title=""
              subtitle="各项成本占比分析"
              :loading="false"
              :showPercentage="true"
              :enableAnimation="true"
              chartHeight="200px"
            />
          </div>
        </div>

        <!-- 实时订单列表 -->
        <div class="data-panel">
          <div class="panel-header">
            <span class="panel-title">实时订单流水</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <RealTimeOrderList
              :listData="orderListData"
              title=""
              subtitle="实时订单状态监控"
              :scrollDuration="30"
              :maxRows="6"
              :showControls="false"
              :loading="false"
            />
          </div>
        </div>
      </section>
    </main>

    <!-- 底部详细图表区域 -->
    <section class="bottom-charts-section">
      <div class="bottom-charts-container">
        <!-- 物流地图详细视图 -->
        <div class="bottom-chart-panel">
          <div class="panel-header">
            <span class="panel-title">物流网络地图</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <LogisticsMap
              :warehouseData="logisticsMapData.warehouses"
              :routeData="logisticsMapData.routes"
              title=""
              subtitle="全国物流网络分布"
              :loading="false"
              :enableAnimation="true"
              mapHeight="300px"
            />
          </div>
        </div>

        <!-- 详细统计图表 -->
        <div class="bottom-chart-panel">
          <div class="panel-header">
            <span class="panel-title">综合数据分析</span>
            <div class="panel-indicator"></div>
          </div>
          <div class="panel-content">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-label">总业务量</div>
                <div class="stat-value">{{ totalBusinessVolume.toLocaleString() }}</div>
                <div class="stat-unit">单/日</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">网络覆盖</div>
                <div class="stat-value">{{ networkCoverage }}</div>
                <div class="stat-unit">%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">平均效率</div>
                <div class="stat-value">{{ averageEfficiency.toFixed(1) }}</div>
                <div class="stat-unit">%</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">活跃仓库</div>
                <div class="stat-value">{{ activeWarehouses }}</div>
                <div class="stat-unit">个</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import AppHeader from './components/AppHeader.vue';
import OverallKpiCard from './components/OverallKpiCard.vue';
import InventoryHealthPanel from './components/InventoryHealthPanel.vue';
import MonthlyKpiScoreboard from './components/MonthlyKpiScoreboard.vue';
import ServiceQualityTracker from './components/ServiceQualityTracker.vue';
import PieChart from './components/PieChart.vue';
import TestComponents from './TestComponents.vue';
import KpiCardTest from './KpiCardTest.vue';
import DashboardTest from './DashboardTest.vue';
import LaborCostTest from './LaborCostTest.vue';
import MonthlyOrdersTest from './MonthlyOrdersTest.vue';
import LogisticsMapTest from './LogisticsMapTest.vue';
import RealTimeOrderListTest from './RealTimeOrderListTest.vue';
import CrossFilterTest from './CrossFilterTest.vue';
import DashboardView from './views/DashboardView.vue';
import DynamicDashboardTest from './DynamicDashboardTest.vue';
import ChinaMapVisualization from './components/ChinaMapVisualization.vue';
import EnhancedDataDisplay from './components/EnhancedDataDisplay.vue';
import TechDataPanel from './components/TechDataPanel.vue';
import LogisticsVisualization from './components/LogisticsVisualization.vue';
import MonthlyStatsChart from './components/MonthlyStatsChart.vue';
import KpiCard from './components/KpiCard.vue';
import MonthlyOrdersChart from './components/MonthlyOrdersChart.vue';
import LaborCostPieChart from './components/LaborCostPieChart.vue';
import RealTimeOrderList from './components/RealTimeOrderList.vue';
import LogisticsMap from './components/LogisticsMap.vue';

// --- 响应式数据 ---
// 测试模式
const isTestMode = ref(false);
const isKpiTestMode = ref(false);
const isDashboardTestMode = ref(false);
const isLaborCostTestMode = ref(false);
const isMonthlyOrdersTestMode = ref(false);
const isLogisticsMapTestMode = ref(false);
const isRealTimeOrderListTestMode = ref(false);
const isCrossFilterTestMode = ref(false);
const isDynamicDashboardMode = ref(false);
const isDynamicDashboardTestMode = ref(false);

// Header 标题
const headerTitle = ref('物流大数据展示平台');

// 日期时间相关
const currentDate = ref('');
const currentTime = ref('');
const currentWeekday = ref('');

// 后端数据
const apiData = ref([]);

// 商品分类数据
const categoryData = ref([
  { value: 35, name: '电子产品' },
  { value: 25, name: '服装鞋帽' },
  { value: 20, name: '食品饮料' },
  { value: 12, name: '家居用品' },
  { value: 8, name: '其他' }
]);

// 物流节点数据
const logisticsData = ref([
  { name: '武汉仓库', orders: 2340, efficiency: 98.5, color: '#00D4FF', trend: 12.5 },
  { name: '黄冈仓库', orders: 1890, efficiency: 96.2, color: '#4ECDC4', trend: 8.3 },
  { name: '天门仓库', orders: 1680, efficiency: 94.8, color: '#00FF88', trend: -2.1 },
  { name: '深圳仓库', orders: 1560, efficiency: 99.1, color: '#FFEAA7', trend: 15.7 },
  { name: '北京仓库', orders: 1200, efficiency: 97.3, color: '#DDA0DD', trend: 6.9 },
  { name: '上海仓库', orders: 980, efficiency: 95.7, color: '#FF6B6B', trend: 4.2 },
  { name: '广州仓库', orders: 890, efficiency: 98.9, color: '#96CEB4', trend: 9.8 },
  { name: '杭州仓库', orders: 780, efficiency: 96.5, color: '#FFB6C1', trend: 3.4 },
  { name: '成都仓库', orders: 720, efficiency: 93.4, color: '#87CEEB', trend: -1.2 },
  { name: '重庆仓库', orders: 650, efficiency: 95.1, color: '#F0E68C', trend: 7.6 }
]);

// 运营指标数据
const operationalMetrics = ref([
  {
    label: '今日订单',
    value: 2340,
    unit: '单',
    color: '#00D4FF',
    icon: 'fas fa-box',
    trend: 12.5,
    highlight: true
  },
  {
    label: '处理效率',
    value: 96.8,
    unit: '%',
    color: '#00FF88',
    icon: 'fas fa-tachometer-alt',
    trend: 8.3
  },
  {
    label: '异常订单',
    value: 23,
    unit: '单',
    color: '#FF6B6B',
    icon: 'fas fa-exclamation-triangle',
    trend: -15.2
  },
  {
    label: '库存周转',
    value: 4.2,
    unit: '次',
    color: '#4ECDC4',
    icon: 'fas fa-sync-alt',
    trend: 5.7
  }
]);

// 仓库状态数据
const warehouseStatus = ref([
  { name: '武汉仓库', value: '98.5%', color: '#00FF88' },
  { name: '黄冈仓库', value: '96.2%', color: '#00D4FF' },
  { name: '天门仓库', value: '94.8%', color: '#4ECDC4' },
  { name: '深圳仓库', value: '99.1%', color: '#00FF88' },
  { name: '北京仓库', value: '97.3%', color: '#00D4FF' },
  { name: '上海仓库', value: '95.7%', color: '#4ECDC4' },
  { name: '广州仓库', value: '98.9%', color: '#00FF88' },
  { name: '成都仓库', value: '93.4%', color: '#FFEAA7' }
]);

// 销售统计数据
const salesStats = ref([
  { label: '今日销售', value: 15680, icon: 'fas fa-chart-line', color: '#00D4FF', trend: 18.5 },
  { label: '订单转化', value: 87.3, icon: 'fas fa-percentage', color: '#00FF88', trend: 12.3 },
  { label: '客户满意', value: 96.8, icon: 'fas fa-smile', color: '#4ECDC4', trend: 5.7 },
  { label: '退货率', value: 2.1, icon: 'fas fa-undo', color: '#FF6B6B', trend: -15.2 }
]);

// 销售排行数据
const salesRanking = ref([
  { name: '华为手机', value: '¥2,340万', percentage: 95, color: '#00D4FF', highlight: true },
  { name: '苹果耳机', value: '¥1,890万', percentage: 78, color: '#4ECDC4' },
  { name: '小米电视', value: '¥1,560万', percentage: 65, color: '#00FF88' },
  { name: '联想笔记本', value: '¥1,200万', percentage: 50, color: '#FFEAA7' },
  { name: '戴尔显示器', value: '¥980万', percentage: 41, color: '#DDA0DD' },
  { name: '罗技鼠标', value: '¥720万', percentage: 30, color: '#FF6B6B' }
]);

// 平台状态数据
const platformStatus = ref([
  { name: '天猫平台', value: '98.5%', color: '#00FF88' },
  { name: '京东平台', value: '96.2%', color: '#00D4FF' },
  { name: '拼多多', value: '94.8%', color: '#4ECDC4' },
  { name: '抖音电商', value: '92.1%', color: '#FFEAA7' }
]);

// 月度统计数据
const monthlyStatsData = ref([
  { month: '1月', orders: 1200, revenue: 2400, efficiency: 85 },
  { month: '2月', orders: 1100, revenue: 2200, efficiency: 82 },
  { month: '3月', orders: 1350, revenue: 2700, efficiency: 88 },
  { month: '4月', orders: 1450, revenue: 2900, efficiency: 90 },
  { month: '5月', orders: 1600, revenue: 3200, efficiency: 92 },
  { month: '6月', orders: 1750, revenue: 3500, efficiency: 94 },
  { month: '7月', orders: 1900, revenue: 3800, efficiency: 96 },
  { month: '8月', orders: 2100, revenue: 4200, efficiency: 98 },
  { month: '9月', orders: 2250, revenue: 4500, efficiency: 97 },
  { month: '10月', orders: 2400, revenue: 4800, efficiency: 99 },
  { month: '11月', orders: 2300, revenue: 4600, efficiency: 98 },
  { month: '12月', orders: 2500, revenue: 5000, efficiency: 100 }
]);

// 趋势图表数据
const trendChartData = ref({
  xAxisData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
  seriesData: [12580, 15200, 18900, 16800, 21500, 23400, 25600]
});

// 成本分析数据
const laborCostData = ref([
  { name: '人力成本', value: 45, color: '#1E90FF' },
  { name: '运输成本', value: 30, color: '#00CED1' },
  { name: '仓储成本', value: 15, color: '#32CD32' },
  { name: '设备成本', value: 10, color: '#FFD700' }
]);

// 实时订单列表数据
const orderListData = ref([
  { orderId: 'SH20240719001', origin: '上海仓', destination: '北京分拨中心', status: '运输中' },
  { orderId: 'BJ20240719002', origin: '北京仓', destination: '广州分拨中心', status: '已发货' },
  { orderId: 'GZ20240719003', origin: '广州仓', destination: '深圳配送站', status: '配送中' },
  { orderId: 'SZ20240719004', origin: '深圳仓', destination: '上海分拨中心', status: '运输中' },
  { orderId: 'WH20240719005', origin: '武汉仓', destination: '成都分拨中心', status: '已发货' },
  { orderId: 'CD20240719006', origin: '成都仓', destination: '西安配送站', status: '配送中' },
  { orderId: 'XA20240719007', origin: '西安仓', destination: '武汉分拨中心', status: '运输中' },
  { orderId: 'HZ20240719008', origin: '杭州仓', destination: '上海配送站', status: '已送达' }
]);

// 物流地图数据
const logisticsMapData = ref({
  warehouses: [
    { name: '北京', value: [116.40, 39.90, 150] },
    { name: '上海', value: [121.47, 31.23, 200] },
    { name: '广州', value: [113.23, 23.16, 180] },
    { name: '深圳', value: [114.07, 22.62, 220] },
    { name: '武汉', value: [114.31, 30.52, 120] },
    { name: '成都', value: [104.06, 30.67, 160] },
    { name: '西安', value: [108.95, 34.27, 100] },
    { name: '杭州', value: [120.19, 30.26, 140] }
  ],
  routes: [
    { coords: [[116.40, 39.90], [121.47, 31.23]] }, // 北京-上海
    { coords: [[121.47, 31.23], [113.23, 23.16]] }, // 上海-广州
    { coords: [[113.23, 23.16], [114.07, 22.62]] }, // 广州-深圳
    { coords: [[114.31, 30.52], [104.06, 30.67]] }, // 武汉-成都
    { coords: [[104.06, 30.67], [108.95, 34.27]] }, // 成都-西安
    { coords: [[108.95, 34.27], [114.31, 30.52]] }  // 西安-武汉
  ]
});

// 计算属性
const totalBusinessVolume = computed(() => {
  return logisticsData.value.reduce((sum, item) => sum + item.orders, 0);
});

const networkCoverage = computed(() => {
  return Math.round((logisticsMapData.value.warehouses.length / 34) * 100); // 假设全国有34个主要城市
});

const averageEfficiency = computed(() => {
  const total = logisticsData.value.reduce((sum, item) => sum + item.efficiency, 0);
  return total / logisticsData.value.length;
});

const activeWarehouses = computed(() => {
  return logisticsData.value.filter(item => item.efficiency > 95).length;
});

// 获取后端数据
const fetchData = async () => {
  try {
    const response = await axios.get('http://localhost:8000/api/v1/metrics/all');
    apiData.value = response.data;
    console.log('获取到数据:', response.data);
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// --- ECharts 容器引用 ---
const chart2Container = ref(null);
const chart4Container = ref(null);
const chart5Container = ref(null);
const mapContainer = ref(null);

// --- 工具函数 ---
// 切换测试模式
const toggleTestMode = () => {
  isTestMode.value = !isTestMode.value;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
  isDynamicDashboardTestMode.value = false;
};

// 切换 KPI 测试模式
const toggleKpiTestMode = () => {
  isKpiTestMode.value = !isKpiTestMode.value;
  isTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换大屏测试模式
const toggleDashboardTestMode = () => {
  isDashboardTestMode.value = !isDashboardTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换人力成本测试模式
const toggleLaborCostTestMode = () => {
  isLaborCostTestMode.value = !isLaborCostTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换月度订单测试模式
const toggleMonthlyOrdersTestMode = () => {
  isMonthlyOrdersTestMode.value = !isMonthlyOrdersTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换物流地图测试模式
const toggleLogisticsMapTestMode = () => {
  isLogisticsMapTestMode.value = !isLogisticsMapTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换实时订单列表测试模式
const toggleRealTimeOrderListTestMode = () => {
  isRealTimeOrderListTestMode.value = !isRealTimeOrderListTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换交叉筛选测试模式
const toggleCrossFilterTestMode = () => {
  isCrossFilterTestMode.value = !isCrossFilterTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换动态仪表盘模式
const toggleDynamicDashboardMode = () => {
  isDynamicDashboardMode.value = !isDynamicDashboardMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardTestMode.value = false;
};

// 切换动态仪表盘测试模式
const toggleDynamicDashboardTestMode = () => {
  isDynamicDashboardTestMode.value = !isDynamicDashboardTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// --- 日期时间更新函数 ---
function updateDateTime() {
  const now = new Date();

  // 格式化日期
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  currentDate.value = `${year}-${month}-${day}`;

  // 格式化时间
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  currentTime.value = `${hours}:${minutes}:${seconds}`;

  // 格式化星期
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  currentWeekday.value = weekdays[now.getDay()];
}

// --- 响应式布局适配（移动端优先，固定断点） ---
function setResponsiveFontSize() {
    const docEl = document.documentElement;
    const clientWidth = docEl.clientWidth;
    if (!clientWidth) return;

    // 使用固定断点而不是无限制的vw单位
    if (clientWidth < 640) {
        // 手机端
        docEl.style.fontSize = '14px';
    } else if (clientWidth < 768) {
        // 大手机端
        docEl.style.fontSize = '15px';
    } else if (clientWidth < 1024) {
        // 平板端
        docEl.style.fontSize = '16px';
    } else if (clientWidth < 1280) {
        // 小桌面端
        docEl.style.fontSize = '17px';
    } else {
        // 大桌面端
        docEl.style.fontSize = '18px';
    }
}


// --- 生命周期钩子 ---
// 定时器引用
const timeInterval = ref<number | null>(null);

onMounted(() => {
  // 获取后端数据
  fetchData();

  // 初始化日期时间
  updateDateTime();
  // 每秒更新时间
  timeInterval.value = setInterval(updateDateTime, 1000);

  // 初始化响应式字体大小
  setResponsiveFontSize();
  // 监听窗口变化，重新适配
  window.addEventListener('resize', setResponsiveFontSize);

  // 在这里，我们将稍后初始化所有的 ECharts 实例
  console.log('Component Mounted. Chart containers are ready.');
  console.log('Map Container:', mapContainer.value);
});

onUnmounted(() => {
  // 组件卸载时清除事件监听
  window.removeEventListener('resize', setResponsiveFontSize);

  // 清理定时器
  if (timeInterval.value) {
    clearInterval(timeInterval.value);
  }
});
</script>

<style scoped>
/* 全屏应用容器 */
.dashboard-app-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
}

/* 全局背景和基础样式 */
.logistics-dashboard-bg {
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0f1419 100%);
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  color: #ffffff;
}

.logistics-dashboard-bg::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 255, 150, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 0, 150, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* Header 样式 */
.dashboard-header {
  position: relative;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: linear-gradient(90deg, rgba(6, 22, 74, 0.9) 0%, rgba(14, 38, 92, 0.8) 100%);
  border-bottom: 2px solid rgba(0, 150, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 150, 255, 0.2);
  flex-shrink: 0;
}

.header-datetime {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-text {
  font-size: 1rem;
  color: #7BDEFF;
  margin: 0;
  font-weight: 500;
}

.time-text {
  font-size: 0.9rem;
  color: #A0D8EF;
  margin: 0;
  font-weight: 400;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
  margin: 0;
  text-shadow: 0 0 20px rgba(0, 150, 255, 0.6);
}

.title-glow {
  color: #00D4FF;
  text-shadow:
    0 0 10px rgba(0, 212, 255, 0.8),
    0 0 20px rgba(0, 212, 255, 0.6),
    0 0 30px rgba(0, 212, 255, 0.4);
}

.title-accent {
  color: #7BDEFF;
  margin-left: 10px;
}

/* 导航按钮 */
.nav-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-end;
}

.nav-btn {
  padding: 8px 16px;
  border: 1px solid;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  white-space: nowrap;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
  font-weight: 500;
}

.nav-btn-primary {
  border-color: #9C27B0;
  color: #E1BEE7;
  box-shadow: 0 0 10px rgba(156, 39, 176, 0.3);
}

.nav-btn-primary:hover {
  background: rgba(156, 39, 176, 0.2);
  box-shadow: 0 0 15px rgba(156, 39, 176, 0.5);
  transform: translateY(-2px);
}

.nav-btn-secondary {
  border-color: #FF9800;
  color: #FFE0B2;
  box-shadow: 0 0 10px rgba(255, 152, 0, 0.3);
}

.nav-btn-secondary:hover {
  background: rgba(255, 152, 0, 0.2);
  box-shadow: 0 0 15px rgba(255, 152, 0, 0.5);
  transform: translateY(-2px);
}

.nav-btn-accent {
  border-color: #00D4FF;
  color: #B3E5FC;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.nav-btn-accent:hover {
  background: rgba(0, 212, 255, 0.2);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.5);
  transform: translateY(-2px);
}

/* 主内容区域 */
.dashboard-main {
  position: relative;
  z-index: 5;
  height: calc(100vh - 100px);
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

/* 顶部KPI卡片区域 */
.top-kpi-section {
  padding: 15px 20px;
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.3) 0%, rgba(14, 38, 92, 0.2) 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.kpi-cards-container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  max-width: 1400px;
  margin: 0 auto;
}

@media (max-width: 1200px) {
  .kpi-cards-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .kpi-cards-container {
    grid-template-columns: 1fr;
  }
}

/* 底部图表区域 */
.bottom-charts-section {
  padding: 15px 20px;
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.2) 0%, rgba(14, 38, 92, 0.1) 100%);
  border-top: 1px solid rgba(0, 212, 255, 0.3);
}

.bottom-charts-container {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.bottom-chart-panel {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.8) 0%, rgba(14, 38, 92, 0.6) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 12px;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.2);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 20px;
}

.stat-item {
  text-align: center;
  padding: 15px;
  background: rgba(0, 212, 255, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

.stat-label {
  font-size: 0.9rem;
  color: #7BDEFF;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #00FF88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.stat-unit {
  font-size: 0.8rem;
  color: #B3E5FC;
  margin-top: 4px;
}

@media (max-width: 1024px) {
  .bottom-charts-container {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

.main-content {
  flex: 0 0 auto;
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  padding: 20px;
  min-height: 500px;
  max-height: 70vh;
}

.column-left, .column-center, .column-right {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  overflow: hidden;
}

/* 数据面板样式 */
.data-panel {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.8) 0%, rgba(14, 38, 92, 0.6) 100%);
  border: 1px solid rgba(0, 212, 255, 0.4);
  border-radius: 12px;
  box-shadow:
    0 0 20px rgba(0, 212, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 1;
  position: relative;
  min-height: 180px;
  max-height: 300px;
}

.data-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.6), transparent);
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 150, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.panel-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #7BDEFF;
  text-shadow: 0 0 10px rgba(123, 222, 255, 0.5);
}

.panel-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00FF88;
  box-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.panel-content {
  flex: 1;
  padding: 10px;
  position: relative;
  overflow: hidden;
}

/* 中央地图面板 */
.central-map-panel {
  background: linear-gradient(135deg, rgba(6, 22, 74, 0.9) 0%, rgba(14, 38, 92, 0.7) 100%);
  border: 2px solid rgba(0, 212, 255, 0.5);
  border-radius: 15px;
  box-shadow:
    0 0 30px rgba(0, 212, 255, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex: 2;
  position: relative;
}

.central-map-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.8), transparent);
}

.map-stats-header {
  display: flex;
  justify-content: space-around;
  padding: 20px;
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, rgba(0, 150, 255, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.stat-display {
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-label {
  font-size: 1rem;
  color: #7BDEFF;
  font-weight: 500;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #00FF88;
  text-shadow: 0 0 15px rgba(0, 255, 136, 0.6);
}

.stat-positive {
  color: #00FF88;
}

.map-container {
  flex: 1;
  width: 100%;
  position: relative;
  background: radial-gradient(circle at center, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
}

/* 科技面板特殊样式 */
.tech-panel-wrapper {
  flex: 2 !important;
}

.tech-panel-wrapper .panel-content {
  padding: 10px;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
    gap: 15px;
  }

  .column-left, .column-center, .column-right {
    gap: 10px;
  }

  .central-map-panel {
    min-height: 400px;
  }

  .tech-panel-wrapper {
    flex: 1 !important;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px 20px;
  }

  .nav-buttons {
    justify-content: center;
  }

  .nav-btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }

  .dashboard-title {
    font-size: 2rem;
  }

  .main-content {
    padding: 15px;
    gap: 10px;
  }
}
</style>
