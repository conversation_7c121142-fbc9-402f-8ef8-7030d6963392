<template>
  <div class="min-h-screen bg-slate-100">
    <!-- Header Section -->
    <header class="flex justify-between items-center p-4 bg-white shadow-md mb-6" v-if="!isTestMode && !isKpiTestMode && !isDashboardTestMode && !isLaborCostTestMode && !isMonthlyOrdersTestMode && !isLogisticsMapTestMode && !isRealTimeOrderListTestMode && !isCrossFilterTestMode && !isDynamicDashboardMode && !isDynamicDashboardTestMode">
      <!-- Left: Date and Time -->
      <div class="text-slate-500">
        <p>{{ currentDate }}</p>
        <p>{{ currentTime }} {{ currentWeekday }}</p>
      </div>

      <!-- Center: Main Title -->
      <h1 class="text-2xl lg:text-3xl font-bold text-slate-800">
        物流大数据展示平台
      </h1>

      <!-- Right: Navigation Buttons -->
      <div class="flex flex-wrap gap-2 justify-end">
        <button @click="toggleDynamicDashboardMode" class="bg-purple-500 text-white px-4 py-2 rounded-md hover:bg-purple-600 transition">动态仪表盘</button>
        <button @click="toggleDynamicDashboardTestMode" class="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600 transition">仪表盘测试</button>
        <button @click="toggleTestMode" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition">组件测试</button>
        <button @click="toggleKpiTestMode" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition">KPI卡片</button>
        <button @click="toggleDashboardTestMode" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition">大屏布局</button>
        <button @click="toggleLaborCostTestMode" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition">成本图表</button>
        <button @click="toggleMonthlyOrdersTestMode" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition">趋势图表</button>
        <button @click="toggleLogisticsMapTestMode" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition">物流地图</button>
        <button @click="toggleRealTimeOrderListTestMode" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition">订单列表</button>
        <button @click="toggleCrossFilterTestMode" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 transition">交叉筛选</button>
      </div>
    </header>

    <!-- 动态仪表盘页面 -->
    <DashboardView v-if="isDynamicDashboardMode" />

    <!-- 动态仪表盘测试页面 -->
    <DynamicDashboardTest v-if="isDynamicDashboardTestMode" />

    <!-- 交叉筛选测试页面 -->
    <CrossFilterTest v-if="isCrossFilterTestMode" />

    <!-- 实时订单列表测试页面 -->
    <RealTimeOrderListTest v-if="isRealTimeOrderListTestMode" />

    <!-- 物流地图测试页面 -->
    <LogisticsMapTest v-if="isLogisticsMapTestMode" />

    <!-- 月度订单图表测试页面 -->
    <MonthlyOrdersTest v-if="isMonthlyOrdersTestMode" />

    <!-- 人力成本图表测试页面 -->
    <LaborCostTest v-if="isLaborCostTestMode" />

    <!-- 大屏布局测试页面 -->
    <DashboardTest v-if="isDashboardTestMode" />

    <!-- KPI 卡片测试页面 -->
    <KpiCardTest v-if="isKpiTestMode" />

    <!-- 测试组件页面 -->
    <TestComponents v-if="isTestMode" />

    <!-- 原始应用 -->
    <div v-else-if="!isTestMode && !isKpiTestMode && !isDashboardTestMode && !isLaborCostTestMode && !isMonthlyOrdersTestMode && !isLogisticsMapTestMode && !isRealTimeOrderListTestMode && !isCrossFilterTestMode">
      <!-- 顶部 Header -->
      <AppHeader :title="headerTitle" />

    <!-- 主内容区 (三列布局) -->
    <main class="main-content">
      <!-- 左侧列 -->
      <section class="column-left">
        <div class="chart-card">
          <div class="card-title">基本信息</div>
          <div class="card-content">
            <!-- ECharts 图表或数据将放在这里 -->
          </div>
        </div>
        <div class="chart-card">
          <div class="card-title">包裹量排名</div>
          <div class="card-content">
            <!-- ECharts 图表或数据将放在这里 -->
          </div>
        </div>
        <PieChart
          :data="categoryData"
          chart-id="category-pie-chart"
          title="商品分类占比"
        />

        <!-- 新增组件测试 -->
        <OverallKpiCard
          title="核心运营指标"
          :data="apiData"
        />

        <InventoryHealthPanel
          title="库存健康度"
          :data="{ turnover_rate: 2.3, accuracy_rate: 96.8 }"
        />
      </section>

      <!-- 中间列 -->
      <section class="column-center">
        <div class="map-card">
           <div class="top-stats">
              <div class="stat-item">
                <p>数量统计</p>
                <span>365个</span>
              </div>
              <div class="stat-item">
                <p>同比上升</p>
                <span>13%</span>
              </div>
           </div>
           <div class="map-container" ref="mapContainer" style="width: 100%; height: calc(100% - 70px);">
             <!-- 地图将放在这里 -->
           </div>
        </div>
        <div class="chart-card">
          <div class="card-title">月统计</div>
          <div class="card-content" ref="chart4Container" style="width: 100%; height: 100%;"></div>
        </div>
      </section>

      <!-- 右侧列 -->
      <section class="column-right">
        <div class="chart-card">
          <div class="card-title">商品销售排行</div>
          <div class="card-content">
            <!-- 滚动列表将放在这里 -->
          </div>
        </div>
        <div class="chart-card">
          <div class="card-title">各平台占比</div>
          <div class="card-content" ref="chart2Container" style="width: 100%; height: 100%;"></div>
        </div>
        <div class="chart-card">
          <div class="card-title">全球贸易国家城市排行</div>
          <div class="card-content" ref="chart5Container" style="width: 100%; height: 100%;"></div>
        </div>

        <!-- 新增组件测试区域 -->
        <MonthlyKpiScoreboard
          title="月度绩效看板"
          :data="[
            { name: 'TOC人效', actual: 85, target: 90, unit: '单/人日' },
            { name: 'TOB人效', actual: 120, target: 110, unit: '单/人日' }
          ]"
        />

        <ServiceQualityTracker
          title="服务质量跟踪"
          chart-id="service-chart"
          :data="apiData"
        />
      </section>
    </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import AppHeader from './components/AppHeader.vue';
import OverallKpiCard from './components/OverallKpiCard.vue';
import InventoryHealthPanel from './components/InventoryHealthPanel.vue';
import MonthlyKpiScoreboard from './components/MonthlyKpiScoreboard.vue';
import ServiceQualityTracker from './components/ServiceQualityTracker.vue';
import PieChart from './components/PieChart.vue';
import TestComponents from './TestComponents.vue';
import KpiCardTest from './KpiCardTest.vue';
import DashboardTest from './DashboardTest.vue';
import LaborCostTest from './LaborCostTest.vue';
import MonthlyOrdersTest from './MonthlyOrdersTest.vue';
import LogisticsMapTest from './LogisticsMapTest.vue';
import RealTimeOrderListTest from './RealTimeOrderListTest.vue';
import CrossFilterTest from './CrossFilterTest.vue';
import DashboardView from './views/DashboardView.vue';
import DynamicDashboardTest from './DynamicDashboardTest.vue';

// --- 响应式数据 ---
// 测试模式
const isTestMode = ref(false);
const isKpiTestMode = ref(false);
const isDashboardTestMode = ref(false);
const isLaborCostTestMode = ref(false);
const isMonthlyOrdersTestMode = ref(false);
const isLogisticsMapTestMode = ref(false);
const isRealTimeOrderListTestMode = ref(false);
const isCrossFilterTestMode = ref(false);
const isDynamicDashboardMode = ref(false);
const isDynamicDashboardTestMode = ref(false);

// Header 标题
const headerTitle = ref('物流大数据展示平台');

// 日期时间相关
const currentDate = ref('');
const currentTime = ref('');
const currentWeekday = ref('');

// 后端数据
const apiData = ref([]);

// 商品分类数据
const categoryData = ref([
  { value: 35, name: '电子产品' },
  { value: 25, name: '服装鞋帽' },
  { value: 20, name: '食品饮料' },
  { value: 12, name: '家居用品' },
  { value: 8, name: '其他' }
]);

// 获取后端数据
const fetchData = async () => {
  try {
    const response = await axios.get('http://localhost:8000/api/v1/metrics/all');
    apiData.value = response.data;
    console.log('获取到数据:', response.data);
  } catch (error) {
    console.error('获取数据失败:', error);
  }
};

// --- ECharts 容器引用 ---
const chart2Container = ref(null);
const chart4Container = ref(null);
const chart5Container = ref(null);
const mapContainer = ref(null);

// --- 工具函数 ---
// 切换测试模式
const toggleTestMode = () => {
  isTestMode.value = !isTestMode.value;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
  isDynamicDashboardTestMode.value = false;
};

// 切换 KPI 测试模式
const toggleKpiTestMode = () => {
  isKpiTestMode.value = !isKpiTestMode.value;
  isTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换大屏测试模式
const toggleDashboardTestMode = () => {
  isDashboardTestMode.value = !isDashboardTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换人力成本测试模式
const toggleLaborCostTestMode = () => {
  isLaborCostTestMode.value = !isLaborCostTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换月度订单测试模式
const toggleMonthlyOrdersTestMode = () => {
  isMonthlyOrdersTestMode.value = !isMonthlyOrdersTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换物流地图测试模式
const toggleLogisticsMapTestMode = () => {
  isLogisticsMapTestMode.value = !isLogisticsMapTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换实时订单列表测试模式
const toggleRealTimeOrderListTestMode = () => {
  isRealTimeOrderListTestMode.value = !isRealTimeOrderListTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换交叉筛选测试模式
const toggleCrossFilterTestMode = () => {
  isCrossFilterTestMode.value = !isCrossFilterTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// 切换动态仪表盘模式
const toggleDynamicDashboardMode = () => {
  isDynamicDashboardMode.value = !isDynamicDashboardMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardTestMode.value = false;
};

// 切换动态仪表盘测试模式
const toggleDynamicDashboardTestMode = () => {
  isDynamicDashboardTestMode.value = !isDynamicDashboardTestMode.value;
  isTestMode.value = false;
  isKpiTestMode.value = false;
  isDashboardTestMode.value = false;
  isLaborCostTestMode.value = false;
  isMonthlyOrdersTestMode.value = false;
  isLogisticsMapTestMode.value = false;
  isRealTimeOrderListTestMode.value = false;
  isCrossFilterTestMode.value = false;
  isDynamicDashboardMode.value = false;
};

// --- 日期时间更新函数 ---
function updateDateTime() {
  const now = new Date();

  // 格式化日期
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  currentDate.value = `${year}-${month}-${day}`;

  // 格式化时间
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  currentTime.value = `${hours}:${minutes}:${seconds}`;

  // 格式化星期
  const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  currentWeekday.value = weekdays[now.getDay()];
}

// --- 响应式布局适配（移动端优先，固定断点） ---
function setResponsiveFontSize() {
    const docEl = document.documentElement;
    const clientWidth = docEl.clientWidth;
    if (!clientWidth) return;

    // 使用固定断点而不是无限制的vw单位
    if (clientWidth < 640) {
        // 手机端
        docEl.style.fontSize = '14px';
    } else if (clientWidth < 768) {
        // 大手机端
        docEl.style.fontSize = '15px';
    } else if (clientWidth < 1024) {
        // 平板端
        docEl.style.fontSize = '16px';
    } else if (clientWidth < 1280) {
        // 小桌面端
        docEl.style.fontSize = '17px';
    } else {
        // 大桌面端
        docEl.style.fontSize = '18px';
    }
}


// --- 生命周期钩子 ---
// 定时器引用
const timeInterval = ref<number | null>(null);

onMounted(() => {
  // 获取后端数据
  fetchData();

  // 初始化日期时间
  updateDateTime();
  // 每秒更新时间
  timeInterval.value = setInterval(updateDateTime, 1000);

  // 初始化响应式字体大小
  setResponsiveFontSize();
  // 监听窗口变化，重新适配
  window.addEventListener('resize', setResponsiveFontSize);

  // 在这里，我们将稍后初始化所有的 ECharts 实例
  console.log('Component Mounted. Chart containers are ready.');
  console.log('Map Container:', mapContainer.value);
});

onUnmounted(() => {
  // 组件卸载时清除事件监听
  window.removeEventListener('resize', setResponsiveFontSize);

  // 清理定时器
  if (timeInterval.value) {
    clearInterval(timeInterval.value);
  }
});
</script>

<style scoped>
/* 测试模式切换按钮 */
.test-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.test-btn {
  background: rgba(30, 144, 255, 0.8);
  color: #ffffff;
  border: 1px solid #1E90FF;
  border-radius: 6px;
  padding: 8px 16px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.test-btn:hover {
  background: rgba(30, 144, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(30, 144, 255, 0.3);
}

/* 全局和基础布局 */
.main-container {
  display: flex;
  flex-direction: column;
  width: 100vw;
  height: 100vh;
  background-color: #06164A; /* 深蓝色背景 */
  color: #ffffff;
  overflow: hidden; /* 防止出现滚动条 */
  font-family: Arial, sans-serif;
}



/* 主内容区 Grid 布局 */
.main-content {
  flex: 1; /* 占据剩余所有空间 */
  display: grid;
  grid-template-columns: 1fr 2fr 1fr; /* 三列布局，中间列是左右两列的两倍宽 */
  grid-template-rows: 100%; /* 行高占满 */
  gap: 20px; /* 列间距 */
  padding: 20px;
}

/* 列的通用样式 */
.column-left, .column-center, .column-right {
  display: flex;
  flex-direction: column;
  gap: 20px; /* 卡片之间的垂直间距 */
  height: 100%;
}

/* 通用卡片样式 */
.chart-card {
  background-color: rgba(14, 38, 92, 0.6); /* 半透明背景 */
  border: 1px solid #1E90FF; /* 蓝色边框 */
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset; /* 内部发光效果 */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.column-left .chart-card { flex: 1; } /* 左侧卡片等高 */
.column-right .chart-card { flex: 1; } /* 右侧卡片等高 */

.column-center .map-card { flex: 2; } /* 中间地图卡片占2/3高度 */
.column-center .chart-card { flex: 1; } /* 中间下方图表占1/3高度 */
.map-card {
    background-color: rgba(14, 38, 92, 0.6);
    border: 1px solid #1E90FF;
    border-radius: 8px;
    box-shadow: 0 0 15px rgba(30, 144, 255, 0.5) inset;
    padding: 20px;
    display: flex;
    flex-direction: column;
}
.top-stats {
    display: flex;
    justify-content: space-around;
    height: 70px;
}
.stat-item {
    text-align: center;
}
.stat-item p {
    font-size: 1rem;
    color: #7BDEFF;
    margin: 0 0 5px 0;
}
.stat-item span {
    font-size: 1.5rem;
    font-weight: bold;
}


/* 卡片标题 */
.card-title {
  font-size: 1.2rem;
  font-weight: bold;
  padding: 10px 20px;
  background-color: rgba(30, 144, 255, 0.2); /* 标题区域的背景色 */
  border-bottom: 1px solid #1E90FF;
}

/* 卡片内容区 */
.card-content {
  flex: 1; /* 占据卡片内剩余所有空间 */
  padding: 15px;
}
</style>
